<template>
  <text 
    :class="['icon', `icon-${name}`, customClass]" 
    :style="iconStyle"
    @click="handleClick"
  />
</template>

<script setup lang="ts">
interface Props {
  name: string          // 图标名称
  size?: number         // 图标大小 (rpx)
  color?: string        // 图标颜色
  customClass?: string  // 自定义样式类
}

interface Emits {
  (e: 'click'): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 32,
  color: '#333333',
  customClass: ''
})

const emit = defineEmits<Emits>()

// 计算图标样式
const iconStyle = computed(() => ({
  fontSize: props.size + 'rpx',
  color: props.color
}))

// 点击事件处理
const handleClick = () => {
  emit('click')
}
</script>

<style lang="scss" scoped>
.icon {
  font-family: 'iconfont';
  font-style: normal;
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
  
  // 基础图标定义 (使用Unicode字符)
  &.icon-home::before { content: '\e001'; }        // 首页 🏠
  &.icon-product::before { content: '\e002'; }     // 产品 📦
  &.icon-cart::before { content: '\e003'; }        // 购物车 🛒
  &.icon-order::before { content: '\e004'; }       // 订单 📋
  &.icon-user::before { content: '\e005'; }        // 用户 👤
  &.icon-search::before { content: '\e006'; }      // 搜索 🔍
  &.icon-share::before { content: '\e007'; }       // 分享 📤
  &.icon-star::before { content: '\e008'; }        // 收藏 ⭐
  &.icon-location::before { content: '\e009'; }    // 位置 📍
  &.icon-phone::before { content: '\e010'; }       // 电话 📞
  &.icon-wechat::before { content: '\e011'; }      // 微信 💬
  &.icon-money::before { content: '\e012'; }       // 金钱 💰
  &.icon-team::before { content: '\e013'; }        // 团队 👥
  &.icon-setting::before { content: '\e014'; }     // 设置 ⚙️
  &.icon-arrow-right::before { content: '\e015'; } // 右箭头 ➡️
  &.icon-arrow-left::before { content: '\e016'; }  // 左箭头 ⬅️
  &.icon-close::before { content: '\e017'; }       // 关闭 ❌
  &.icon-check::before { content: '\e018'; }       // 选中 ✅
  &.icon-plus::before { content: '\e019'; }        // 加号 ➕
  &.icon-minus::before { content: '\e020'; }       // 减号 ➖
  &.icon-edit::before { content: '\e021'; }        // 编辑 ✏️
  &.icon-delete::before { content: '\e022'; }      // 删除 🗑️
  &.icon-refresh::before { content: '\e023'; }     // 刷新 🔄
  &.icon-download::before { content: '\e024'; }    // 下载 ⬇️
  &.icon-upload::before { content: '\e025'; }      // 上传 ⬆️
  &.icon-camera::before { content: '\e026'; }      // 相机 📷
  &.icon-image::before { content: '\e027'; }       // 图片 🖼️
  &.icon-video::before { content: '\e028'; }       // 视频 🎥
  &.icon-audio::before { content: '\e029'; }       // 音频 🎵
  &.icon-file::before { content: '\e030'; }        // 文件 📄
  &.icon-folder::before { content: '\e031'; }      // 文件夹 📁
  &.icon-link::before { content: '\e032'; }        // 链接 🔗
  &.icon-lock::before { content: '\e033'; }        // 锁定 🔒
  &.icon-unlock::before { content: '\e034'; }      // 解锁 🔓
  &.icon-eye::before { content: '\e035'; }         // 查看 👁️
  &.icon-eye-close::before { content: '\e036'; }   // 隐藏 🙈
  &.icon-heart::before { content: '\e037'; }       // 喜欢 ❤️
  &.icon-heart-o::before { content: '\e038'; }     // 未喜欢 🤍
  &.icon-message::before { content: '\e039'; }     // 消息 💬
  &.icon-notification::before { content: '\e040'; } // 通知 🔔
  &.icon-calendar::before { content: '\e041'; }    // 日历 📅
  &.icon-clock::before { content: '\e042'; }       // 时钟 🕐
  &.icon-gift::before { content: '\e043'; }        // 礼品 🎁
  &.icon-coupon::before { content: '\e044'; }      // 优惠券 🎫
  &.icon-vip::before { content: '\e045'; }         // VIP 👑
  &.icon-diamond::before { content: '\e046'; }     // 钻石 💎
  &.icon-fire::before { content: '\e047'; }        // 热门 🔥
  &.icon-new::before { content: '\e048'; }         // 新品 ✨
  &.icon-hot::before { content: '\e049'; }         // 热销 🌟
  &.icon-sale::before { content: '\e050'; }        // 促销 🏷️
  
  // 状态图标
  &.icon-success::before { content: '\e051'; }     // 成功 ✅
  &.icon-error::before { content: '\e052'; }       // 错误 ❌
  &.icon-warning::before { content: '\e053'; }     // 警告 ⚠️
  &.icon-info::before { content: '\e054'; }        // 信息 ℹ️
  &.icon-loading::before { content: '\e055'; }     // 加载 ⏳
  
  // 方向图标
  &.icon-up::before { content: '\e056'; }          // 向上 ⬆️
  &.icon-down::before { content: '\e057'; }        // 向下 ⬇️
  &.icon-left::before { content: '\e058'; }        // 向左 ⬅️
  &.icon-right::before { content: '\e059'; }       // 向右 ➡️
  
  // 社交图标
  &.icon-qq::before { content: '\e060'; }          // QQ 🐧
  &.icon-weibo::before { content: '\e061'; }       // 微博 📱
  &.icon-alipay::before { content: '\e062'; }      // 支付宝 💳
  
  // 业务图标
  &.icon-distribution::before { content: '\e063'; } // 分销 🤝
  &.icon-commission::before { content: '\e064'; }   // 佣金 💵
  &.icon-promotion::before { content: '\e065'; }    // 推广 📢
  &.icon-invite::before { content: '\e066'; }       // 邀请 👋
  &.icon-qrcode::before { content: '\e067'; }       // 二维码 📱
  &.icon-barcode::before { content: '\e068'; }      // 条形码 📊
}

// 图标动画效果
.icon-loading {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// 图标大小预设
.icon-xs { font-size: 20rpx !important; }
.icon-sm { font-size: 24rpx !important; }
.icon-md { font-size: 32rpx !important; }
.icon-lg { font-size: 40rpx !important; }
.icon-xl { font-size: 48rpx !important; }

// 图标颜色预设
.icon-primary { color: #007AFF !important; }
.icon-success { color: #34C759 !important; }
.icon-warning { color: #FF9500 !important; }
.icon-danger { color: #FF3B30 !important; }
.icon-info { color: #5AC8FA !important; }
.icon-secondary { color: #8E8E93 !important; }
.icon-light { color: #F2F2F7 !important; }
.icon-dark { color: #1C1C1E !important; }
</style>
