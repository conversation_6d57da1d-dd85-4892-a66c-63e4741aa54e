/**
 * 订单状态管理
 * 管理订单列表、订单详情、订单操作等
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import http from '@/utils/request'
import { getCdnUrl } from '@/utils/config'
import { ORDER_STATUS, ORDER_STATUS_TEXT, PAYMENT_METHODS } from '@/utils/constants'

// 订单商品接口
export interface OrderItem {
  id: number
  product_id: number
  product_name: string
  product_image: string
  product_price: number
  spec_id?: number
  spec_name?: string
  spec_attrs?: Record<string, string>
  quantity: number
  total_price: number
}

// 收货地址接口
export interface Address {
  id: number
  name: string
  phone: string
  province: string
  city: string
  district: string
  detail: string
  is_default: boolean
}

// 订单接口
export interface Order {
  id: number
  order_no: string
  status: string
  total_amount: number
  discount_amount: number
  shipping_fee: number
  final_amount: number
  payment_method?: string
  payment_time?: string
  shipping_time?: string
  delivery_time?: string
  completion_time?: string
  address: Address
  items: OrderItem[]
  remark?: string
  created_at: string
  updated_at: string
}

export const useOrderStore = defineStore('order', () => {
  // 状态
  const orders = ref<Order[]>([])
  const currentOrder = ref<Order | null>(null)
  const isLoading = ref(false)
  const hasMore = ref(true)
  const currentPage = ref(1)
  const pageSize = ref(20)

  // 计算属性
  const ordersByStatus = computed(() => {
    return (status?: string) => {
      if (!status) return orders.value
      return orders.value.filter(order => order.status === status)
    }
  })

  const pendingOrders = computed(() => {
    return orders.value.filter(order => order.status === ORDER_STATUS.PENDING)
  })

  const paidOrders = computed(() => {
    return orders.value.filter(order => order.status === ORDER_STATUS.PAID)
  })

  const shippedOrders = computed(() => {
    return orders.value.filter(order => order.status === ORDER_STATUS.SHIPPED)
  })

  const completedOrders = computed(() => {
    return orders.value.filter(order => order.status === ORDER_STATUS.COMPLETED)
  })

  // 方法
  /**
   * 获取订单列表
   */
  const getOrders = async (params: {
    page?: number
    page_size?: number
    status?: string
  } = {}): Promise<boolean> => {
    try {
      isLoading.value = true
      
      const queryParams = {
        page: params.page || currentPage.value,
        page_size: params.page_size || pageSize.value,
        ...params
      }
      
      const response = await http.get('/orders/list', queryParams)
      
      if (response.success) {
        const { data, pagination } = response.data
        
        // 处理订单数据
        const processedOrders = data.map((order: any) => ({
          ...order,
          items: order.items.map((item: any) => ({
            ...item,
            product_image: getCdnUrl(item.product_image)
          }))
        }))
        
        if (queryParams.page === 1) {
          orders.value = processedOrders
        } else {
          orders.value.push(...processedOrders)
        }
        
        currentPage.value = pagination.current_page
        hasMore.value = pagination.current_page < pagination.total_pages
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('获取订单列表失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 获取订单详情
   */
  const getOrderDetail = async (id: number): Promise<boolean> => {
    try {
      const response = await http.get(`/orders/${id}`)
      
      if (response.success) {
        // 处理订单数据
        const order = {
          ...response.data,
          items: response.data.items.map((item: any) => ({
            ...item,
            product_image: getCdnUrl(item.product_image)
          }))
        }
        
        currentOrder.value = order
        return true
      }
      
      return false
    } catch (error) {
      console.error('获取订单详情失败:', error)
      return false
    }
  }

  /**
   * 创建订单
   */
  const createOrder = async (params: {
    cart_ids?: number[]
    address_id: number
    payment_method: string
    remark?: string
    coupon_id?: number
  }): Promise<Order | null> => {
    try {
      const response = await http.post('/orders/create', params)
      
      if (response.success) {
        const order = response.data
        
        // 添加到订单列表开头
        orders.value.unshift(order)
        
        return order
      }
      
      return null
    } catch (error) {
      console.error('创建订单失败:', error)
      return null
    }
  }

  /**
   * 立即购买创建订单
   */
  const buyNowCreateOrder = async (params: {
    product_id: number
    spec_id?: number
    quantity: number
    address_id: number
    payment_method: string
    remark?: string
  }): Promise<Order | null> => {
    try {
      const response = await http.post('/orders/buy-now', params)
      
      if (response.success) {
        const order = response.data
        orders.value.unshift(order)
        return order
      }
      
      return null
    } catch (error) {
      console.error('立即购买创建订单失败:', error)
      return null
    }
  }

  /**
   * 支付订单
   */
  const payOrder = async (orderId: number): Promise<boolean> => {
    try {
      const response = await http.post(`/orders/${orderId}/pay`)
      
      if (response.success) {
        const { payment_params } = response.data
        
        // 调用微信支付
        return new Promise((resolve) => {
          uni.requestPayment({
            ...payment_params,
            success: async () => {
              // 支付成功，更新订单状态
              await getOrderDetail(orderId)
              const order = orders.value.find(o => o.id === orderId)
              if (order) {
                order.status = ORDER_STATUS.PAID
                order.payment_time = new Date().toISOString()
              }
              resolve(true)
            },
            fail: () => {
              resolve(false)
            }
          })
        })
      }
      
      return false
    } catch (error) {
      console.error('支付订单失败:', error)
      return false
    }
  }

  /**
   * 取消订单
   */
  const cancelOrder = async (orderId: number, reason?: string): Promise<boolean> => {
    try {
      const response = await http.post(`/orders/${orderId}/cancel`, { reason })
      
      if (response.success) {
        // 更新本地订单状态
        const order = orders.value.find(o => o.id === orderId)
        if (order) {
          order.status = ORDER_STATUS.CANCELLED
        }
        
        if (currentOrder.value?.id === orderId) {
          currentOrder.value.status = ORDER_STATUS.CANCELLED
        }
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('取消订单失败:', error)
      return false
    }
  }

  /**
   * 确认收货
   */
  const confirmOrder = async (orderId: number): Promise<boolean> => {
    try {
      const response = await http.post(`/orders/${orderId}/confirm`)
      
      if (response.success) {
        // 更新本地订单状态
        const order = orders.value.find(o => o.id === orderId)
        if (order) {
          order.status = ORDER_STATUS.COMPLETED
          order.completion_time = new Date().toISOString()
        }
        
        if (currentOrder.value?.id === orderId) {
          currentOrder.value.status = ORDER_STATUS.COMPLETED
          currentOrder.value.completion_time = new Date().toISOString()
        }
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('确认收货失败:', error)
      return false
    }
  }

  /**
   * 申请退款
   */
  const requestRefund = async (orderId: number, reason: string): Promise<boolean> => {
    try {
      const response = await http.post(`/orders/${orderId}/refund`, { reason })
      
      if (response.success) {
        // 更新本地订单状态
        const order = orders.value.find(o => o.id === orderId)
        if (order) {
          order.status = ORDER_STATUS.REFUNDING
        }
        
        if (currentOrder.value?.id === orderId) {
          currentOrder.value.status = ORDER_STATUS.REFUNDING
        }
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('申请退款失败:', error)
      return false
    }
  }

  /**
   * 删除订单
   */
  const deleteOrder = async (orderId: number): Promise<boolean> => {
    try {
      const response = await http.delete(`/orders/${orderId}`)
      
      if (response.success) {
        // 从本地列表中移除
        orders.value = orders.value.filter(o => o.id !== orderId)
        
        if (currentOrder.value?.id === orderId) {
          currentOrder.value = null
        }
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('删除订单失败:', error)
      return false
    }
  }

  /**
   * 重置订单列表
   */
  const resetOrders = (): void => {
    orders.value = []
    currentPage.value = 1
    hasMore.value = true
  }

  /**
   * 加载更多订单
   */
  const loadMoreOrders = async (params: any = {}): Promise<boolean> => {
    if (!hasMore.value || isLoading.value) {
      return false
    }
    
    return getOrders({
      ...params,
      page: currentPage.value + 1
    })
  }

  return {
    // 状态
    orders,
    currentOrder,
    isLoading,
    hasMore,
    currentPage,
    pageSize,
    
    // 计算属性
    ordersByStatus,
    pendingOrders,
    paidOrders,
    shippedOrders,
    completedOrders,
    
    // 方法
    getOrders,
    getOrderDetail,
    createOrder,
    buyNowCreateOrder,
    payOrder,
    cancelOrder,
    confirmOrder,
    requestRefund,
    deleteOrder,
    resetOrders,
    loadMoreOrders
  }
})
