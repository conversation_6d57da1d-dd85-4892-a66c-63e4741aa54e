"""
智梦科技系统Django基础配置
创建时间：2025年7月24日
"""

import os
from pathlib import Path
from decouple import config

# 构建路径
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# 安全配置
SECRET_KEY = config('SECRET_KEY', default='django-insecure-development-key')
DEBUG = config('DEBUG', default=True, cast=bool)
ALLOWED_HOSTS = config('ALLOWED_HOSTS', default='localhost,127.0.0.1').split(',')

# 应用定义
DJANGO_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
]

THIRD_PARTY_APPS = [
    'rest_framework',
    'corsheaders',
    'drf_spectacular',
    'django_filters',
]

LOCAL_APPS = [
    # 应用将在创建后逐步添加
    'apps.core',
    'apps.users',
    'apps.products',
    'apps.orders',
    'apps.wechat',  # 微信集成应用
    'apps.distribution',  # 分销系统应用
]

INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

# 中间件配置
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',  # 静态文件
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.locale.LocaleMiddleware',  # 国际化
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'apps.core.middleware.ThemeMiddleware',  # 主题切换中间件
]

ROOT_URLCONF = 'zmkj.urls'

# 模板配置
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.i18n',  # 国际化
                'apps.core.context_processors.theme_context',  # 主题上下文
            ],
        },
    },
]

WSGI_APPLICATION = 'zmkj.wsgi.application'

# 数据库配置 - 支持PostgreSQL和MySQL切换
DATABASES = {
    'default': {
        'ENGINE': f'django.db.backends.{config("DATABASE_ENGINE", default="postgresql")}',
        'NAME': config('DATABASE_NAME', default='zmkj_system'),
        'USER': config('DATABASE_USER', default='zmkj_user'),
        'PASSWORD': config('DATABASE_PASSWORD', default='password'),
        'HOST': config('DATABASE_HOST', default='localhost'),
        'PORT': config('DATABASE_PORT', default='5432'),
        'OPTIONS': {
            'charset': 'utf8mb4',
        } if config("DATABASE_ENGINE", default="postgresql") == 'mysql' else {},
    }
}

# 缓存配置 - 基础配置，在具体环境配置中覆盖
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'zmkj-cache',
    }
}

# 国际化配置
LANGUAGE_CODE = 'zh-hans'  # 简体中文
TIME_ZONE = 'Asia/Shanghai'  # 中国时区
USE_I18N = True
USE_L10N = True
USE_TZ = True

# 支持的语言
LANGUAGES = [
    ('zh-hans', '简体中文'),
    ('en', 'English'),
]

# 本地化文件路径
LOCALE_PATHS = [
    BASE_DIR / 'locale',
]

# 静态文件配置
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# 媒体文件配置
MEDIA_URL = '/media/'
MEDIA_ROOT = config('MEDIA_ROOT', default=str(BASE_DIR / 'media'))

# 默认主键字段类型
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 密码验证
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Django REST Framework配置
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.TokenAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'apps.core.renderers.StandardResponseRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',  # 保留可浏览API用于调试
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
}

# API文档配置
SPECTACULAR_SETTINGS = {
    'TITLE': '智梦科技系统 API',
    'DESCRIPTION': '智梦科技企业级系统接口文档',
    'VERSION': '1.0.0',
    'SERVE_INCLUDE_SCHEMA': False,
    'COMPONENT_SPLIT_REQUEST': True,
    'SCHEMA_PATH_PREFIX': '/api/v1/',
}

# CORS配置
CORS_ALLOW_ALL_ORIGINS = True  # 开发环境允许所有源
CORS_ALLOWED_ORIGINS = [
    "http://localhost:8080",
    "http://127.0.0.1:8080",
    config('MAIN_DOMAIN', default='http://localhost:8000'),
]

# 微信配置
WECHAT_CONFIG = {
    'APP_ID': config('WECHAT_APP_ID', default=''),
    'APP_SECRET': config('WECHAT_APP_SECRET', default=''),
    'MCH_ID': config('WECHAT_MCH_ID', default=''),
    'MCH_KEY': config('WECHAT_MCH_KEY', default=''),
    'CERT_PATH': BASE_DIR / config('WECHAT_CERT_PATH', default='certs/wechat/apiclient_cert.pem'),
    'KEY_PATH': BASE_DIR / config('WECHAT_KEY_PATH', default='certs/wechat/apiclient_key.pem'),
}

# 微信公众号配置
WECHAT_OFFICIAL_CONFIG = {
    'APP_ID': config('WECHAT_OFFICIAL_APP_ID', default=''),
    'APP_SECRET': config('WECHAT_OFFICIAL_APP_SECRET', default=''),
    'TOKEN': config('WECHAT_OFFICIAL_TOKEN', default=''),
    'ENCODING_AES_KEY': config('WECHAT_OFFICIAL_ENCODING_AES_KEY', default=''),
}

# 企业微信配置
WEWORK_CONFIG = {
    'CORP_ID': config('WEWORK_CORP_ID', default=''),
    'CORP_SECRET': config('WEWORK_CORP_SECRET', default=''),
}

# 小程序配置
MINIPROGRAM_CONFIG = {
    'APP_ID': config('MINIPROGRAM_APP_ID', default=''),
    'APP_SECRET': config('MINIPROGRAM_APP_SECRET', default=''),
}

# 邮件配置
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = config('EMAIL_HOST', default='smtp.qq.com')
EMAIL_PORT = config('EMAIL_PORT', default=587, cast=int)
EMAIL_HOST_USER = config('EMAIL_HOST_USER', default='')
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD', default='')
EMAIL_USE_TLS = config('EMAIL_USE_TLS', default=True, cast=bool)
DEFAULT_FROM_EMAIL = EMAIL_HOST_USER

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '[{levelname}] {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '[{levelname}] {asctime} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / config('LOG_FILE', default='logs/django.log'),
            'maxBytes': 1024*1024*5,  # 5MB
            'backupCount': 5,
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': config('LOG_LEVEL', default='INFO'),
            'propagate': True,
        },
        'zmkj': {
            'handlers': ['file', 'console'],
            'level': config('LOG_LEVEL', default='INFO'),
            'propagate': True,
        },
    },
}

# 确保必要目录存在
os.makedirs(BASE_DIR / 'logs', exist_ok=True)
os.makedirs(BASE_DIR / 'locale', exist_ok=True)
os.makedirs(BASE_DIR / 'media', exist_ok=True)

# 会话配置
SESSION_COOKIE_AGE = 86400  # 24小时
SESSION_SAVE_EVERY_REQUEST = True

# 安全配置
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# 文件上传配置
FILE_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB

# 自定义用户模型
AUTH_USER_MODEL = 'users.User'