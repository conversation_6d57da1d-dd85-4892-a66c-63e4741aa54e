# -*- coding: utf-8 -*-
"""
智梦科技系统 - AI服务模块

AI服务的核心业务逻辑，包括：
- AI服务管理
- 任务调度
- 数据处理
- 结果分析

创建时间：2025年7月30日
维护人员：智梦科技开发团队
"""

import logging
from typing import Dict, List, Any, Optional
from django.conf import settings
from django.utils import timezone
from .models import AIServiceConfig, AITask, UserBehaviorLog

logger = logging.getLogger(__name__)


class AIServiceManager:
    """AI服务管理器"""
    
    def __init__(self):
        self.services = {}
        self._load_services()
    
    def _load_services(self):
        """加载AI服务配置"""
        try:
            configs = AIServiceConfig.objects.filter(is_active=True)
            for config in configs:
                self.services[config.service_type] = config
                logger.info(f"已加载AI服务: {config.name}")
        except Exception as e:
            logger.error(f"加载AI服务配置失败: {e}")
    
    def get_service(self, service_type: str) -> Optional[AIServiceConfig]:
        """
        获取AI服务配置
        
        Args:
            service_type: 服务类型
            
        Returns:
            AIServiceConfig: 服务配置对象
        """
        return self.services.get(service_type)
    
    def is_service_available(self, service_type: str) -> bool:
        """
        检查服务是否可用
        
        Args:
            service_type: 服务类型
            
        Returns:
            bool: 是否可用
        """
        service = self.get_service(service_type)
        return service is not None and service.is_active
    
    def reload_services(self):
        """重新加载服务配置"""
        self.services.clear()
        self._load_services()


class AITaskManager:
    """AI任务管理器"""
    
    @staticmethod
    def create_task(name: str, task_type: str, parameters: Dict[str, Any] = None) -> AITask:
        """
        创建AI任务
        
        Args:
            name: 任务名称
            task_type: 任务类型
            parameters: 任务参数
            
        Returns:
            AITask: 任务对象
        """
        task = AITask.objects.create(
            name=name,
            task_type=task_type,
            parameters=parameters or {},
            status='pending'
        )
        logger.info(f"创建AI任务: {task.name} (ID: {task.id})")
        return task
    
    @staticmethod
    def start_task(task_id: int) -> bool:
        """
        启动任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功启动
        """
        try:
            task = AITask.objects.get(id=task_id)
            if task.status != 'pending':
                logger.warning(f"任务 {task_id} 状态不是待执行，无法启动")
                return False
            
            task.status = 'running'
            task.start_time = timezone.now()
            task.save()
            
            logger.info(f"启动AI任务: {task.name} (ID: {task.id})")
            return True
        except AITask.DoesNotExist:
            logger.error(f"任务 {task_id} 不存在")
            return False
        except Exception as e:
            logger.error(f"启动任务 {task_id} 失败: {e}")
            return False
    
    @staticmethod
    def complete_task(task_id: int, result_data: Dict[str, Any] = None) -> bool:
        """
        完成任务
        
        Args:
            task_id: 任务ID
            result_data: 结果数据
            
        Returns:
            bool: 是否成功完成
        """
        try:
            task = AITask.objects.get(id=task_id)
            task.status = 'completed'
            task.end_time = timezone.now()
            task.progress = 100
            if result_data:
                task.result_data = result_data
            task.save()
            
            logger.info(f"完成AI任务: {task.name} (ID: {task.id})")
            return True
        except AITask.DoesNotExist:
            logger.error(f"任务 {task_id} 不存在")
            return False
        except Exception as e:
            logger.error(f"完成任务 {task_id} 失败: {e}")
            return False
    
    @staticmethod
    def fail_task(task_id: int, error_message: str) -> bool:
        """
        标记任务失败
        
        Args:
            task_id: 任务ID
            error_message: 错误信息
            
        Returns:
            bool: 是否成功标记
        """
        try:
            task = AITask.objects.get(id=task_id)
            task.status = 'failed'
            task.end_time = timezone.now()
            task.error_message = error_message
            task.save()
            
            logger.error(f"AI任务失败: {task.name} (ID: {task.id}) - {error_message}")
            return True
        except AITask.DoesNotExist:
            logger.error(f"任务 {task_id} 不存在")
            return False
        except Exception as e:
            logger.error(f"标记任务 {task_id} 失败: {e}")
            return False
    
    @staticmethod
    def update_progress(task_id: int, progress: int) -> bool:
        """
        更新任务进度
        
        Args:
            task_id: 任务ID
            progress: 进度百分比
            
        Returns:
            bool: 是否成功更新
        """
        try:
            task = AITask.objects.get(id=task_id)
            task.progress = max(0, min(100, progress))
            task.save(update_fields=['progress', 'updated_at'])
            return True
        except AITask.DoesNotExist:
            logger.error(f"任务 {task_id} 不存在")
            return False
        except Exception as e:
            logger.error(f"更新任务 {task_id} 进度失败: {e}")
            return False


class BehaviorAnalyzer:
    """用户行为分析器"""
    
    @staticmethod
    def log_behavior(user_id: Optional[int], session_id: str, action_type: str,
                    target_type: str, target_id: str, context_data: Dict[str, Any] = None,
                    ip_address: str = None, user_agent: str = None) -> UserBehaviorLog:
        """
        记录用户行为
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            action_type: 行为类型
            target_type: 目标类型
            target_id: 目标ID
            context_data: 上下文数据
            ip_address: IP地址
            user_agent: 用户代理
            
        Returns:
            UserBehaviorLog: 行为日志对象
        """
        log = UserBehaviorLog.objects.create(
            user_id=user_id,
            session_id=session_id,
            action_type=action_type,
            target_type=target_type,
            target_id=target_id,
            context_data=context_data or {},
            ip_address=ip_address,
            user_agent=user_agent
        )
        return log
    
    @staticmethod
    def get_user_behavior_stats(user_id: int, days: int = 30) -> Dict[str, Any]:
        """
        获取用户行为统计
        
        Args:
            user_id: 用户ID
            days: 统计天数
            
        Returns:
            dict: 统计结果
        """
        from django.db.models import Count
        from datetime import timedelta
        
        start_date = timezone.now() - timedelta(days=days)
        
        behaviors = UserBehaviorLog.objects.filter(
            user_id=user_id,
            created_at__gte=start_date
        )
        
        # 按行为类型统计
        action_stats = behaviors.values('action_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # 按目标类型统计
        target_stats = behaviors.values('target_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        return {
            'total_behaviors': behaviors.count(),
            'action_stats': list(action_stats),
            'target_stats': list(target_stats),
            'period_days': days
        }
    
    @staticmethod
    def get_popular_targets(target_type: str, days: int = 7, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取热门目标
        
        Args:
            target_type: 目标类型
            days: 统计天数
            limit: 返回数量限制
            
        Returns:
            list: 热门目标列表
        """
        from django.db.models import Count
        from datetime import timedelta
        
        start_date = timezone.now() - timedelta(days=days)
        
        popular_targets = UserBehaviorLog.objects.filter(
            target_type=target_type,
            created_at__gte=start_date
        ).values('target_id').annotate(
            view_count=Count('id')
        ).order_by('-view_count')[:limit]
        
        return list(popular_targets)


# 全局AI服务管理器实例
ai_service_manager = AIServiceManager()
