from drf_spectacular.extensions import OpenApiAuthenticationExtension
from drf_spectacular.plumbing import build_bearer_security_scheme_object

class TokenAuthenticationExtension(OpenApiAuthenticationExtension):
    target_class = 'rest_framework.authentication.TokenAuthentication'
    name = 'tokenAuth'

    def get_security_definition(self, auto_schema):
        return build_bearer_security_scheme_object(
            header_name='Authorization',
            token_prefix='Token',
            bearer_format='Token {token}',
            description='使用Token进行API认证，格式: Token <your_token>',
        )

# 中文API文档配置
SPECTACULAR_SETTINGS = {
    'TITLE': '智梦科技系统 API',
    'DESCRIPTION': '智梦科技企业级系统接口文档，包含用户管理、权限控制等功能模块',
    'VERSION': '1.0.0',
    'SERVE_INCLUDE_SCHEMA': False,
    'COMPONENT_SPLIT_REQUEST': True,
    'SCHEMA_PATH_PREFIX': '/api/v1/',
    'LOCALE': 'zh-hans',  # 设置中文
    'POSTPROCESSING_HOOKS': [
        'drf_spectacular.hooks.postprocess_schema_enums',
        'drf_spectacular.hooks.postprocess_schema_examples',
    ],
    'ENUM_NAME_OVERRIDES': {
        # 枚举名称中文覆盖
    },
    'EXAMPLE_GENERATOR_CLASS': 'drf_spectacular.generators.SchemaExampleGenerator',
    'AUTHENTICATION_WHITELIST': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.TokenAuthentication',
    ],
}}