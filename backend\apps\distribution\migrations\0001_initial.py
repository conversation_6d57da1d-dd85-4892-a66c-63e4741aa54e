# Generated by Django 5.2.4 on 2025-07-27 07:28

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('orders', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DistributionConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('name', models.CharField(help_text='配置项名称', max_length=100, verbose_name='配置名称')),
                ('key', models.CharField(help_text='配置键名', max_length=50, unique=True, verbose_name='配置键')),
                ('value', models.TextField(help_text='配置项的值', verbose_name='配置值')),
                ('description', models.TextField(blank=True, help_text='配置项说明', verbose_name='配置说明')),
                ('is_active', models.BooleanField(default=True, help_text='是否启用此配置', verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '分销配置',
                'verbose_name_plural': '分销配置',
                'indexes': [models.Index(fields=['key'], name='distributio_key_41e827_idx'), models.Index(fields=['is_active'], name='distributio_is_acti_acdeb1_idx')],
            },
        ),
        migrations.CreateModel(
            name='DistributionLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('name', models.CharField(help_text='分销等级名称', max_length=50, verbose_name='等级名称')),
                ('level', models.PositiveIntegerField(help_text='等级数值，数值越大等级越高', unique=True, verbose_name='等级数值')),
                ('commission_rate', models.DecimalField(decimal_places=4, help_text='佣金比例，0-1之间的小数', max_digits=5, validators=[django.core.validators.MinValueValidator(Decimal('0')), django.core.validators.MaxValueValidator(Decimal('1'))], verbose_name='佣金比例')),
                ('min_sales', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='达到此等级所需的最低销售额', max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='最低销售额')),
                ('min_referrals', models.PositiveIntegerField(default=0, help_text='达到此等级所需的最低推荐人数', verbose_name='最低推荐人数')),
                ('description', models.TextField(blank=True, help_text='等级说明', verbose_name='等级描述')),
                ('is_active', models.BooleanField(default=True, help_text='是否启用此等级', verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '分销等级',
                'verbose_name_plural': '分销等级',
                'ordering': ['level'],
                'indexes': [models.Index(fields=['level'], name='distributio_level_518949_idx'), models.Index(fields=['is_active'], name='distributio_is_acti_28bedf_idx')],
            },
        ),
        migrations.CreateModel(
            name='DistributionRelation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('invitation_code', models.CharField(help_text='个人专属邀请码', max_length=20, unique=True, verbose_name='邀请码')),
                ('total_sales', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='累计销售金额', max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='累计销售额')),
                ('total_commission', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='累计获得佣金', max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='累计佣金')),
                ('available_commission', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='可提现的佣金余额', max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='可提现佣金')),
                ('referral_count', models.PositiveIntegerField(default=0, help_text='直接推荐的用户数量', verbose_name='推荐人数')),
                ('is_active', models.BooleanField(default=True, help_text='分销商是否激活', verbose_name='是否激活')),
                ('activated_at', models.DateTimeField(blank=True, help_text='分销商激活时间', null=True, verbose_name='激活时间')),
                ('level', models.ForeignKey(help_text='当前分销等级', on_delete=django.db.models.deletion.PROTECT, related_name='distributors', to='distribution.distributionlevel', verbose_name='分销等级')),
                ('parent', models.ForeignKey(blank=True, help_text='推荐人/上级分销商', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='children', to='distribution.distributionrelation', verbose_name='上级分销商')),
                ('user', models.OneToOneField(help_text='分销商用户', on_delete=django.db.models.deletion.CASCADE, related_name='distribution_relation', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '分销关系',
                'verbose_name_plural': '分销关系',
            },
        ),
        migrations.CreateModel(
            name='CommissionRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('commission_type', models.CharField(choices=[('direct', '直推佣金'), ('indirect', '间推佣金'), ('team', '团队佣金'), ('bonus', '奖励佣金'), ('activity', '活动佣金')], help_text='佣金类型', max_length=20, verbose_name='佣金类型')),
                ('commission_rate', models.DecimalField(decimal_places=4, help_text='计算佣金时使用的比例', max_digits=5, validators=[django.core.validators.MinValueValidator(Decimal('0')), django.core.validators.MaxValueValidator(Decimal('1'))], verbose_name='佣金比例')),
                ('order_amount', models.DecimalField(decimal_places=2, help_text='计算佣金的订单金额', max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='订单金额')),
                ('commission_amount', models.DecimalField(decimal_places=2, help_text='实际佣金金额', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='佣金金额')),
                ('status', models.CharField(choices=[('pending', '待结算'), ('confirmed', '已确认'), ('settled', '已结算'), ('cancelled', '已取消'), ('frozen', '已冻结')], default='pending', help_text='佣金状态', max_length=20, verbose_name='状态')),
                ('settled_at', models.DateTimeField(blank=True, help_text='佣金结算时间', null=True, verbose_name='结算时间')),
                ('remark', models.TextField(blank=True, help_text='佣金备注信息', verbose_name='备注')),
                ('order', models.ForeignKey(help_text='产生佣金的订单', on_delete=django.db.models.deletion.CASCADE, related_name='commission_records', to='orders.order', verbose_name='关联订单')),
                ('distributor', models.ForeignKey(help_text='获得佣金的分销商', on_delete=django.db.models.deletion.CASCADE, related_name='commission_records', to='distribution.distributionrelation', verbose_name='分销商')),
            ],
            options={
                'verbose_name': '佣金记录',
                'verbose_name_plural': '佣金记录',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MarketingActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('name', models.CharField(help_text='营销活动名称', max_length=200, verbose_name='活动名称')),
                ('activity_type', models.CharField(choices=[('discount', '折扣活动'), ('cashback', '返现活动'), ('commission_boost', '佣金加成'), ('referral_bonus', '推荐奖励'), ('group_buy', '团购活动')], help_text='营销活动类型', max_length=20, verbose_name='活动类型')),
                ('description', models.TextField(help_text='活动详细描述', verbose_name='活动描述')),
                ('rules', models.JSONField(help_text='活动规则配置（JSON格式）', verbose_name='活动规则')),
                ('start_time', models.DateTimeField(help_text='活动开始时间', verbose_name='开始时间')),
                ('end_time', models.DateTimeField(help_text='活动结束时间', verbose_name='结束时间')),
                ('target_users', models.JSONField(default=dict, help_text='目标用户条件（JSON格式）', verbose_name='目标用户')),
                ('budget', models.DecimalField(blank=True, decimal_places=2, help_text='活动预算金额', max_digits=12, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='活动预算')),
                ('used_budget', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='已使用的预算金额', max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='已用预算')),
                ('participant_count', models.PositiveIntegerField(default=0, help_text='活动参与人数', verbose_name='参与人数')),
                ('status', models.CharField(choices=[('draft', '草稿'), ('active', '进行中'), ('paused', '已暂停'), ('ended', '已结束'), ('cancelled', '已取消')], default='draft', help_text='活动状态', max_length=20, verbose_name='状态')),
                ('creator', models.ForeignKey(help_text='活动创建者', on_delete=django.db.models.deletion.PROTECT, related_name='created_activities', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
            ],
            options={
                'verbose_name': '营销活动',
                'verbose_name_plural': '营销活动',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PromotionCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('code', models.CharField(help_text='推广码内容', max_length=20, unique=True, verbose_name='推广码')),
                ('name', models.CharField(help_text='推广码显示名称', max_length=100, verbose_name='推广码名称')),
                ('code_type', models.CharField(choices=[('personal', '个人推广码'), ('activity', '活动推广码'), ('product', '产品推广码'), ('category', '分类推广码')], help_text='推广码类型', max_length=20, verbose_name='推广码类型')),
                ('target_id', models.PositiveIntegerField(blank=True, help_text='关联的产品或分类ID', null=True, verbose_name='目标ID')),
                ('commission_rate', models.DecimalField(blank=True, decimal_places=4, help_text='特殊佣金比例，为空则使用默认比例', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0')), django.core.validators.MaxValueValidator(Decimal('1'))], verbose_name='佣金比例')),
                ('usage_limit', models.PositiveIntegerField(blank=True, help_text='推广码使用次数限制，为空表示无限制', null=True, verbose_name='使用次数限制')),
                ('used_count', models.PositiveIntegerField(default=0, help_text='推广码已使用次数', verbose_name='已使用次数')),
                ('start_time', models.DateTimeField(blank=True, help_text='推广码生效时间', null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, help_text='推广码失效时间', null=True, verbose_name='结束时间')),
                ('status', models.CharField(choices=[('active', '有效'), ('inactive', '无效'), ('expired', '已过期'), ('disabled', '已禁用')], default='active', help_text='推广码状态', max_length=20, verbose_name='状态')),
                ('description', models.TextField(blank=True, help_text='推广码描述', verbose_name='描述')),
                ('distributor', models.ForeignKey(help_text='推广码所属分销商', on_delete=django.db.models.deletion.CASCADE, related_name='promotion_codes', to='distribution.distributionrelation', verbose_name='分销商')),
            ],
            options={
                'verbose_name': '推广码',
                'verbose_name_plural': '推广码',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WithdrawalRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('withdrawal_no', models.CharField(help_text='系统生成的提现单号', max_length=32, unique=True, verbose_name='提现单号')),
                ('amount', models.DecimalField(decimal_places=2, help_text='申请提现的金额', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='提现金额')),
                ('fee', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='提现手续费', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='手续费')),
                ('actual_amount', models.DecimalField(decimal_places=2, help_text='扣除手续费后的实际金额', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='实际到账金额')),
                ('method', models.CharField(choices=[('wechat', '微信零钱'), ('alipay', '支付宝'), ('bank', '银行卡')], help_text='提现方式', max_length=20, verbose_name='提现方式')),
                ('account_info', models.JSONField(help_text='提现账户信息（加密存储）', verbose_name='账户信息')),
                ('status', models.CharField(choices=[('pending', '待审核'), ('approved', '已审核'), ('processing', '处理中'), ('completed', '已完成'), ('rejected', '已拒绝'), ('cancelled', '已取消')], default='pending', help_text='提现状态', max_length=20, verbose_name='状态')),
                ('processed_at', models.DateTimeField(blank=True, help_text='提现处理时间', null=True, verbose_name='处理时间')),
                ('remark', models.TextField(blank=True, help_text='提现备注', verbose_name='备注')),
                ('admin_remark', models.TextField(blank=True, help_text='管理员处理备注', verbose_name='管理员备注')),
                ('distributor', models.ForeignKey(help_text='申请提现的分销商', on_delete=django.db.models.deletion.CASCADE, related_name='withdrawal_records', to='distribution.distributionrelation', verbose_name='分销商')),
            ],
            options={
                'verbose_name': '提现记录',
                'verbose_name_plural': '提现记录',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='distributionrelation',
            index=models.Index(fields=['user'], name='distributio_user_id_452f36_idx'),
        ),
        migrations.AddIndex(
            model_name='distributionrelation',
            index=models.Index(fields=['parent'], name='distributio_parent__839048_idx'),
        ),
        migrations.AddIndex(
            model_name='distributionrelation',
            index=models.Index(fields=['invitation_code'], name='distributio_invitat_4552e0_idx'),
        ),
        migrations.AddIndex(
            model_name='distributionrelation',
            index=models.Index(fields=['is_active'], name='distributio_is_acti_52c3ce_idx'),
        ),
        migrations.AddIndex(
            model_name='distributionrelation',
            index=models.Index(fields=['level'], name='distributio_level_i_fa491f_idx'),
        ),
        migrations.AddIndex(
            model_name='commissionrecord',
            index=models.Index(fields=['distributor', 'status'], name='distributio_distrib_c55331_idx'),
        ),
        migrations.AddIndex(
            model_name='commissionrecord',
            index=models.Index(fields=['order'], name='distributio_order_i_52113b_idx'),
        ),
        migrations.AddIndex(
            model_name='commissionrecord',
            index=models.Index(fields=['commission_type'], name='distributio_commiss_21212c_idx'),
        ),
        migrations.AddIndex(
            model_name='commissionrecord',
            index=models.Index(fields=['status'], name='distributio_status_383455_idx'),
        ),
        migrations.AddIndex(
            model_name='commissionrecord',
            index=models.Index(fields=['created_at'], name='distributio_created_490b32_idx'),
        ),
        migrations.AddIndex(
            model_name='marketingactivity',
            index=models.Index(fields=['activity_type'], name='distributio_activit_a9b73f_idx'),
        ),
        migrations.AddIndex(
            model_name='marketingactivity',
            index=models.Index(fields=['status'], name='distributio_status_1a5e2b_idx'),
        ),
        migrations.AddIndex(
            model_name='marketingactivity',
            index=models.Index(fields=['start_time', 'end_time'], name='distributio_start_t_139709_idx'),
        ),
        migrations.AddIndex(
            model_name='marketingactivity',
            index=models.Index(fields=['creator'], name='distributio_creator_91adb1_idx'),
        ),
        migrations.AddIndex(
            model_name='promotioncode',
            index=models.Index(fields=['distributor'], name='distributio_distrib_bb5559_idx'),
        ),
        migrations.AddIndex(
            model_name='promotioncode',
            index=models.Index(fields=['code'], name='distributio_code_6a84e8_idx'),
        ),
        migrations.AddIndex(
            model_name='promotioncode',
            index=models.Index(fields=['code_type'], name='distributio_code_ty_c4a845_idx'),
        ),
        migrations.AddIndex(
            model_name='promotioncode',
            index=models.Index(fields=['status'], name='distributio_status_7abf2e_idx'),
        ),
        migrations.AddIndex(
            model_name='withdrawalrecord',
            index=models.Index(fields=['distributor', 'status'], name='distributio_distrib_01d39f_idx'),
        ),
        migrations.AddIndex(
            model_name='withdrawalrecord',
            index=models.Index(fields=['withdrawal_no'], name='distributio_withdra_fe3d10_idx'),
        ),
        migrations.AddIndex(
            model_name='withdrawalrecord',
            index=models.Index(fields=['status'], name='distributio_status_fb23aa_idx'),
        ),
        migrations.AddIndex(
            model_name='withdrawalrecord',
            index=models.Index(fields=['created_at'], name='distributio_created_28796d_idx'),
        ),
    ]
