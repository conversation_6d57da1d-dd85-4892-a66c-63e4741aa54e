# -*- coding: utf-8 -*-
"""
智梦科技系统 - AI功能后台管理

AI功能模块的Django Admin配置
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    AIServiceConfig, ChatbotConversation, ChatbotMessage,
    RecommendationConfig, UserBehaviorLog, AITask
)


@admin.register(AIServiceConfig)
class AIServiceConfigAdmin(admin.ModelAdmin):
    """AI服务配置管理"""
    
    list_display = ['name', 'service_type', 'is_active', 'created_at']
    list_filter = ['service_type', 'is_active', 'created_at']
    search_fields = ['name', 'api_endpoint']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'service_type', 'is_active')
        }),
        ('API配置', {
            'fields': ('api_endpoint', 'api_key'),
            'classes': ('collapse',)
        }),
        ('配置数据', {
            'fields': ('config_data',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related()


class ChatbotMessageInline(admin.TabularInline):
    """智能客服消息内联"""
    
    model = ChatbotMessage
    extra = 0
    readonly_fields = ['created_at']
    fields = ['message_type', 'content', 'created_at']
    
    def has_add_permission(self, request, obj=None):
        return False


@admin.register(ChatbotConversation)
class ChatbotConversationAdmin(admin.ModelAdmin):
    """智能客服对话管理"""
    
    list_display = ['session_id', 'user', 'status', 'message_count', 'start_time', 'duration']
    list_filter = ['status', 'start_time']
    search_fields = ['session_id', 'user__username']
    readonly_fields = ['session_id', 'start_time', 'created_at', 'updated_at']
    inlines = [ChatbotMessageInline]
    
    fieldsets = (
        ('对话信息', {
            'fields': ('session_id', 'user', 'status')
        }),
        ('时间信息', {
            'fields': ('start_time', 'end_time', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def message_count(self, obj):
        """消息数量"""
        return obj.messages.count()
    message_count.short_description = '消息数量'
    
    def duration(self, obj):
        """对话时长"""
        if obj.start_time and obj.end_time:
            duration = obj.end_time - obj.start_time
            return str(duration).split('.')[0]  # 去掉微秒
        return '-'
    duration.short_description = '对话时长'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user').prefetch_related('messages')


@admin.register(ChatbotMessage)
class ChatbotMessageAdmin(admin.ModelAdmin):
    """智能客服消息管理"""
    
    list_display = ['conversation_link', 'message_type', 'content_preview', 'created_at']
    list_filter = ['message_type', 'created_at']
    search_fields = ['content', 'conversation__session_id']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('消息信息', {
            'fields': ('conversation', 'message_type', 'content')
        }),
        ('元数据', {
            'fields': ('metadata',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def conversation_link(self, obj):
        """对话链接"""
        url = reverse('admin:ai_chatbotconversation_change', args=[obj.conversation.id])
        return format_html('<a href="{}">{}</a>', url, obj.conversation.session_id)
    conversation_link.short_description = '对话会话'
    
    def content_preview(self, obj):
        """内容预览"""
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = '内容预览'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('conversation')


@admin.register(RecommendationConfig)
class RecommendationConfigAdmin(admin.ModelAdmin):
    """推荐算法配置管理"""
    
    list_display = ['name', 'algorithm_type', 'weight', 'is_active', 'created_at']
    list_filter = ['algorithm_type', 'is_active', 'created_at']
    search_fields = ['name']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'algorithm_type', 'weight', 'is_active')
        }),
        ('算法参数', {
            'fields': ('parameters',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(UserBehaviorLog)
class UserBehaviorLogAdmin(admin.ModelAdmin):
    """用户行为日志管理"""
    
    list_display = ['user', 'action_type', 'target_type', 'target_id', 'ip_address', 'created_at']
    list_filter = ['action_type', 'target_type', 'created_at']
    search_fields = ['user__username', 'target_id', 'ip_address']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('行为信息', {
            'fields': ('user', 'session_id', 'action_type', 'target_type', 'target_id')
        }),
        ('请求信息', {
            'fields': ('ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
        ('上下文数据', {
            'fields': ('context_data',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(AITask)
class AITaskAdmin(admin.ModelAdmin):
    """AI任务管理"""
    
    list_display = ['name', 'task_type', 'status', 'progress_bar', 'start_time', 'duration']
    list_filter = ['task_type', 'status', 'created_at']
    search_fields = ['name']
    readonly_fields = ['created_at', 'updated_at', 'duration_display']
    
    fieldsets = (
        ('任务信息', {
            'fields': ('name', 'task_type', 'status', 'progress')
        }),
        ('任务参数', {
            'fields': ('parameters',),
            'classes': ('collapse',)
        }),
        ('执行结果', {
            'fields': ('result_data', 'error_message'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('start_time', 'end_time', 'duration_display', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def progress_bar(self, obj):
        """进度条"""
        if obj.progress == 0:
            color = 'gray'
        elif obj.progress < 50:
            color = 'orange'
        elif obj.progress < 100:
            color = 'blue'
        else:
            color = 'green'
        
        return format_html(
            '<div style="width: 100px; background-color: #f0f0f0; border-radius: 3px;">'
            '<div style="width: {}px; height: 20px; background-color: {}; border-radius: 3px; text-align: center; color: white; font-size: 12px; line-height: 20px;">'
            '{}%</div></div>',
            obj.progress, color, obj.progress
        )
    progress_bar.short_description = '进度'
    
    def duration_display(self, obj):
        """任务时长显示"""
        duration = obj.duration
        if duration:
            return str(duration).split('.')[0]  # 去掉微秒
        return '-'
    duration_display.short_description = '执行时长'
    
    actions = ['restart_failed_tasks', 'cancel_pending_tasks']
    
    def restart_failed_tasks(self, request, queryset):
        """重启失败的任务"""
        failed_tasks = queryset.filter(status='failed')
        count = failed_tasks.update(status='pending', error_message='', start_time=None, end_time=None, progress=0)
        self.message_user(request, f'已重启 {count} 个失败的任务')
    restart_failed_tasks.short_description = '重启失败的任务'
    
    def cancel_pending_tasks(self, request, queryset):
        """取消待执行的任务"""
        pending_tasks = queryset.filter(status='pending')
        count = pending_tasks.update(status='cancelled')
        self.message_user(request, f'已取消 {count} 个待执行的任务')
    cancel_pending_tasks.short_description = '取消待执行的任务'
