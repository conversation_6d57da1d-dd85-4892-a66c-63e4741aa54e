from django.test import TestCase
from django.contrib.auth import get_user_model
from apps.users.models import UserAddress, UserLoginLog

User = get_user_model()

class UserModelTest(TestCase):
    """用户模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            phone='13800138000'
        )
    
    def test_user_creation(self):
        """测试用户创建"""
        self.assertEqual(self.user.username, 'testuser')
        self.assertEqual(self.user.email, '<EMAIL>')
        self.assertEqual(self.user.phone, '13800138000')
        self.assertTrue(self.user.is_active)
        self.assertFalse(self.user.is_staff)
        self.assertFalse(self.user.is_superuser)
    
    def test_user_str_representation(self):
        """测试用户字符串表示"""
        self.assertEqual(str(self.user), 'testuser')
    
    def test_user_full_name(self):
        """测试用户全名"""
        self.user.first_name = 'Test'
        self.user.last_name = 'User'
        self.user.save()
        self.assertEqual(self.user.get_full_name(), 'Test User')
    
    def test_user_phone_unique(self):
        """测试手机号唯一性"""
        with self.assertRaises(Exception):
            User.objects.create_user(
                username='testuser2',
                email='<EMAIL>',
                password='testpass123',
                phone='13800138000'  # 重复手机号
            )


class UserAddressModelTest(TestCase):
    """用户地址模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.address = UserAddress.objects.create(
            user=self.user,
            recipient_name='张三',
            recipient_phone='13800138000',
            province='广东省',
            city='深圳市',
            district='南山区',
            detailed_address='科技园南区101号',
            postal_code='518000',
            is_default=True
        )
    
    def test_address_creation(self):
        """测试地址创建"""
        self.assertEqual(self.address.recipient_name, '张三')
        self.assertEqual(self.address.province, '广东省')
        self.assertTrue(self.address.is_default)
    
    def test_address_str_representation(self):
        """测试地址字符串表示"""
        expected = f"{self.user.username}的地址: 广东省深圳市南山区科技园南区101号"
        self.assertEqual(str(self.address), expected)


class UserLoginLogModelTest(TestCase):
    """用户登录日志模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.login_log = UserLoginLog.objects.create(
            user=self.user,
            ip_address='127.0.0.1',
            device_info='Test Device',
            browser_info='Test Browser'
        )
    
    def test_login_log_creation(self):
        """测试登录日志创建"""
        self.assertEqual(self.login_log.user, self.user)
        self.assertEqual(self.login_log.ip_address, '127.0.0.1')
        self.assertIsNotNone(self.login_log.login_time)
    
    def test_login_log_str_representation(self):
        """测试登录日志字符串表示"""
        expected = f"{self.user.username}于{self.login_log.login_time}登录，IP: {self.login_log.ip_address}"
        self.assertEqual(str(self.login_log), expected)
