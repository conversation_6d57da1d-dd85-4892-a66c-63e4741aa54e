"""
初始化微信配置的管理命令
"""
from django.core.management.base import BaseCommand
from apps.wechat.models import WechatConfig


class Command(BaseCommand):
    help = '初始化微信配置'

    def handle(self, *args, **options):
        """执行命令"""
        self.stdout.write('开始初始化微信配置...')
        
        # 创建默认配置
        configs = [
            {
                'platform': 'official',
                'name': 'default',
                'description': '默认公众号配置',
                'app_id': '',
                'app_secret': '',
                'token': '',
                'encoding_aes_key': '',
            },
            {
                'platform': 'miniprogram',
                'name': 'default',
                'description': '默认小程序配置',
                'app_id': '',
                'app_secret': '',
            },
            {
                'platform': 'work',
                'name': 'default',
                'description': '默认企业微信配置',
                'corp_id': '',
                'corp_secret': '',
                'agent_id': '',
            },
            {
                'platform': 'payment',
                'name': 'default',
                'description': '默认微信支付配置',
                'app_id': '',
                'mch_id': '',
                'mch_key': '',
                'cert_path': '',
                'key_path': '',
            },
        ]
        
        created_count = 0
        for config_data in configs:
            config, created = WechatConfig.objects.get_or_create(
                platform=config_data['platform'],
                name=config_data['name'],
                defaults=config_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(
                        f'创建配置: {config.get_platform_display()} - {config.name}'
                    )
                )
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f'配置已存在: {config.get_platform_display()} - {config.name}'
                    )
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'初始化完成！共创建 {created_count} 个配置。')
        )
        
        if created_count > 0:
            self.stdout.write(
                self.style.WARNING(
                    '请到后台管理界面 (/admin/wechat/wechatconfig/) 填写具体的配置参数。'
                )
            )
