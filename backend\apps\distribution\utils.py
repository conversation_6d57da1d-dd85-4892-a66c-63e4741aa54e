import random
import string
from decimal import Decimal
from django.utils import timezone
from django.db import transaction
from .models import DistributionRelation, CommissionRecord, DistributionConfig


def generate_invitation_code(length=8):
    """生成邀请码"""
    characters = string.ascii_uppercase + string.digits
    while True:
        code = ''.join(random.choices(characters, k=length))
        # 确保邀请码唯一
        if not DistributionRelation.objects.filter(invitation_code=code).exists():
            return code


def generate_withdrawal_no():
    """生成提现单号"""
    from datetime import datetime
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    random_suffix = ''.join(random.choices(string.digits, k=4))
    return f'WD{timestamp}{random_suffix}'


def get_distribution_config(key, default=None):
    """获取分销配置"""
    try:
        config = DistributionConfig.objects.get(key=key, is_active=True)
        return config.value
    except DistributionConfig.DoesNotExist:
        return default


def is_distribution_enabled():
    """检查分销系统是否启用"""
    return get_distribution_config('distribution_enabled', 'false').lower() == 'true'


def get_max_distribution_level():
    """获取最大分销层级"""
    return int(get_distribution_config('max_distribution_level', '3'))


def get_min_withdrawal_amount():
    """获取最小提现金额"""
    return Decimal(get_distribution_config('min_withdrawal_amount', '10.00'))


def get_withdrawal_fee_rate():
    """获取提现手续费率"""
    return Decimal(get_distribution_config('withdrawal_fee_rate', '0.01'))


def calculate_withdrawal_fee(amount):
    """计算提现手续费"""
    fee_rate = get_withdrawal_fee_rate()
    return amount * fee_rate


def get_commission_settlement_days():
    """获取佣金结算周期"""
    return int(get_distribution_config('commission_settlement_days', '7'))


def build_distribution_tree(relation, max_depth=3, current_depth=0):
    """构建分销关系树"""
    if current_depth >= max_depth:
        return None
    
    tree = {
        'relation': relation,
        'user': relation.user,
        'level': relation.level,
        'stats': {
            'total_sales': relation.total_sales,
            'total_commission': relation.total_commission,
            'referral_count': relation.referral_count,
        },
        'children': []
    }
    
    # 获取直接下级
    children = relation.children.filter(is_active=True)
    for child in children:
        child_tree = build_distribution_tree(child, max_depth, current_depth + 1)
        if child_tree:
            tree['children'].append(child_tree)
    
    return tree


def calculate_team_performance(relation):
    """计算团队业绩"""
    def _calculate_recursive(rel, depth=0, max_depth=10):
        if depth >= max_depth:
            return {
                'total_sales': Decimal('0.00'),
                'total_commission': Decimal('0.00'),
                'member_count': 0
            }
        
        # 当前分销商的业绩
        performance = {
            'total_sales': rel.total_sales,
            'total_commission': rel.total_commission,
            'member_count': 1
        }
        
        # 累加下级业绩
        for child in rel.children.filter(is_active=True):
            child_performance = _calculate_recursive(child, depth + 1, max_depth)
            performance['total_sales'] += child_performance['total_sales']
            performance['total_commission'] += child_performance['total_commission']
            performance['member_count'] += child_performance['member_count']
        
        return performance
    
    return _calculate_recursive(relation)


def process_commission_settlement():
    """处理佣金结算"""
    settlement_days = get_commission_settlement_days()
    cutoff_date = timezone.now() - timezone.timedelta(days=settlement_days)
    
    # 获取需要结算的佣金记录
    pending_commissions = CommissionRecord.objects.filter(
        status='confirmed',
        created_at__lte=cutoff_date
    )
    
    settled_count = 0
    with transaction.atomic():
        for commission in pending_commissions:
            commission.status = 'settled'
            commission.settled_at = timezone.now()
            commission.save()
            settled_count += 1
    
    return settled_count


def check_level_upgrade(relation):
    """检查分销商等级升级"""
    from .models import DistributionLevel

    # 获取所有可用的分销等级，按等级从低到高排序
    levels = DistributionLevel.objects.filter(is_active=True).order_by('level')

    # 找到最高的符合条件的等级
    target_level = None
    for level in levels:
        # 检查是否满足升级条件
        if (relation.total_sales >= level.min_sales and
            relation.referral_count >= level.min_referrals and
            level.level > relation.level.level):
            target_level = level

    # 如果找到了更高的等级，进行升级
    if target_level:
        old_level = relation.level
        relation.level = target_level
        relation.save()

        return {
            'upgraded': True,
            'old_level': old_level,
            'new_level': target_level
        }

    return {'upgraded': False}


def validate_promotion_code(code):
    """验证推广码"""
    from .models import PromotionCode
    
    try:
        promotion_code = PromotionCode.objects.get(code=code)
        
        if not promotion_code.is_valid():
            return {
                'valid': False,
                'message': '推广码已失效',
                'code': None
            }
        
        return {
            'valid': True,
            'message': '推广码有效',
            'code': promotion_code
        }
        
    except PromotionCode.DoesNotExist:
        return {
            'valid': False,
            'message': '推广码不存在',
            'code': None
        }


def get_distribution_statistics():
    """获取分销系统统计信息"""
    from django.db.models import Sum, Count, Avg
    from .models import DistributionLevel, WithdrawalRecord
    
    # 分销商统计
    distributor_stats = DistributionRelation.objects.filter(is_active=True).aggregate(
        total_count=Count('id'),
        total_sales=Sum('total_sales'),
        total_commission=Sum('total_commission'),
        avg_sales=Avg('total_sales')
    )
    
    # 按等级统计
    level_stats = []
    for level in DistributionLevel.objects.filter(is_active=True).order_by('level'):
        level_distributors = DistributionRelation.objects.filter(
            level=level,
            is_active=True
        )
        level_stat = {
            'level': level,
            'count': level_distributors.count(),
            'total_sales': level_distributors.aggregate(Sum('total_sales'))['total_sales__sum'] or Decimal('0.00'),
            'total_commission': level_distributors.aggregate(Sum('total_commission'))['total_commission__sum'] or Decimal('0.00')
        }
        level_stats.append(level_stat)
    
    # 佣金统计
    commission_stats = CommissionRecord.objects.aggregate(
        total_amount=Sum('commission_amount'),
        pending_amount=Sum('commission_amount', filter=models.Q(status='pending')),
        confirmed_amount=Sum('commission_amount', filter=models.Q(status='confirmed')),
        settled_amount=Sum('commission_amount', filter=models.Q(status='settled'))
    )
    
    # 提现统计
    withdrawal_stats = WithdrawalRecord.objects.aggregate(
        total_amount=Sum('amount'),
        pending_amount=Sum('amount', filter=models.Q(status='pending')),
        approved_amount=Sum('amount', filter=models.Q(status='approved')),
        completed_amount=Sum('amount', filter=models.Q(status='completed'))
    )
    
    return {
        'distributor_stats': distributor_stats,
        'level_stats': level_stats,
        'commission_stats': commission_stats,
        'withdrawal_stats': withdrawal_stats
    }
