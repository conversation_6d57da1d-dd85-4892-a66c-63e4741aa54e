from django.contrib import admin
from .models import User, UserAddress, UserLoginLog
from apps.core.admin import admin_site

class UserAdmin(admin.ModelAdmin):
    """用户模型管理界面"""
    list_display = ('id', 'username', 'email', 'phone', 'nickname', 'is_active', 'is_staff', 'is_verified', 'date_joined')
    search_fields = ('username', 'email', 'phone', 'nickname')
    list_filter = ('is_active', 'is_staff', 'is_verified', 'gender', 'date_joined')
    readonly_fields = ('date_joined', 'last_login')
    fieldsets = (
        ('基本信息', {'fields': ('username', 'email', 'password', 'phone', 'nickname')}),
        ('个人资料', {'fields': ('avatar', 'gender', 'birth_date')}),
        ('权限设置', {'fields': ('is_active', 'is_staff', 'is_superuser', 'is_verified', 'groups', 'user_permissions')}),
        ('时间信息', {'fields': ('date_joined', 'last_login')}),
    )


class UserAddressAdmin(admin.ModelAdmin):
    """用户地址模型管理界面"""
    list_display = ('id', 'user', 'recipient_name', 'recipient_phone', 'province', 'city', 'district', 'is_default')
    search_fields = ('user__username', 'recipient_name', 'recipient_phone', 'province', 'city', 'district', 'detailed_address')
    list_filter = ('is_default', 'province', 'city')
    readonly_fields = ('created_at', 'updated_at')


class UserLoginLogAdmin(admin.ModelAdmin):
    """用户登录日志模型管理界面"""
    list_display = ('id', 'user', 'ip_address', 'login_time', 'device_info')
    search_fields = ('user__username', 'ip_address', 'device_info', 'browser_info')
    list_filter = ('login_time',)
    readonly_fields = ('user', 'ip_address', 'login_time', 'logout_time', 'device_info', 'browser_info')
    date_hierarchy = 'login_time'


# 注册到自定义管理站点
admin_site.register(User, UserAdmin)
admin_site.register(UserAddress, UserAddressAdmin)
admin_site.register(UserLoginLog, UserLoginLogAdmin)