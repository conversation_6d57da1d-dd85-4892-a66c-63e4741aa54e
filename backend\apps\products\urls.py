from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    ProductCategoryViewSet,
    ProductViewSet,
    ProductImageViewSet,
    ProductAttributeViewSet,
    ProductReviewViewSet
)

app_name = 'products'

# 创建路由器
router = DefaultRouter()
router.register(r'categories', ProductCategoryViewSet, basename='category')
router.register(r'products', ProductViewSet, basename='product')
router.register(r'images', ProductImageViewSet, basename='image')
router.register(r'attributes', ProductAttributeViewSet, basename='attribute')
router.register(r'reviews', ProductReviewViewSet, basename='review')

urlpatterns = [
    path('', include(router.urls)),
] 