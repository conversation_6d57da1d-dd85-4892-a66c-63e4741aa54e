# 智梦科技系统开发规则

## 项目基本信息
- 项目名称：智梦科技企业级系统
- 开发时间：2025年7月24日开始
- 技术栈：Django 5.2.4 + uniapp + 微信生态
- 开发模式：分层迭代开发（楼层式架构）

## 开发原则

### 1. 功能模块化开发理念
- 严格按照基础架构→核心业务→微信生态→营销分销→AI智能的顺序开发
- 每个阶段完成后进行完整测试和Git备份
- 下一阶段开发前必须确保前一阶段功能稳定
- 不允许跨阶段开发，避免架构混乱

### 2. 图标使用规则 ⚠️ 重要
- **禁止使用静态图片文件**（.png/.jpg/.svg）作为UI图标
- **统一使用emoji表情符号**或阿里巴巴矢量图标库
- tabBar导航栏直接在text中使用emoji，如"🎨 AI处理"
- 避免图片文件管理问题，减少体积，提高兼容性
- 图标示例：🏠首页 🛒商品 🛍️购物车 👤用户 ⚙️设置

### 3. 目录结构规范
- Django后端项目统一放在 `backend/` 目录
- uniapp小程序项目统一放在 `frontend/` 目录
- 不允许在项目根目录直接放置业务代码
- 配置文件(.env, .gitignore, requirements.txt等)放在backend目录
- 微信支付证书文件放在backend/certs/wechat/目录

### 4. 测试驱动开发
- 每个模块必须包含完整的单元测试
- 编写功能代码的同时编写对应的测试用例
- 测试覆盖率要求：核心业务模块≥90%，其他模块≥70%
- 集成测试必须覆盖完整的业务流程

## Django后端开发规范

### 1. 代码结构
- 使用Django App模式，每个App专注单一职责
- 模型定义在models.py，视图在views.py，序列化器在serializers.py
- 每个App必须包含tests/目录，按功能分类测试文件
- 使用Django REST Framework构建API
- 后台管理优先使用Django自带的admin系统
- PC网站尽量使用Django自带功能，但要支持主题切换

### 2. 数据库设计
- 支持PostgreSQL和MySQL8双数据库
- 所有模型必须继承抽象基类，包含created_at、updated_at字段
- 外键关系必须设置on_delete参数
- 索引优化：为常用查询字段添加数据库索引

### 3. API设计
- 遵循RESTful API设计原则
- 统一的响应格式：{code: int, message: str, data: any}
- API版本管理：使用URL版本控制(/api/v1/)
- 自动生成API文档使用drf-spectacular

### 4. 安全规范
- 所有敏感配置使用环境变量
- API接口必须有权限验证
- 微信支付回调必须验证签名
- 用户输入必须进行数据验证和清理

### 5. 测试规范
```python
# 单元测试示例
class UserModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>'
        )
    
    def test_user_creation(self):
        self.assertEqual(self.user.username, 'testuser')
        self.assertTrue(self.user.is_active)
    
    def test_user_str_representation(self):
        self.assertEqual(str(self.user), 'testuser')
```

## uniapp前端开发规范

### 1. 项目结构
- 页面文件统一放在pages/目录，按功能模块分组
- 组件文件放在components/目录，按通用性分类
- 工具函数放在utils/目录，按功能分文件
- 状态管理使用Vuex，按模块划分store

### 2. 命名规范
- 页面文件：小写+横线，如user-profile.vue
- 组件文件：大驼峰，如ProductCard.vue
- 变量名：小驼峰，如userName
- 常量：大写+下划线，如API_BASE_URL

### 3. 样式规范
- 使用uni.scss全局样式
- 组件样式使用scoped
- 响应式设计：支持不同屏幕尺寸
- 颜色主题：定义全局颜色变量

### 4. 图标使用（重要）
```vue
<!-- 正确：使用emoji -->
<text class="tab-icon">🏠</text>
<text class="tab-text">首页</text>

<!-- 正确：使用阿里矢量图标 -->
<text class="iconfont icon-home"></text>

<!-- 错误：禁止使用图片图标 -->
<image src="/static/icons/home.png"></image>
```

### 5. 测试规范
- 使用Jest进行单元测试
- 组件测试覆盖props、events、computed
- 工具函数必须有完整的测试用例
- E2E测试覆盖关键用户流程

## 微信集成规范

### 1. 微信登录
- 使用UnionID统一用户身份
- 妥善处理用户授权流程
- 用户信息本地缓存和同步

### 2. 微信支付
- 严格验证支付回调签名
- 支付状态必须双重确认
- 异常处理和重试机制

### 3. 公众号开发
- 菜单配置通过后台管理
- 消息回复支持文本、图片、链接
- 用户标签和分组管理

## 环境配置规范

### 1. 环境变量管理
- 使用.env文件管理所有配置
- 敏感信息不允许写在代码中
- 开发、测试、生产环境分离配置

### 2. 依赖管理
- Python依赖使用requirements.txt
- Node.js依赖使用package.json
- 版本锁定：指定具体版本号

### 3. 数据库配置
```python
# 支持数据库切换
DATABASES = {
    'default': {
        'ENGINE': f'django.db.backends.{config("DATABASE_ENGINE")}',
        'NAME': config('DATABASE_NAME'),
        'USER': config('DATABASE_USER'),
        'PASSWORD': config('DATABASE_PASSWORD'),
        'HOST': config('DATABASE_HOST'),
        'PORT': config('DATABASE_PORT'),
    }
}
```

## Git工作流规范

### 1. 分支管理
- main分支：生产环境代码
- develop分支：开发环境代码
- feature/*：功能开发分支
- hotfix/*：热修复分支

### 2. 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
test: 测试相关
refactor: 代码重构
style: 代码格式
perf: 性能优化
```

### 3. 阶段提交策略
- 每完成一个阶段的开发，创建对应的Git标签
- 标签命名：v1.0-basic, v1.0-business, v1.0-wechat, v1.0-distribution, v1.0-ai
- 重要功能点必须及时提交

## 性能优化规范

### 1. 后端优化
- 数据库查询优化：使用select_related、prefetch_related
- 缓存策略：Redis缓存热点数据
- API响应时间：核心接口<200ms，其他接口<500ms

### 2. 前端优化
- 图片懒加载：长列表图片延迟加载
- 组件按需加载：大型组件异步加载
- 本地存储：合理使用缓存减少网络请求

## 文档规范

### 1. 代码注释
- 类和函数必须有中文docstring
- 复杂业务逻辑必须有中文行注释
- API接口必须有完整的中文参数说明
- 数据库字段必须包含中文help_text
- 所有用户可见的文本必须使用简体中文

### 2. 文档维护
- 只维护两个主要文档：软件设计文档.md、开发进度管理.md
- 及时更新架构变更和开发进度
- API文档自动生成，无需手动维护

## 部署规范

### 1. 开发环境
- Windows 10本地开发
- 外网映射：http://zmkj.nat100.top
- 数据库：PostgreSQL/MySQL本地安装

### 2. 生产环境
- 腾讯云轻量服务器：CentOS 7.6
- 域名：https://zmkj.live
- Web服务器：Nginx + Gunicorn
- 自动化部署：Gitee CI/CD

## 安全规范

### 1. 数据安全
- 用户密码必须加密存储
- 敏感数据传输使用HTTPS
- 定期备份数据库和文件

### 2. 接口安全
- API接口限流：防止恶意请求
- 参数验证：所有输入必须验证
- 错误处理：不暴露敏感信息

## 团队协作规范

### 1. 开发流程
1. 从develop分支创建feature分支
2. 完成功能开发和测试
3. 创建Pull Request
4. 代码审查通过后合并
5. 部署到测试环境验证
6. 合并到main分支部署生产

### 2. 沟通规范
- 重要功能开发前必须讨论技术方案
- 发现问题及时在开发进度管理文档中记录
- 定期同步开发进度和遇到的问题

---

**文档版本**：v1.0  
**创建时间**：2025年7月24日  
**维护人员**：智梦科技开发团队

## Django管理系统规范

### 1. 后台管理系统
- 优先使用Django自带的admin管理系统
- 自定义admin界面时使用中文界面和字段标签
- 支持暗色/亮色主题切换功能
- 管理界面必须responsive，支持移动端访问

### 2. PC网站开发
- 优先使用Django自带的功能和模板系统
- 网站支持主题切换（亮色/暗色模式）
- 响应式设计，兼容不同设备
- 使用Bootstrap 5进行样式美化

### 3. 主题切换实现
```python
# 在模板中支持主题切换
class ThemeMiddleware:
    """主题切换中间件"""
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # 从cookie或session获取主题设置
        theme = request.COOKIES.get('theme', 'light')
        request.theme = theme
        response = self.get_response(request)
        return response
```

## 注意事项
1. 所有开发人员必须严格遵循以上规范
2. 违反图标使用规则将影响项目整体质量
3. 测试用例是代码质量的重要保障，不可忽视
4. 功能模块化开发确保项目稳定可控，不可跨阶段开发
5. 配置文件统一放在backend目录，包括.env、.gitignore等
6. 微信支付证书文件必须放在backend/certs/wechat/目录 