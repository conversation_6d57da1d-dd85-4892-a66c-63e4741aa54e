/**
 * 格式化工具
 * 提供各种数据格式化功能
 */

/**
 * 格式化金额
 * @param amount 金额
 * @param decimals 小数位数，默认2位
 * @param symbol 货币符号，默认￥
 */
export function formatMoney(amount: number | string, decimals: number = 2, symbol: string = '￥'): string {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  
  if (isNaN(num)) {
    return `${symbol}0.00`
  }
  
  return `${symbol}${num.toFixed(decimals)}`
}

/**
 * 格式化数字，添加千分位分隔符
 */
export function formatNumber(num: number | string): string {
  const number = typeof num === 'string' ? parseFloat(num) : num
  
  if (isNaN(number)) {
    return '0'
  }
  
  return number.toLocaleString('zh-CN')
}

/**
 * 格式化手机号，中间4位用*替代
 */
export function formatPhone(phone: string): string {
  if (!phone || phone.length !== 11) {
    return phone
  }
  
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 格式化身份证号，中间部分用*替代
 */
export function formatIdCard(idCard: string): string {
  if (!idCard || idCard.length !== 18) {
    return idCard
  }
  
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

/**
 * 格式化银行卡号，只显示后4位
 */
export function formatBankCard(cardNumber: string): string {
  if (!cardNumber || cardNumber.length < 4) {
    return cardNumber
  }
  
  const lastFour = cardNumber.slice(-4)
  return `**** **** **** ${lastFour}`
}

/**
 * 格式化时间
 */
export function formatTime(time: string | Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  const date = typeof time === 'string' ? new Date(time) : time
  
  if (isNaN(date.getTime())) {
    return ''
  }
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化相对时间（多久前）
 */
export function formatRelativeTime(time: string | Date): string {
  const date = typeof time === 'string' ? new Date(time) : time
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 0) {
    return '刚刚'
  }
  
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  const months = Math.floor(days / 30)
  const years = Math.floor(months / 12)
  
  if (years > 0) {
    return `${years}年前`
  } else if (months > 0) {
    return `${months}个月前`
  } else if (days > 0) {
    return `${days}天前`
  } else if (hours > 0) {
    return `${hours}小时前`
  } else if (minutes > 0) {
    return `${minutes}分钟前`
  } else {
    return '刚刚'
  }
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化百分比
 */
export function formatPercent(value: number, decimals: number = 2): string {
  if (isNaN(value)) {
    return '0%'
  }
  
  return `${(value * 100).toFixed(decimals)}%`
}

/**
 * 格式化地址
 */
export function formatAddress(address: {
  province?: string
  city?: string
  district?: string
  detail?: string
}): string {
  const { province, city, district, detail } = address
  
  let result = ''
  
  if (province) result += province
  if (city && city !== province) result += city
  if (district && district !== city) result += district
  if (detail) result += detail
  
  return result
}

/**
 * 格式化订单状态
 */
export function formatOrderStatus(status: string): {
  text: string
  color: string
} {
  const statusMap: Record<string, { text: string; color: string }> = {
    pending: { text: '待付款', color: '#ff9500' },
    paid: { text: '已付款', color: '#007aff' },
    shipped: { text: '已发货', color: '#34c759' },
    delivered: { text: '已送达', color: '#30d158' },
    completed: { text: '已完成', color: '#32d74b' },
    cancelled: { text: '已取消', color: '#8e8e93' },
    refunding: { text: '退款中', color: '#ff3b30' },
    refunded: { text: '已退款', color: '#ff2d92' }
  }
  
  return statusMap[status] || { text: '未知状态', color: '#8e8e93' }
}

/**
 * 格式化佣金状态
 */
export function formatCommissionStatus(status: string): {
  text: string
  color: string
} {
  const statusMap: Record<string, { text: string; color: string }> = {
    pending: { text: '待结算', color: '#ff9500' },
    settled: { text: '已结算', color: '#34c759' },
    withdrawn: { text: '已提现', color: '#007aff' },
    frozen: { text: '已冻结', color: '#ff3b30' }
  }
  
  return statusMap[status] || { text: '未知状态', color: '#8e8e93' }
}

/**
 * 格式化产品规格
 */
export function formatProductSpec(specs: Record<string, string>): string {
  if (!specs || Object.keys(specs).length === 0) {
    return ''
  }
  
  return Object.entries(specs)
    .map(([key, value]) => `${key}: ${value}`)
    .join('; ')
}

/**
 * 格式化搜索关键词高亮
 */
export function formatHighlight(text: string, keyword: string): string {
  if (!keyword) return text
  
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<span class="highlight">$1</span>')
}

/**
 * 格式化URL参数
 */
export function formatUrlParams(params: Record<string, any>): string {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      searchParams.append(key, String(value))
    }
  })
  
  return searchParams.toString()
}

/**
 * 格式化分销等级
 */
export function formatDistributorLevel(level: number): {
  text: string
  color: string
  icon: string
} {
  const levelMap: Record<number, { text: string; color: string; icon: string }> = {
    0: { text: '普通用户', color: '#8e8e93', icon: 'user' },
    1: { text: '一级分销商', color: '#ff9500', icon: 'crown' },
    2: { text: '二级分销商', color: '#007aff', icon: 'diamond' },
    3: { text: '三级分销商', color: '#af52de', icon: 'star' }
  }
  
  return levelMap[level] || levelMap[0]
}

/**
 * 格式化距离
 */
export function formatDistance(meters: number): string {
  if (meters < 1000) {
    return `${Math.round(meters)}m`
  } else {
    return `${(meters / 1000).toFixed(1)}km`
  }
}

/**
 * 格式化评分
 */
export function formatRating(rating: number, maxRating: number = 5): string {
  const stars = '★'.repeat(Math.floor(rating)) + '☆'.repeat(maxRating - Math.floor(rating))
  return `${stars} ${rating.toFixed(1)}`
}

/**
 * 格式化库存状态
 */
export function formatStockStatus(stock: number): {
  text: string
  color: string
} {
  if (stock <= 0) {
    return { text: '缺货', color: '#ff3b30' }
  } else if (stock <= 10) {
    return { text: '库存紧张', color: '#ff9500' }
  } else {
    return { text: '有库存', color: '#34c759' }
  }
}

/**
 * 格式化优惠券
 */
export function formatCoupon(coupon: {
  type: 'discount' | 'amount'
  value: number
  min_amount?: number
}): string {
  const { type, value, min_amount } = coupon
  
  let text = ''
  
  if (type === 'discount') {
    text = `${(value * 10).toFixed(1)}折`
  } else {
    text = `￥${value}`
  }
  
  if (min_amount && min_amount > 0) {
    text += `（满￥${min_amount}可用）`
  }
  
  return text
}

export default {
  formatMoney,
  formatNumber,
  formatPhone,
  formatIdCard,
  formatBankCard,
  formatTime,
  formatRelativeTime,
  formatFileSize,
  formatPercent,
  formatAddress,
  formatOrderStatus,
  formatCommissionStatus,
  formatProductSpec,
  formatHighlight,
  formatUrlParams,
  formatDistributorLevel,
  formatDistance,
  formatRating,
  formatStockStatus,
  formatCoupon
}
