"""
微信公众号模块
"""
import json
import time
import requests
import xml.etree.ElementTree as ET
from django.conf import settings
from django.utils import timezone
from .models import WechatUser, WechatMessage, WechatMenu, WechatConfig


class WechatOfficial:
    """微信公众号API类"""
    
    def __init__(self, config_name='default'):
        # 从数据库获取配置
        config = WechatConfig.get_config('official', config_name)
        self.app_id = config.get('app_id', '')
        self.app_secret = config.get('app_secret', '')
        self.token = config.get('token', '')
        self.encoding_aes_key = config.get('encoding_aes_key', '')
        self.api_base = 'https://api.weixin.qq.com/cgi-bin'
        self._access_token = None
        self._access_token_expires = 0
    
    def get_access_token(self):
        """获取access_token"""
        if self._access_token and time.time() < self._access_token_expires:
            return self._access_token
        
        url = f"{self.api_base}/token"
        params = {
            'grant_type': 'client_credential',
            'appid': self.app_id,
            'secret': self.app_secret
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if 'errcode' in data:
            raise Exception(f"获取access_token失败: {data.get('errmsg', '未知错误')}")
        
        self._access_token = data['access_token']
        self._access_token_expires = time.time() + data['expires_in'] - 300  # 提前5分钟过期
        
        return self._access_token
    
    def parse_message(self, xml_data):
        """解析微信消息"""
        root = ET.fromstring(xml_data)
        msg_data = {}
        for child in root:
            msg_data[child.tag] = child.text
        return msg_data
    
    def create_text_response(self, to_user, from_user, content):
        """创建文本回复消息"""
        timestamp = int(time.time())
        xml_template = """
        <xml>
        <ToUserName><![CDATA[{to_user}]]></ToUserName>
        <FromUserName><![CDATA[{from_user}]]></FromUserName>
        <CreateTime>{timestamp}</CreateTime>
        <MsgType><![CDATA[text]]></MsgType>
        <Content><![CDATA[{content}]]></Content>
        </xml>
        """
        return xml_template.format(
            to_user=to_user,
            from_user=from_user,
            timestamp=timestamp,
            content=content
        ).strip()
    
    def create_news_response(self, to_user, from_user, articles):
        """创建图文回复消息"""
        timestamp = int(time.time())
        articles_xml = ""
        for article in articles:
            articles_xml += f"""
            <item>
            <Title><![CDATA[{article.get('title', '')}]]></Title>
            <Description><![CDATA[{article.get('description', '')}]]></Description>
            <PicUrl><![CDATA[{article.get('pic_url', '')}]]></PicUrl>
            <Url><![CDATA[{article.get('url', '')}]]></Url>
            </item>
            """
        
        xml_template = f"""
        <xml>
        <ToUserName><![CDATA[{to_user}]]></ToUserName>
        <FromUserName><![CDATA[{from_user}]]></FromUserName>
        <CreateTime>{timestamp}</CreateTime>
        <MsgType><![CDATA[news]]></MsgType>
        <ArticleCount>{len(articles)}</ArticleCount>
        <Articles>
        {articles_xml}
        </Articles>
        </xml>
        """
        return xml_template.strip()
    
    def handle_message(self, xml_data):
        """处理微信消息"""
        msg_data = self.parse_message(xml_data)
        
        # 保存消息记录
        self.save_message(msg_data)
        
        # 根据消息类型处理
        msg_type = msg_data.get('MsgType')
        
        if msg_type == 'text':
            return self.handle_text_message(msg_data)
        elif msg_type == 'event':
            return self.handle_event_message(msg_data)
        elif msg_type == 'image':
            return self.handle_image_message(msg_data)
        elif msg_type == 'voice':
            return self.handle_voice_message(msg_data)
        elif msg_type == 'location':
            return self.handle_location_message(msg_data)
        else:
            return self.create_text_response(
                msg_data['FromUserName'],
                msg_data['ToUserName'],
                "感谢您的消息，我们会尽快回复！"
            )
    
    def handle_text_message(self, msg_data):
        """处理文本消息"""
        content = msg_data.get('Content', '').strip()
        
        # 简单的关键词回复
        if '帮助' in content or 'help' in content.lower():
            reply_content = """
欢迎使用智梦科技系统！

🔹 输入'产品'查看产品列表
🔹 输入'订单'查看订单状态
🔹 输入'联系'获取联系方式
🔹 输入'帮助'查看帮助信息

如需更多帮助，请联系客服。
            """.strip()
        elif '产品' in content:
            reply_content = "正在为您展示产品列表，请稍候..."
        elif '订单' in content:
            reply_content = "请登录系统查看您的订单详情。"
        elif '联系' in content:
            reply_content = """
📞 客服热线：400-123-4567
📧 邮箱：<EMAIL>
🌐 官网：https://zmkj.live
⏰ 服务时间：9:00-18:00
            """.strip()
        else:
            reply_content = "感谢您的消息！如需帮助，请输入'帮助'查看可用命令。"
        
        return self.create_text_response(
            msg_data['FromUserName'],
            msg_data['ToUserName'],
            reply_content
        )
    
    def handle_event_message(self, msg_data):
        """处理事件消息"""
        event = msg_data.get('Event')
        
        if event == 'subscribe':
            # 关注事件
            self.update_user_subscribe_status(msg_data['FromUserName'], True)
            reply_content = """
🎉 欢迎关注智梦科技！

我们是一家专注于企业级系统开发的科技公司，为您提供：
✨ 产品销售系统
✨ 微信生态集成
✨ AI智能服务
✨ 分销营销系统

输入'帮助'了解更多功能！
            """.strip()
            
        elif event == 'unsubscribe':
            # 取消关注事件
            self.update_user_subscribe_status(msg_data['FromUserName'], False)
            return 'success'  # 取消关注不需要回复
            
        elif event == 'CLICK':
            # 菜单点击事件
            event_key = msg_data.get('EventKey')
            reply_content = self.handle_menu_click(event_key)
            
        elif event == 'VIEW':
            # 菜单跳转事件
            return 'success'  # 跳转事件不需要回复
            
        else:
            reply_content = "感谢您的操作！"
        
        return self.create_text_response(
            msg_data['FromUserName'],
            msg_data['ToUserName'],
            reply_content
        )
    
    def handle_image_message(self, msg_data):
        """处理图片消息"""
        return self.create_text_response(
            msg_data['FromUserName'],
            msg_data['ToUserName'],
            "收到您的图片，感谢分享！"
        )
    
    def handle_voice_message(self, msg_data):
        """处理语音消息"""
        return self.create_text_response(
            msg_data['FromUserName'],
            msg_data['ToUserName'],
            "收到您的语音消息，我们会尽快处理！"
        )
    
    def handle_location_message(self, msg_data):
        """处理位置消息"""
        return self.create_text_response(
            msg_data['FromUserName'],
            msg_data['ToUserName'],
            "收到您的位置信息，感谢分享！"
        )
    
    def handle_menu_click(self, event_key):
        """处理菜单点击"""
        menu_responses = {
            'MENU_PRODUCTS': '正在为您展示产品列表...',
            'MENU_ORDERS': '请登录系统查看订单详情。',
            'MENU_CONTACT': '客服热线：400-123-4567',
            'MENU_HELP': '如需帮助，请输入\'帮助\'查看可用命令。',
        }
        return menu_responses.get(event_key, '感谢您的点击！')
    
    def save_message(self, msg_data):
        """保存消息记录"""
        try:
            # 获取或创建微信用户
            openid = msg_data['FromUserName']
            wechat_user, created = WechatUser.objects.get_or_create(
                openid=openid,
                defaults={'nickname': openid}
            )
            
            # 保存消息
            WechatMessage.objects.create(
                wechat_user=wechat_user,
                msg_id=msg_data.get('MsgId', ''),
                msg_type=msg_data.get('MsgType', ''),
                direction='in',
                content=msg_data.get('Content', ''),
                pic_url=msg_data.get('PicUrl', ''),
                media_id=msg_data.get('MediaId', ''),
                location_x=float(msg_data.get('Location_X', 0)) if msg_data.get('Location_X') else None,
                location_y=float(msg_data.get('Location_Y', 0)) if msg_data.get('Location_Y') else None,
                scale=int(msg_data.get('Scale', 0)) if msg_data.get('Scale') else None,
                label=msg_data.get('Label', ''),
                title=msg_data.get('Title', ''),
                description=msg_data.get('Description', ''),
                url=msg_data.get('Url', ''),
                event=msg_data.get('Event', ''),
                event_key=msg_data.get('EventKey', ''),
            )
        except Exception as e:
            print(f"保存消息失败: {e}")
    
    def update_user_subscribe_status(self, openid, subscribe):
        """更新用户关注状态"""
        try:
            wechat_user, created = WechatUser.objects.get_or_create(
                openid=openid,
                defaults={'nickname': openid}
            )
            wechat_user.subscribe = subscribe
            if subscribe:
                wechat_user.subscribe_time = timezone.now()
            wechat_user.save()
        except Exception as e:
            print(f"更新用户关注状态失败: {e}")
    
    def create_menu(self):
        """创建自定义菜单"""
        access_token = self.get_access_token()
        url = f"{self.api_base}/menu/create?access_token={access_token}"
        
        # 从数据库获取菜单配置
        menus = WechatMenu.objects.filter(parent=None, is_active=True).order_by('sort_order')
        
        menu_data = {
            "button": []
        }
        
        for menu in menus:
            menu_item = {
                "name": menu.name
            }
            
            # 检查是否有子菜单
            sub_menus = menu.children.filter(is_active=True).order_by('sort_order')
            if sub_menus.exists():
                menu_item["sub_button"] = []
                for sub_menu in sub_menus:
                    sub_item = {
                        "name": sub_menu.name,
                        "type": sub_menu.type
                    }
                    if sub_menu.type == 'click':
                        sub_item["key"] = sub_menu.key
                    elif sub_menu.type == 'view':
                        sub_item["url"] = sub_menu.url
                    menu_item["sub_button"].append(sub_item)
            else:
                menu_item["type"] = menu.type
                if menu.type == 'click':
                    menu_item["key"] = menu.key
                elif menu.type == 'view':
                    menu_item["url"] = menu.url
            
            menu_data["button"].append(menu_item)
        
        response = requests.post(url, json=menu_data)
        result = response.json()
        
        if result.get('errcode') != 0:
            raise Exception(f"创建菜单失败: {result.get('errmsg', '未知错误')}")
        
        return result
    
    def delete_menu(self):
        """删除自定义菜单"""
        access_token = self.get_access_token()
        url = f"{self.api_base}/menu/delete?access_token={access_token}"
        
        response = requests.get(url)
        result = response.json()
        
        if result.get('errcode') != 0:
            raise Exception(f"删除菜单失败: {result.get('errmsg', '未知错误')}")
        
        return result
