{"version": 3, "file": "Icon.js", "sources": ["components/common/Icon.uvue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/Rjovem1rai1zeXN0ZW0vZnJvbnRlbmQvY29tcG9uZW50cy9jb21tb24vSWNvbi51dnVl"], "sourcesContent": ["<template>\n  <text \n    :class=\"['icon', `icon-${name}`, customClass]\" \n    :style=\"iconStyle\"\n    @click=\"handleClick\"\n  />\n</template>\n\n<script setup lang=\"uts\">\ninterface Props {\n  name: string          // 图标名称\n  size?: number         // 图标大小 (rpx)\n  color?: string        // 图标颜色\n  customClass?: string  // 自定义样式类\n}\n\ninterface Emits {\n  (e: 'click'): void\n}\n\nconst props = withDefaults(defineProps<Props>(), {\n  size: 32,\n  color: '#333333',\n  customClass: ''\n})\n\nconst emit = defineEmits<Emits>()\n\n// 计算图标样式\nconst iconStyle = computed(() => ({\n  fontSize: props.size + 'rpx',\n  color: props.color\n}))\n\n// 点击事件处理\nconst handleClick = () => {\n  emit('click')\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.icon {\n  font-family: 'iconfont';\n  font-style: normal;\n  display: inline-block;\n  vertical-align: middle;\n  line-height: 1;\n  \n  // 基础图标定义 (使用Unicode字符)\n  &.icon-home::before { content: '\\e001'; }        // 首页 🏠\n  &.icon-product::before { content: '\\e002'; }     // 产品 📦\n  &.icon-cart::before { content: '\\e003'; }        // 购物车 🛒\n  &.icon-order::before { content: '\\e004'; }       // 订单 📋\n  &.icon-user::before { content: '\\e005'; }        // 用户 👤\n  &.icon-search::before { content: '\\e006'; }      // 搜索 🔍\n  &.icon-share::before { content: '\\e007'; }       // 分享 📤\n  &.icon-star::before { content: '\\e008'; }        // 收藏 ⭐\n  &.icon-location::before { content: '\\e009'; }    // 位置 📍\n  &.icon-phone::before { content: '\\e010'; }       // 电话 📞\n  &.icon-wechat::before { content: '\\e011'; }      // 微信 💬\n  &.icon-money::before { content: '\\e012'; }       // 金钱 💰\n  &.icon-team::before { content: '\\e013'; }        // 团队 👥\n  &.icon-setting::before { content: '\\e014'; }     // 设置 ⚙️\n  &.icon-arrow-right::before { content: '\\e015'; } // 右箭头 ➡️\n  &.icon-arrow-left::before { content: '\\e016'; }  // 左箭头 ⬅️\n  &.icon-close::before { content: '\\e017'; }       // 关闭 ❌\n  &.icon-check::before { content: '\\e018'; }       // 选中 ✅\n  &.icon-plus::before { content: '\\e019'; }        // 加号 ➕\n  &.icon-minus::before { content: '\\e020'; }       // 减号 ➖\n  &.icon-edit::before { content: '\\e021'; }        // 编辑 ✏️\n  &.icon-delete::before { content: '\\e022'; }      // 删除 🗑️\n  &.icon-refresh::before { content: '\\e023'; }     // 刷新 🔄\n  &.icon-download::before { content: '\\e024'; }    // 下载 ⬇️\n  &.icon-upload::before { content: '\\e025'; }      // 上传 ⬆️\n  &.icon-camera::before { content: '\\e026'; }      // 相机 📷\n  &.icon-image::before { content: '\\e027'; }       // 图片 🖼️\n  &.icon-video::before { content: '\\e028'; }       // 视频 🎥\n  &.icon-audio::before { content: '\\e029'; }       // 音频 🎵\n  &.icon-file::before { content: '\\e030'; }        // 文件 📄\n  &.icon-folder::before { content: '\\e031'; }      // 文件夹 📁\n  &.icon-link::before { content: '\\e032'; }        // 链接 🔗\n  &.icon-lock::before { content: '\\e033'; }        // 锁定 🔒\n  &.icon-unlock::before { content: '\\e034'; }      // 解锁 🔓\n  &.icon-eye::before { content: '\\e035'; }         // 查看 👁️\n  &.icon-eye-close::before { content: '\\e036'; }   // 隐藏 🙈\n  &.icon-heart::before { content: '\\e037'; }       // 喜欢 ❤️\n  &.icon-heart-o::before { content: '\\e038'; }     // 未喜欢 🤍\n  &.icon-message::before { content: '\\e039'; }     // 消息 💬\n  &.icon-notification::before { content: '\\e040'; } // 通知 🔔\n  &.icon-calendar::before { content: '\\e041'; }    // 日历 📅\n  &.icon-clock::before { content: '\\e042'; }       // 时钟 🕐\n  &.icon-gift::before { content: '\\e043'; }        // 礼品 🎁\n  &.icon-coupon::before { content: '\\e044'; }      // 优惠券 🎫\n  &.icon-vip::before { content: '\\e045'; }         // VIP 👑\n  &.icon-diamond::before { content: '\\e046'; }     // 钻石 💎\n  &.icon-fire::before { content: '\\e047'; }        // 热门 🔥\n  &.icon-new::before { content: '\\e048'; }         // 新品 ✨\n  &.icon-hot::before { content: '\\e049'; }         // 热销 🌟\n  &.icon-sale::before { content: '\\e050'; }        // 促销 🏷️\n  \n  // 状态图标\n  &.icon-success::before { content: '\\e051'; }     // 成功 ✅\n  &.icon-error::before { content: '\\e052'; }       // 错误 ❌\n  &.icon-warning::before { content: '\\e053'; }     // 警告 ⚠️\n  &.icon-info::before { content: '\\e054'; }        // 信息 ℹ️\n  &.icon-loading::before { content: '\\e055'; }     // 加载 ⏳\n  \n  // 方向图标\n  &.icon-up::before { content: '\\e056'; }          // 向上 ⬆️\n  &.icon-down::before { content: '\\e057'; }        // 向下 ⬇️\n  &.icon-left::before { content: '\\e058'; }        // 向左 ⬅️\n  &.icon-right::before { content: '\\e059'; }       // 向右 ➡️\n  \n  // 社交图标\n  &.icon-qq::before { content: '\\e060'; }          // QQ 🐧\n  &.icon-weibo::before { content: '\\e061'; }       // 微博 📱\n  &.icon-alipay::before { content: '\\e062'; }      // 支付宝 💳\n  \n  // 业务图标\n  &.icon-distribution::before { content: '\\e063'; } // 分销 🤝\n  &.icon-commission::before { content: '\\e064'; }   // 佣金 💵\n  &.icon-promotion::before { content: '\\e065'; }    // 推广 📢\n  &.icon-invite::before { content: '\\e066'; }       // 邀请 👋\n  &.icon-qrcode::before { content: '\\e067'; }       // 二维码 📱\n  &.icon-barcode::before { content: '\\e068'; }      // 条形码 📊\n}\n\n// 图标动画效果\n.icon-loading {\n  animation: rotate 1s linear infinite;\n}\n\n@keyframes rotate {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n// 图标大小预设\n.icon-xs { font-size: 20rpx !important; }\n.icon-sm { font-size: 24rpx !important; }\n.icon-md { font-size: 32rpx !important; }\n.icon-lg { font-size: 40rpx !important; }\n.icon-xl { font-size: 48rpx !important; }\n\n// 图标颜色预设\n.icon-primary { color: #007AFF !important; }\n.icon-success { color: #34C759 !important; }\n.icon-warning { color: #FF9500 !important; }\n.icon-danger { color: #FF3B30 !important; }\n.icon-info { color: #5AC8FA !important; }\n.icon-secondary { color: #8E8E93 !important; }\n.icon-light { color: #F2F2F7 !important; }\n.icon-dark { color: #1C1C1E !important; }\n</style>\n", "import Component from 'F:/zmkj-system/frontend/components/common/Icon.uvue'\nwx.createComponent(Component)"], "names": ["computed"], "mappings": ";;;;;;;;;;;;;AAoBA,UAAM,QAAQ;AAMd,UAAM,OAAO;AAGb,UAAM,YAAYA,cAAQ,SAAC,MAAA;AAAM,aAAC,IAAA,cAAA;AAAA,QAChC,UAAU,MAAM,OAAO;AAAA,QACvB,OAAO,MAAM;AAAA,OACd;AAAA,IAAC,CAAA;AAGF,UAAM,cAAc,MAAA;AAClB,WAAK,OAAO;AAAA,IACd;;;;;;;;;;;;;;ACpCA,GAAG,gBAAgB,SAAS;"}