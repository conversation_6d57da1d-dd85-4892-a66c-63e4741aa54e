from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from django.contrib.admin.models import LogEntry
from django.utils.html import format_html
from django.urls import reverse, path
from django.template.response import TemplateResponse
from django.contrib.contenttypes.models import ContentType
from django.http import HttpResponseRedirect
from django.utils import timezone
from .models import Banner


class LogEntryAdmin(admin.ModelAdmin):
    """管理操作日志记录"""
    list_display = ('action_time', 'user', 'content_type', 'object_repr', 'action_flag', 'change_message')
    list_filter = ('action_time', 'user', 'content_type', 'action_flag')
    search_fields = ('object_repr', 'change_message')
    date_hierarchy = 'action_time'
    readonly_fields = ('action_time', 'user', 'content_type', 'object_id', 'object_repr', 'action_flag', 'change_message')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


# 注册管理操作日志
admin.site.register(LogEntry, LogEntryAdmin)


class BannerAdmin(admin.ModelAdmin):
    """轮播图管理"""
    list_display = (
        'title', 'image_preview', 'position', 'link_type', 'link_value',
        'sort_order', 'is_active', 'time_status', 'click_count', 'created_at'
    )
    list_filter = ('position', 'link_type', 'is_active', 'created_at')
    search_fields = ('title', 'link_value')
    list_editable = ('sort_order', 'is_active')
    ordering = ('position', 'sort_order', '-created_at')

    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'image', 'position')
        }),
        ('链接设置', {
            'fields': ('link_type', 'link_value'),
            'description': '根据链接类型填写对应的值：商品ID、分类ID、URL地址或页面路径'
        }),
        ('显示设置', {
            'fields': ('sort_order', 'is_active')
        }),
        ('时间设置', {
            'fields': ('start_time', 'end_time'),
            'classes': ('collapse',),
            'description': '留空表示永久有效'
        }),
        ('统计信息', {
            'fields': ('click_count',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('click_count',)

    def image_preview(self, obj):
        """图片预览"""
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width:120px;max-height:60px;border-radius:4px;" />',
                obj.image.url
            )
        return '-'
    image_preview.short_description = '图片预览'

    def time_status(self, obj):
        """时间状态"""
        if not obj.is_active:
            return format_html('<span style="color: #999;">已禁用</span>')

        now = timezone.now()
        if obj.start_time and now < obj.start_time:
            return format_html('<span style="color: #ff9500;">未开始</span>')
        elif obj.end_time and now > obj.end_time:
            return format_html('<span style="color: #ff3b30;">已过期</span>')
        else:
            return format_html('<span style="color: #34c759;">正常</span>')
    time_status.short_description = '状态'

    actions = ['make_active', 'make_inactive', 'reset_click_count']

    def make_active(self, request, queryset):
        """批量启用"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'已启用 {updated} 个轮播图')
    make_active.short_description = '启用选中的轮播图'

    def make_inactive(self, request, queryset):
        """批量禁用"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'已禁用 {updated} 个轮播图')
    make_inactive.short_description = '禁用选中的轮播图'

    def reset_click_count(self, request, queryset):
        """重置点击统计"""
        updated = queryset.update(click_count=0)
        self.message_user(request, f'已重置 {updated} 个轮播图的点击统计')
    reset_click_count.short_description = '重置点击统计'


class ZmkjAdminSite(admin.AdminSite):
    """自定义管理站点，支持主题切换"""
    site_header = _('智梦科技系统管理后台')
    site_title = _('智梦科技系统')
    index_title = _('系统管理')

    def get_urls(self):
        """添加主题切换URL"""
        urls = super().get_urls()
        custom_urls = [
            path('switch_theme/<str:theme>/', self.admin_view(self.switch_theme), name='switch_theme'),
        ]
        return custom_urls + urls

    def switch_theme(self, request, theme):
        """切换管理界面主题"""
        valid_themes = {
            'default': '默认主题',
            'cool-black': '酷黑',
            'elegant-blue': '雅蓝',
            'soft-green': '绿柔',
        }
        
        if theme in valid_themes:
            request.session['admin_theme'] = theme
            
        # 重定向回管理首页或来源页面
        next_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
        return HttpResponseRedirect(next_url)

    def each_context(self, request):
        """为管理界面模板添加主题上下文"""
        context = super().each_context(request)
        
        # 添加主题相关上下文
        context.update({
            'available_themes': {
                'default': '默认主题',
                'cool-black': '酷黑',
                'elegant-blue': '雅蓝',
                'soft-green': '绿柔',
            },
            'current_theme': request.session.get('admin_theme', 'default'),
        })
        
        return context


# 使用自定义管理站点替换默认站点
admin_site = ZmkjAdminSite(name='admin')
admin.site = admin_site

# 重新注册LogEntry到新的管理站点
admin_site.register(LogEntry, LogEntryAdmin)

# 注册轮播图到默认admin站点
admin.site.register(Banner, BannerAdmin)
