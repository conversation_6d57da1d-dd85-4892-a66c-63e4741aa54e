# -*- coding: utf-8 -*-
"""
智梦科技系统 - AI功能序列化器

AI功能模块的DRF序列化器
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    AIServiceConfig, ChatbotConversation, ChatbotMessage,
    RecommendationConfig, UserBehaviorLog, AITask
)

User = get_user_model()


class AIServiceConfigSerializer(serializers.ModelSerializer):
    """AI服务配置序列化器"""
    
    service_type_display = serializers.CharField(source='get_service_type_display', read_only=True)
    
    class Meta:
        model = AIServiceConfig
        fields = [
            'id', 'name', 'service_type', 'service_type_display',
            'api_endpoint', 'config_data', 'is_active',
            'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'api_key': {'write_only': True},  # API密钥只写不读
        }


class ChatbotMessageSerializer(serializers.ModelSerializer):
    """智能客服消息序列化器"""
    
    message_type_display = serializers.CharField(source='get_message_type_display', read_only=True)
    
    class Meta:
        model = ChatbotMessage
        fields = [
            'id', 'message_type', 'message_type_display',
            'content', 'metadata', 'created_at'
        ]


class ChatbotConversationSerializer(serializers.ModelSerializer):
    """智能客服对话序列化器"""
    
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    user_info = serializers.SerializerMethodField()
    message_count = serializers.SerializerMethodField()
    duration = serializers.SerializerMethodField()
    messages = ChatbotMessageSerializer(many=True, read_only=True)
    
    class Meta:
        model = ChatbotConversation
        fields = [
            'id', 'session_id', 'user', 'user_info', 'status', 'status_display',
            'start_time', 'end_time', 'duration', 'message_count', 'messages',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['session_id', 'start_time']
    
    def get_user_info(self, obj):
        """获取用户信息"""
        if obj.user:
            return {
                'id': obj.user.id,
                'username': obj.user.username,
                'nickname': getattr(obj.user, 'nickname', ''),
            }
        return None
    
    def get_message_count(self, obj):
        """获取消息数量"""
        return obj.messages.count()
    
    def get_duration(self, obj):
        """获取对话时长"""
        if obj.start_time and obj.end_time:
            duration = obj.end_time - obj.start_time
            return str(duration).split('.')[0]  # 去掉微秒
        return None


class RecommendationConfigSerializer(serializers.ModelSerializer):
    """推荐算法配置序列化器"""
    
    algorithm_type_display = serializers.CharField(source='get_algorithm_type_display', read_only=True)
    
    class Meta:
        model = RecommendationConfig
        fields = [
            'id', 'name', 'algorithm_type', 'algorithm_type_display',
            'parameters', 'weight', 'is_active',
            'created_at', 'updated_at'
        ]


class UserBehaviorLogSerializer(serializers.ModelSerializer):
    """用户行为日志序列化器"""
    
    action_type_display = serializers.CharField(source='get_action_type_display', read_only=True)
    user_info = serializers.SerializerMethodField()
    
    class Meta:
        model = UserBehaviorLog
        fields = [
            'id', 'user', 'user_info', 'session_id',
            'action_type', 'action_type_display',
            'target_type', 'target_id', 'context_data',
            'ip_address', 'user_agent', 'created_at'
        ]
        read_only_fields = ['created_at']
    
    def get_user_info(self, obj):
        """获取用户信息"""
        if obj.user:
            return {
                'id': obj.user.id,
                'username': obj.user.username,
                'nickname': getattr(obj.user, 'nickname', ''),
            }
        return None


class AITaskSerializer(serializers.ModelSerializer):
    """AI任务序列化器"""
    
    task_type_display = serializers.CharField(source='get_task_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = AITask
        fields = [
            'id', 'name', 'task_type', 'task_type_display',
            'status', 'status_display', 'parameters', 'result_data',
            'error_message', 'start_time', 'end_time', 'duration',
            'progress', 'created_at', 'updated_at'
        ]
        read_only_fields = ['start_time', 'end_time', 'created_at', 'updated_at']
    
    def get_duration(self, obj):
        """获取任务执行时长"""
        duration = obj.duration
        if duration:
            return str(duration).split('.')[0]  # 去掉微秒
        return None


# 简化的序列化器用于API响应
class ChatbotStartResponseSerializer(serializers.Serializer):
    """智能客服启动响应序列化器"""
    
    session_id = serializers.CharField()
    welcome_message = serializers.CharField()
    created_at = serializers.DateTimeField()


class ChatbotMessageRequestSerializer(serializers.Serializer):
    """智能客服消息请求序列化器"""
    
    session_id = serializers.CharField()
    message = serializers.CharField()


class ChatbotMessageResponseSerializer(serializers.Serializer):
    """智能客服消息响应序列化器"""
    
    bot_reply = serializers.CharField()
    intent = serializers.CharField()
    need_transfer = serializers.BooleanField()
    message_id = serializers.IntegerField()


class RecommendationRequestSerializer(serializers.Serializer):
    """推荐请求序列化器"""
    
    target_type = serializers.CharField(default='product')
    limit = serializers.IntegerField(default=10, min_value=1, max_value=50)
    context = serializers.JSONField(required=False)


class RecommendationResponseSerializer(serializers.Serializer):
    """推荐响应序列化器"""
    
    target_id = serializers.CharField()
    target_type = serializers.CharField()
    score = serializers.FloatField()
    reason = serializers.CharField()
    algorithm = serializers.CharField()
    config_name = serializers.CharField()


class BehaviorLogRequestSerializer(serializers.Serializer):
    """行为日志请求序列化器"""
    
    session_id = serializers.CharField()
    action_type = serializers.ChoiceField(choices=UserBehaviorLog.ACTION_TYPES)
    target_type = serializers.CharField()
    target_id = serializers.CharField()
    context_data = serializers.JSONField(required=False)


class BehaviorStatsResponseSerializer(serializers.Serializer):
    """行为统计响应序列化器"""
    
    total_behaviors = serializers.IntegerField()
    action_stats = serializers.ListField()
    target_stats = serializers.ListField()
    period_days = serializers.IntegerField()


class PopularTargetsResponseSerializer(serializers.Serializer):
    """热门目标响应序列化器"""
    
    target_id = serializers.CharField()
    view_count = serializers.IntegerField()


class TaskCreateRequestSerializer(serializers.Serializer):
    """任务创建请求序列化器"""
    
    name = serializers.CharField()
    task_type = serializers.ChoiceField(choices=AITask.TASK_TYPES)
    parameters = serializers.JSONField(required=False)


class TaskOperationResponseSerializer(serializers.Serializer):
    """任务操作响应序列化器"""
    
    success = serializers.BooleanField()
    message = serializers.CharField()
    task_id = serializers.IntegerField(required=False)


class AIStatusResponseSerializer(serializers.Serializer):
    """AI状态响应序列化器"""
    
    services = serializers.DictField()
    active_tasks = serializers.IntegerField()
    total_conversations = serializers.IntegerField()
    system_status = serializers.CharField()


class AIHealthCheckResponseSerializer(serializers.Serializer):
    """AI健康检查响应序列化器"""
    
    status = serializers.CharField()
    services = serializers.DictField()
    database = serializers.CharField()
    timestamp = serializers.DateTimeField()
