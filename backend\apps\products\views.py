from django.shortcuts import render
from django.db.models import Q, F
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend
from .models import (
    ProductCategory, 
    Product, 
    ProductImage, 
    ProductAttribute, 
    ProductAttributeValue,
    ProductReview
)
from .serializers import (
    ProductCategorySerializer,
    ProductListSerializer,
    ProductDetailSerializer,
    ProductCreateUpdateSerializer,
    ProductImageSerializer,
    ProductAttributeSerializer,
    ProductReviewSerializer,
    ProductSearchSerializer
)


class ProductCategoryViewSet(viewsets.ModelViewSet):
    """产品分类视图集"""
    queryset = ProductCategory.objects.filter(is_deleted=False, is_active=True)
    serializer_class = ProductCategorySerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['parent', 'is_active']
    ordering_fields = ['sort_order', 'created_at']
    ordering = ['sort_order', 'id']
    
    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()
        
        # 只获取顶级分类
        if self.request.query_params.get('top_level'):
            queryset = queryset.filter(parent__isnull=True)
        
        return queryset
    
    @action(detail=True, methods=['get'])
    def products(self, request, pk=None):
        """获取分类下的产品"""
        category = self.get_object()
        products = Product.objects.filter(
            category=category, 
            status='active',
            is_deleted=False
        ).order_by('-is_featured', 'sort_order', '-created_at')
        
        serializer = ProductListSerializer(products, many=True, context={'request': request})
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })


class ProductViewSet(viewsets.ModelViewSet):
    """产品视图集"""
    queryset = Product.objects.filter(is_deleted=False)
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'subtitle', 'description', 'tags', 'sku']
    filterset_fields = ['category', 'status', 'is_featured']
    ordering_fields = ['price', 'created_at', 'view_count', 'sale_count', 'sort_order']
    ordering = ['-is_featured', 'sort_order', '-created_at']
    
    def get_serializer_class(self):
        """根据操作选择序列化器"""
        if self.action == 'list':
            return ProductListSerializer
        elif self.action == 'retrieve':
            return ProductDetailSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return ProductCreateUpdateSerializer
        return ProductDetailSerializer
    
    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()
        
        # 前台只显示上架商品
        if not self.request.user.is_staff:
            queryset = queryset.filter(status='active')
        
        return queryset
    
    def retrieve(self, request, *args, **kwargs):
        """获取产品详情，增加浏览量"""
        instance = self.get_object()
        
        # 增加浏览量
        Product.objects.filter(id=instance.id).update(view_count=F('view_count') + 1)
        
        serializer = self.get_serializer(instance)
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })
    
    def list(self, request, *args, **kwargs):
        """产品列表"""
        response = super().list(request, *args, **kwargs)
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': response.data
        })
    
    def create(self, request, *args, **kwargs):
        """创建产品"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        
        return Response({
            'code': 201,
            'message': '创建成功',
            'data': serializer.data
        }, status=status.HTTP_201_CREATED)
    
    def update(self, request, *args, **kwargs):
        """更新产品"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        
        return Response({
            'code': 200,
            'message': '更新成功',
            'data': serializer.data
        })
    
    @action(detail=False, methods=['get'])
    def search(self, request):
        """产品搜索"""
        search_serializer = ProductSearchSerializer(data=request.query_params)
        search_serializer.is_valid(raise_exception=True)
        
        queryset = self.get_queryset()
        search_data = search_serializer.validated_data
        
        # 关键词搜索
        if search_data.get('keyword'):
            keyword = search_data['keyword']
            queryset = queryset.filter(
                Q(name__icontains=keyword) |
                Q(subtitle__icontains=keyword) |
                Q(description__icontains=keyword) |
                Q(tags__icontains=keyword)
            )
        
        # 分类筛选
        if search_data.get('category'):
            queryset = queryset.filter(category_id=search_data['category'])
        
        # 价格筛选
        if search_data.get('min_price'):
            queryset = queryset.filter(price__gte=search_data['min_price'])
        if search_data.get('max_price'):
            queryset = queryset.filter(price__lte=search_data['max_price'])
        
        # 推荐商品筛选
        if search_data.get('is_featured') is not None:
            queryset = queryset.filter(is_featured=search_data['is_featured'])
        
        # 排序
        if search_data.get('sort_by'):
            queryset = queryset.order_by(search_data['sort_by'])
        
        # 分页
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = ProductListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response({
                'code': 200,
                'message': '搜索成功',
                'data': serializer.data
            })
        
        serializer = ProductListSerializer(queryset, many=True, context={'request': request})
        return Response({
            'code': 200,
            'message': '搜索成功',
            'data': serializer.data
        })
    
    @action(detail=False, methods=['get'])
    def featured(self, request):
        """获取推荐商品"""
        queryset = self.get_queryset().filter(is_featured=True)[:10]
        serializer = ProductListSerializer(queryset, many=True, context={'request': request})
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })
    
    @action(detail=False, methods=['get'])
    def latest(self, request):
        """获取最新商品"""
        queryset = self.get_queryset().order_by('-created_at')[:10]
        serializer = ProductListSerializer(queryset, many=True, context={'request': request})
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })
    
    @action(detail=False, methods=['get'])
    def hot(self, request):
        """获取热销商品"""
        queryset = self.get_queryset().order_by('-sale_count', '-view_count')[:10]
        serializer = ProductListSerializer(queryset, many=True, context={'request': request})
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })


class ProductImageViewSet(viewsets.ModelViewSet):
    """产品图片视图集"""
    queryset = ProductImage.objects.filter(is_deleted=False)
    serializer_class = ProductImageSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['product', 'is_primary']
    ordering = ['-is_primary', 'sort_order', 'id']
    
    def create(self, request, *args, **kwargs):
        """创建产品图片"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        
        return Response({
            'code': 201,
            'message': '上传成功',
            'data': serializer.data
        }, status=status.HTTP_201_CREATED)


class ProductAttributeViewSet(viewsets.ModelViewSet):
    """产品属性视图集"""
    queryset = ProductAttribute.objects.filter(is_deleted=False)
    serializer_class = ProductAttributeSerializer
    permission_classes = [IsAuthenticated]
    ordering = ['sort_order', 'id']


class ProductReviewViewSet(viewsets.ModelViewSet):
    """产品评价视图集"""
    queryset = ProductReview.objects.filter(is_deleted=False, is_approved=True)
    serializer_class = ProductReviewSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['product', 'rating', 'is_verified_purchase']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()
        
        # 用户只能看到已审核的评价
        if not self.request.user.is_staff:
            queryset = queryset.filter(is_approved=True)
        
        return queryset
    
    def perform_create(self, serializer):
        """创建评价时设置用户"""
        serializer.save(user=self.request.user)
    
    def create(self, request, *args, **kwargs):
        """创建产品评价"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        
        return Response({
            'code': 201,
            'message': '评价提交成功',
            'data': serializer.data
        }, status=status.HTTP_201_CREATED)
    
    @action(detail=True, methods=['post'])
    def helpful(self, request, pk=None):
        """标记评价有用"""
        review = self.get_object()
        ProductReview.objects.filter(id=review.id).update(helpful_count=F('helpful_count') + 1)
        
        return Response({
            'code': 200,
            'message': '操作成功',
            'data': {'helpful_count': review.helpful_count + 1}
        })
