# -*- coding: utf-8 -*-
"""
智梦科技系统 - AI功能视图

AI功能模块的API视图
"""

import logging
from typing import Dict, Any
from django.utils import timezone
from django.db.models import Count, Q
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated, AllowAny
from apps.core.permissions import IsOwnerOrReadOnly
from apps.core.utils import ResponseUtils
from .models import (
    AIServiceConfig, ChatbotConversation, ChatbotMessage,
    RecommendationConfig, UserBehaviorLog, AITask
)
from .serializers import (
    AIServiceConfigSerializer, ChatbotConversationSerializer, ChatbotMessageSerializer,
    RecommendationConfigSerializer, UserBehaviorLogSerializer, AITaskSerializer,
    ChatbotStartResponseSerializer, ChatbotMessageRequestSerializer, ChatbotMessageResponseSerializer,
    RecommendationRequestSerializer, RecommendationResponseSerializer,
    BehaviorLogRequestSerializer, BehaviorStatsResponseSerializer,
    PopularTargetsResponseSerializer, TaskCreateRequestSerializer, TaskOperationResponseSerializer,
    AIStatusResponseSerializer, AIHealthCheckResponseSerializer
)
from .services import ai_service_manager, AITaskManager, BehaviorAnalyzer
from .chatbot import chatbot_manager
from .recommendation import recommendation_engine

logger = logging.getLogger(__name__)


class AIServiceConfigViewSet(viewsets.ModelViewSet):
    """AI服务配置视图集"""
    
    queryset = AIServiceConfig.objects.all()
    serializer_class = AIServiceConfigSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()
        
        # 按服务类型过滤
        service_type = self.request.query_params.get('service_type')
        if service_type:
            queryset = queryset.filter(service_type=service_type)
        
        # 按状态过滤
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        return queryset.order_by('-created_at')
    
    @action(detail=False, methods=['post'])
    def reload_services(self, request):
        """重新加载服务配置"""
        try:
            ai_service_manager.reload_services()
            return Response({
                'success': True,
                'message': '服务配置已重新加载'
            })
        except Exception as e:
            logger.error(f"重新加载服务配置失败: {e}")
            return Response({
                'success': False,
                'message': f'重新加载失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ChatbotConversationViewSet(viewsets.ModelViewSet):
    """智能客服对话视图集"""
    
    queryset = ChatbotConversation.objects.all()
    serializer_class = ChatbotConversationSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()
        
        # 只返回当前用户的对话
        if not self.request.user.is_staff:
            queryset = queryset.filter(user=self.request.user)
        
        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.select_related('user').prefetch_related('messages').order_by('-start_time')


class UserBehaviorLogViewSet(viewsets.ReadOnlyModelViewSet):
    """用户行为日志视图集"""
    
    queryset = UserBehaviorLog.objects.all()
    serializer_class = UserBehaviorLogSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()
        
        # 只返回当前用户的行为日志
        if not self.request.user.is_staff:
            queryset = queryset.filter(user=self.request.user)
        
        # 按行为类型过滤
        action_type = self.request.query_params.get('action_type')
        if action_type:
            queryset = queryset.filter(action_type=action_type)
        
        # 按目标类型过滤
        target_type = self.request.query_params.get('target_type')
        if target_type:
            queryset = queryset.filter(target_type=target_type)
        
        return queryset.select_related('user').order_by('-created_at')


class AITaskViewSet(viewsets.ModelViewSet):
    """AI任务视图集"""
    
    queryset = AITask.objects.all()
    serializer_class = AITaskSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()
        
        # 按任务类型过滤
        task_type = self.request.query_params.get('task_type')
        if task_type:
            queryset = queryset.filter(task_type=task_type)
        
        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.order_by('-created_at')
    
    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """启动任务"""
        task = self.get_object()
        success = AITaskManager.start_task(task.id)
        
        if success:
            return Response({
                'success': True,
                'message': '任务已启动'
            })
        else:
            return Response({
                'success': False,
                'message': '任务启动失败'
            }, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """完成任务"""
        task = self.get_object()
        result_data = request.data.get('result_data', {})
        success = AITaskManager.complete_task(task.id, result_data)
        
        if success:
            return Response({
                'success': True,
                'message': '任务已完成'
            })
        else:
            return Response({
                'success': False,
                'message': '任务完成失败'
            }, status=status.HTTP_400_BAD_REQUEST)


# 智能客服相关视图
class ChatbotStartView(APIView):
    """启动智能客服对话"""
    
    permission_classes = [AllowAny]
    
    def post(self, request):
        """启动新的对话会话"""
        try:
            user_id = request.user.id if request.user.is_authenticated else None
            conversation = chatbot_manager.create_conversation(user_id)
            
            # 获取欢迎消息
            welcome_message = conversation.messages.first()
            
            response_data = {
                'session_id': conversation.session_id,
                'welcome_message': welcome_message.content if welcome_message else '',
                'created_at': conversation.start_time
            }
            
            serializer = ChatbotStartResponseSerializer(response_data)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"启动智能客服对话失败: {e}")
            return ResponseUtils.error_response('启动对话失败')


class ChatbotMessageView(APIView):
    """处理智能客服消息"""
    
    permission_classes = [AllowAny]
    
    def post(self, request):
        """处理用户消息"""
        serializer = ChatbotMessageRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return ResponseUtils.error_response('请求参数无效', serializer.errors)
        
        try:
            session_id = serializer.validated_data['session_id']
            user_message = serializer.validated_data['message']
            
            result = chatbot_manager.process_user_message(session_id, user_message)
            
            if result['success']:
                response_serializer = ChatbotMessageResponseSerializer(result)
                return Response(response_serializer.data)
            else:
                return ResponseUtils.error_response(result.get('error', '处理消息失败'))
                
        except Exception as e:
            logger.error(f"处理智能客服消息失败: {e}")
            return ResponseUtils.error_response('处理消息失败')


class ChatbotCloseView(APIView):
    """关闭智能客服对话"""
    
    permission_classes = [AllowAny]
    
    def post(self, request):
        """关闭对话会话"""
        session_id = request.data.get('session_id')
        if not session_id:
            return ResponseUtils.error_response('缺少会话ID')
        
        try:
            success = chatbot_manager.close_conversation(session_id)
            
            if success:
                return Response({
                    'success': True,
                    'message': '对话已结束'
                })
            else:
                return ResponseUtils.error_response('关闭对话失败')
                
        except Exception as e:
            logger.error(f"关闭智能客服对话失败: {e}")
            return ResponseUtils.error_response('关闭对话失败')


class ChatbotHistoryView(APIView):
    """获取智能客服对话历史"""
    
    permission_classes = [AllowAny]
    
    def get(self, request, session_id):
        """获取对话历史"""
        try:
            history = chatbot_manager.get_conversation_history(session_id)
            return Response({
                'session_id': session_id,
                'messages': history
            })
            
        except Exception as e:
            logger.error(f"获取对话历史失败: {e}")
            return ResponseUtils.error_response('获取对话历史失败')


# 推荐系统相关视图
class RecommendationView(APIView):
    """获取推荐结果"""

    permission_classes = [AllowAny]

    def post(self, request):
        """获取推荐"""
        serializer = RecommendationRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return ResponseUtils.error_response('请求参数无效', serializer.errors)

        try:
            user_id = request.user.id if request.user.is_authenticated else None
            target_type = serializer.validated_data['target_type']
            limit = serializer.validated_data['limit']
            context = serializer.validated_data.get('context', {})

            recommendations = recommendation_engine.get_recommendations(
                user_id=user_id,
                target_type=target_type,
                limit=limit,
                context=context
            )

            response_serializer = RecommendationResponseSerializer(recommendations, many=True)
            return Response(response_serializer.data)

        except Exception as e:
            logger.error(f"获取推荐失败: {e}")
            return ResponseUtils.error_response('获取推荐失败')


class RecommendationConfigView(APIView):
    """推荐配置管理"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取推荐配置"""
        configs = RecommendationConfig.objects.filter(is_active=True)
        serializer = RecommendationConfigSerializer(configs, many=True)
        return Response(serializer.data)


# 用户行为分析相关视图
class BehaviorLogView(APIView):
    """记录用户行为"""

    permission_classes = [AllowAny]

    def post(self, request):
        """记录行为日志"""
        serializer = BehaviorLogRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return ResponseUtils.error_response('请求参数无效', serializer.errors)

        try:
            # 获取客户端信息
            ip_address = self._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')

            # 创建行为日志
            behavior_log = UserBehaviorLog.objects.create(
                user=request.user if request.user.is_authenticated else None,
                session_id=serializer.validated_data['session_id'],
                action_type=serializer.validated_data['action_type'],
                target_type=serializer.validated_data['target_type'],
                target_id=serializer.validated_data['target_id'],
                context_data=serializer.validated_data.get('context_data', {}),
                ip_address=ip_address,
                user_agent=user_agent
            )

            return Response({
                'success': True,
                'log_id': behavior_log.id
            })

        except Exception as e:
            logger.error(f"记录用户行为失败: {e}")
            return ResponseUtils.error_response('记录行为失败')

    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class BehaviorStatsView(APIView):
    """用户行为统计"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取行为统计"""
        try:
            user_id = request.user.id
            days = int(request.query_params.get('days', 30))

            stats = BehaviorAnalyzer.get_user_behavior_stats(user_id, days)

            response_data = {
                'total_behaviors': stats.get('total_behaviors', 0),
                'action_stats': stats.get('action_stats', []),
                'target_stats': stats.get('target_stats', []),
                'period_days': days
            }

            serializer = BehaviorStatsResponseSerializer(response_data)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"获取行为统计失败: {e}")
            return ResponseUtils.error_response('获取统计失败')


class PopularTargetsView(APIView):
    """热门目标统计"""

    permission_classes = [AllowAny]

    def get(self, request):
        """获取热门目标"""
        try:
            target_type = request.query_params.get('target_type', 'product')
            days = int(request.query_params.get('days', 7))
            limit = int(request.query_params.get('limit', 10))

            popular_targets = BehaviorAnalyzer.get_popular_targets(target_type, days, limit)

            serializer = PopularTargetsResponseSerializer(popular_targets, many=True)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"获取热门目标失败: {e}")
            return ResponseUtils.error_response('获取热门目标失败')


# AI任务管理相关视图
class CreateTaskView(APIView):
    """创建AI任务"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """创建新任务"""
        serializer = TaskCreateRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return ResponseUtils.error_response('请求参数无效', serializer.errors)

        try:
            task_id = AITaskManager.create_task(
                name=serializer.validated_data['name'],
                task_type=serializer.validated_data['task_type'],
                parameters=serializer.validated_data.get('parameters', {})
            )

            response_data = {
                'success': True,
                'message': '任务创建成功',
                'task_id': task_id
            }

            response_serializer = TaskOperationResponseSerializer(response_data)
            return Response(response_serializer.data)

        except Exception as e:
            logger.error(f"创建AI任务失败: {e}")
            return ResponseUtils.error_response('创建任务失败')


class StartTaskView(APIView):
    """启动AI任务"""

    permission_classes = [IsAuthenticated]

    def post(self, request, task_id):
        """启动指定任务"""
        try:
            success = AITaskManager.start_task(task_id)

            response_data = {
                'success': success,
                'message': '任务启动成功' if success else '任务启动失败'
            }

            response_serializer = TaskOperationResponseSerializer(response_data)
            return Response(response_serializer.data)

        except Exception as e:
            logger.error(f"启动AI任务失败: {e}")
            return ResponseUtils.error_response('启动任务失败')


class CompleteTaskView(APIView):
    """完成AI任务"""

    permission_classes = [IsAuthenticated]

    def post(self, request, task_id):
        """完成指定任务"""
        try:
            result_data = request.data.get('result_data', {})
            success = AITaskManager.complete_task(task_id, result_data)

            response_data = {
                'success': success,
                'message': '任务完成成功' if success else '任务完成失败'
            }

            response_serializer = TaskOperationResponseSerializer(response_data)
            return Response(response_serializer.data)

        except Exception as e:
            logger.error(f"完成AI任务失败: {e}")
            return ResponseUtils.error_response('完成任务失败')


class FailTaskView(APIView):
    """标记AI任务失败"""

    permission_classes = [IsAuthenticated]

    def post(self, request, task_id):
        """标记任务失败"""
        try:
            error_message = request.data.get('error_message', '')
            success = AITaskManager.fail_task(task_id, error_message)

            response_data = {
                'success': success,
                'message': '任务状态更新成功' if success else '任务状态更新失败'
            }

            response_serializer = TaskOperationResponseSerializer(response_data)
            return Response(response_serializer.data)

        except Exception as e:
            logger.error(f"标记AI任务失败: {e}")
            return ResponseUtils.error_response('更新任务状态失败')


# 系统状态相关视图
class AIStatusView(APIView):
    """AI系统状态"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取AI系统状态"""
        try:
            # 获取服务状态
            services = {}
            for config in AIServiceConfig.objects.filter(is_active=True):
                services[config.name] = {
                    'type': config.service_type,
                    'status': 'active' if config.is_active else 'inactive'
                }

            # 获取任务统计
            active_tasks = AITask.objects.filter(status__in=['pending', 'running']).count()

            # 获取对话统计
            total_conversations = ChatbotConversation.objects.count()

            response_data = {
                'services': services,
                'active_tasks': active_tasks,
                'total_conversations': total_conversations,
                'system_status': 'healthy'
            }

            serializer = AIStatusResponseSerializer(response_data)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"获取AI系统状态失败: {e}")
            return ResponseUtils.error_response('获取系统状态失败')


class AIHealthCheckView(APIView):
    """AI系统健康检查"""

    permission_classes = [AllowAny]

    def get(self, request):
        """健康检查"""
        try:
            # 检查数据库连接
            database_status = 'healthy'
            try:
                AIServiceConfig.objects.count()
            except Exception:
                database_status = 'unhealthy'

            # 检查各个服务状态
            services = {}
            for config in AIServiceConfig.objects.filter(is_active=True):
                # 这里可以添加实际的服务健康检查逻辑
                services[config.name] = 'healthy'

            overall_status = 'healthy' if database_status == 'healthy' else 'unhealthy'

            response_data = {
                'status': overall_status,
                'services': services,
                'database': database_status,
                'timestamp': timezone.now()
            }

            serializer = AIHealthCheckResponseSerializer(response_data)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"AI系统健康检查失败: {e}")
            return Response({
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': timezone.now()
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
