from django.test import TestCase
from django.contrib.auth import get_user_model
from apps.products.models import ProductCategory, Product, ProductImage

User = get_user_model()

class ProductCategoryModelTest(TestCase):
    """产品分类模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.category = ProductCategory.objects.create(
            name='电子产品',
            description='电子类产品',
            sort_order=1,
            is_active=True
        )
    
    def test_category_creation(self):
        """测试分类创建"""
        self.assertEqual(self.category.name, '电子产品')
        self.assertEqual(self.category.description, '电子类产品')
        self.assertTrue(self.category.is_active)
        self.assertEqual(self.category.sort_order, 1)
    
    def test_category_str_representation(self):
        """测试分类字符串表示"""
        self.assertEqual(str(self.category), '电子产品')
    
    def test_category_full_name(self):
        """测试分类完整名称"""
        parent_category = ProductCategory.objects.create(
            name='手机',
            parent=self.category
        )
        self.assertEqual(parent_category.get_full_name(), '电子产品 > 手机')


class ProductModelTest(TestCase):
    """产品模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.category = ProductCategory.objects.create(
            name='电子产品'
        )
        self.product = Product.objects.create(
            name='iPhone 15',
            subtitle='全新一代智能手机',
            description='苹果最新款智能手机',
            category=self.category,
            sku='IP15-256G-BLK',
            price=7999.00,
            original_price=8999.00,
            stock=100,
            status='active'
        )
    
    def test_product_creation(self):
        """测试产品创建"""
        self.assertEqual(self.product.name, 'iPhone 15')
        self.assertEqual(self.product.sku, 'IP15-256G-BLK')
        self.assertEqual(self.product.price, 7999.00)
        self.assertEqual(self.product.stock, 100)
        self.assertTrue(self.product.is_in_stock())
    
    def test_product_str_representation(self):
        """测试产品字符串表示"""
        self.assertEqual(str(self.product), 'iPhone 15')
    
    def test_product_discount_rate(self):
        """测试产品折扣率计算"""
        discount_rate = self.product.get_discount_rate()
        self.assertEqual(discount_rate, 11)  # (1 - 7999/8999) * 100 ≈ 11%
    
    def test_product_low_stock(self):
        """测试产品低库存检查"""
        self.product.stock = 5
        self.product.min_stock = 10
        self.product.save()
        self.assertTrue(self.product.is_low_stock())


class ProductImageModelTest(TestCase):
    """产品图片模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.category = ProductCategory.objects.create(
            name='电子产品'
        )
        self.product = Product.objects.create(
            name='iPhone 15',
            category=self.category,
            sku='IP15-256G-BLK',
            price=7999.00
        )
        self.image = ProductImage.objects.create(
            product=self.product,
            alt_text='iPhone 15正面图',
            is_primary=True,
            sort_order=1
        )
    
    def test_image_creation(self):
        """测试图片创建"""
        self.assertEqual(self.image.product, self.product)
        self.assertEqual(self.image.alt_text, 'iPhone 15正面图')
        self.assertTrue(self.image.is_primary)
    
    def test_image_str_representation(self):
        """测试图片字符串表示"""
        expected = f"{self.product.name} - 图片{self.image.id}"
        self.assertEqual(str(self.image), expected)
