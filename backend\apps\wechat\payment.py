"""
微信支付模块
"""
import hashlib
import time
import uuid
import xml.etree.ElementTree as ET
import requests
from django.conf import settings
from .models import WechatConfig
from django.utils import timezone
from apps.orders.models import Order


class WechatPayment:
    """微信支付类"""
    
    def __init__(self, config_name='default'):
        # 从数据库获取配置
        config = WechatConfig.get_config('payment', config_name)
        self.app_id = config.get('app_id', '')
        self.mch_id = config.get('mch_id', '')
        self.mch_key = config.get('mch_key', '')
        self.cert_path = config.get('cert_path', '')
        self.key_path = config.get('key_path', '')
        self.api_base = 'https://api.mch.weixin.qq.com'
    
    def generate_sign(self, params):
        """生成签名"""
        # 排序参数
        sorted_params = sorted(params.items())
        # 拼接字符串
        string_a = '&'.join([f"{k}={v}" for k, v in sorted_params if v])
        # 添加key
        string_sign_temp = f"{string_a}&key={self.mch_key}"
        # MD5加密并转大写
        sign = hashlib.md5(string_sign_temp.encode('utf-8')).hexdigest().upper()
        return sign
    
    def dict_to_xml(self, data):
        """字典转XML"""
        xml = '<xml>'
        for k, v in data.items():
            xml += f'<{k}><![CDATA[{v}]]></{k}>'
        xml += '</xml>'
        return xml
    
    def xml_to_dict(self, xml_data):
        """XML转字典"""
        root = ET.fromstring(xml_data)
        result = {}
        for child in root:
            result[child.tag] = child.text
        return result
    
    def unified_order(self, order_id, total_fee, openid, body, notify_url):
        """统一下单"""
        # 生成随机字符串
        nonce_str = str(uuid.uuid4()).replace('-', '')
        
        # 构建参数
        params = {
            'appid': self.app_id,
            'mch_id': self.mch_id,
            'nonce_str': nonce_str,
            'body': body,
            'out_trade_no': order_id,
            'total_fee': str(int(total_fee * 100)),  # 转换为分
            'spbill_create_ip': '127.0.0.1',
            'notify_url': notify_url,
            'trade_type': 'JSAPI',
            'openid': openid,
        }
        
        # 生成签名
        params['sign'] = self.generate_sign(params)
        
        # 转换为XML
        xml_data = self.dict_to_xml(params)
        
        # 发送请求
        url = f"{self.api_base}/pay/unifiedorder"
        response = requests.post(url, data=xml_data.encode('utf-8'), 
                               headers={'Content-Type': 'application/xml'})
        
        # 解析响应
        result = self.xml_to_dict(response.text)
        
        if result.get('return_code') != 'SUCCESS':
            raise Exception(f"统一下单失败: {result.get('return_msg', '未知错误')}")
        
        if result.get('result_code') != 'SUCCESS':
            raise Exception(f"统一下单失败: {result.get('err_code_des', '未知错误')}")
        
        return result
    
    def generate_js_pay_params(self, prepay_id):
        """生成JS支付参数"""
        timestamp = str(int(time.time()))
        nonce_str = str(uuid.uuid4()).replace('-', '')
        
        params = {
            'appId': self.app_id,
            'timeStamp': timestamp,
            'nonceStr': nonce_str,
            'package': f'prepay_id={prepay_id}',
            'signType': 'MD5',
        }
        
        # 生成签名
        params['paySign'] = self.generate_sign(params)
        
        return params
    
    def create_payment(self, order_id, openid, notify_url):
        """创建支付订单"""
        try:
            # 获取订单信息
            order = Order.objects.get(order_no=order_id)
            
            # 统一下单
            result = self.unified_order(
                order_id=order.order_no,
                total_fee=float(order.total_amount),
                openid=openid,
                body=f"智梦科技-订单{order.order_no}",
                notify_url=notify_url
            )
            
            # 生成JS支付参数
            js_params = self.generate_js_pay_params(result['prepay_id'])
            
            return {
                'success': True,
                'data': js_params,
                'prepay_id': result['prepay_id']
            }
            
        except Order.DoesNotExist:
            return {
                'success': False,
                'error': '订单不存在'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def handle_notify(self, xml_data):
        """处理支付回调"""
        try:
            # 解析XML数据
            data = self.xml_to_dict(xml_data)
            
            # 验证签名
            sign = data.pop('sign', '')
            if sign != self.generate_sign(data):
                return {
                    'success': False,
                    'error': '签名验证失败'
                }
            
            # 检查支付结果
            if data.get('return_code') != 'SUCCESS' or data.get('result_code') != 'SUCCESS':
                return {
                    'success': False,
                    'error': '支付失败'
                }
            
            # 更新订单状态
            order_no = data.get('out_trade_no')
            transaction_id = data.get('transaction_id')
            total_fee = int(data.get('total_fee', 0)) / 100  # 转换为元
            
            try:
                order = Order.objects.get(order_no=order_no)
                if order.payment_status != 'paid':
                    order.payment_status = 'paid'
                    order.payment_method = 'wechat'
                    order.transaction_id = transaction_id
                    order.paid_at = timezone.now()
                    order.save()
                
                return {
                    'success': True,
                    'order': order
                }
                
            except Order.DoesNotExist:
                return {
                    'success': False,
                    'error': '订单不存在'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def query_order(self, order_no):
        """查询订单支付状态"""
        nonce_str = str(uuid.uuid4()).replace('-', '')
        
        params = {
            'appid': self.app_id,
            'mch_id': self.mch_id,
            'out_trade_no': order_no,
            'nonce_str': nonce_str,
        }
        
        # 生成签名
        params['sign'] = self.generate_sign(params)
        
        # 转换为XML
        xml_data = self.dict_to_xml(params)
        
        # 发送请求
        url = f"{self.api_base}/pay/orderquery"
        response = requests.post(url, data=xml_data.encode('utf-8'),
                               headers={'Content-Type': 'application/xml'})
        
        # 解析响应
        result = self.xml_to_dict(response.text)
        
        return result
