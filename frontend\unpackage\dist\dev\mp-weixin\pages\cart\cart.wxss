@import "../../uvue.wxss";
:host{display:flex;flex-direction:column}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 图标相关变量 */
/* 图标颜色预设 */
/* 全局图标基础样式 */
.icon-base.data-v-70b3dcf8 {
  font-family: "iconfont";
  font-style: normal;
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
}

/* 图标动画 */
@keyframes icon-rotate-70b3dcf8 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
.icon-spin.data-v-70b3dcf8 {
  animation: icon-rotate-70b3dcf8 1s linear infinite;
}
.container.data-v-70b3dcf8 {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 60px;
}
.cart-list.data-v-70b3dcf8 {
  padding: 10px 15px;
}
.cart-list .cart-item.data-v-70b3dcf8 {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
}
.cart-list .cart-item .item-checkbox.data-v-70b3dcf8 {
  margin-right: 12px;
}
.cart-list .cart-item .item-image.data-v-70b3dcf8 {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  margin-right: 12px;
}
.cart-list .cart-item .item-info.data-v-70b3dcf8 {
  flex: 1;
}
.cart-list .cart-item .item-info .item-name.data-v-70b3dcf8 {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.cart-list .cart-item .item-info .item-spec.data-v-70b3dcf8 {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}
.cart-list .cart-item .item-info .item-price .price-text.data-v-70b3dcf8 {
  font-size: 16px;
  font-weight: 600;
  color: #ff3b30;
}
.cart-list .cart-item .item-actions.data-v-70b3dcf8 {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.cart-list .cart-item .item-actions .quantity-control.data-v-70b3dcf8 {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.cart-list .cart-item .item-actions .quantity-control .quantity-btn.data-v-70b3dcf8 {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  border-radius: 4px;
}
.cart-list .cart-item .item-actions .quantity-control .quantity-btn.data-v-70b3dcf8:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.cart-list .cart-item .item-actions .quantity-control .quantity-btn.data-v-70b3dcf8:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}
.cart-list .cart-item .item-actions .quantity-control .quantity-text.data-v-70b3dcf8 {
  width: 40px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  font-size: 14px;
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  background-color: #f8f8f8;
}
.cart-list .cart-item .item-actions .delete-btn.data-v-70b3dcf8 {
  padding: 4px;
}
.empty-cart.data-v-70b3dcf8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}
.empty-cart .empty-text.data-v-70b3dcf8 {
  font-size: 16px;
  color: #999;
  margin: 20px 0 30px;
}
.empty-cart .go-shopping-btn.data-v-70b3dcf8 {
  width: 120px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #007aff;
  border-radius: 20px;
}
.empty-cart .go-shopping-btn .btn-text.data-v-70b3dcf8 {
  font-size: 14px;
  color: #fff;
}
.bottom-bar.data-v-70b3dcf8 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-top: 1px solid #eee;
  padding: 0 15px;
}
.bottom-bar .select-all.data-v-70b3dcf8 {
  display: flex;
  align-items: center;
  margin-right: 20px;
}
.bottom-bar .select-all .select-text.data-v-70b3dcf8 {
  font-size: 14px;
  color: #333;
  margin-left: 8px;
}
.bottom-bar .total-info.data-v-70b3dcf8 {
  flex: 1;
  text-align: right;
  margin-right: 15px;
}
.bottom-bar .total-info .total-text.data-v-70b3dcf8 {
  font-size: 14px;
  color: #333;
}
.bottom-bar .total-info .total-price.data-v-70b3dcf8 {
  font-size: 18px;
  font-weight: 600;
  color: #ff3b30;
}
.bottom-bar .checkout-btn.data-v-70b3dcf8 {
  width: 100px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #007aff;
  border-radius: 20px;
}
.bottom-bar .checkout-btn.disabled.data-v-70b3dcf8 {
  background-color: #ccc;
}
.bottom-bar .checkout-btn .btn-text.data-v-70b3dcf8 {
  font-size: 14px;
  color: #fff;
}