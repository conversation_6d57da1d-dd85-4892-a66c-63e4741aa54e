{"version": 3, "file": "config.js", "sources": ["utils/config.ts"], "sourcesContent": ["/**\n * 环境配置管理\n * 统一管理不同环境的域名和配置\n */\n\n// 环境配置接口\nexport interface EnvConfig {\n  API_BASE_URL: string      // API基础地址\n  CDN_BASE_URL: string      // CDN基础地址\n  WS_BASE_URL?: string      // WebSocket地址(如需要)\n  APP_NAME: string          // 应用名称\n  VERSION: string           // 版本号\n}\n\n// 域名配置\nconst MAIN_DOMAIN = 'zmkj.nat100.top'  // 主域名\nconst CDN_DOMAIN = 'zmkj.nat100.top'   // 图片域名\n\n// 统一配置\nconst UNIFIED_CONFIG: EnvConfig = {\n  API_BASE_URL: `http://${MAIN_DOMAIN}/api/v1`,\n  CDN_BASE_URL: `http://${CDN_DOMAIN}`,\n  WS_BASE_URL: `ws://${MAIN_DOMAIN}/ws`,\n  APP_NAME: '智梦科技系统',\n  VERSION: '1.0.0'\n}\n\n// 开发环境配置\nconst DEV_CONFIG: EnvConfig = UNIFIED_CONFIG\n\n// 生产环境配置\nconst PROD_CONFIG: EnvConfig = UNIFIED_CONFIG\n\n// 测试环境配置\nconst TEST_CONFIG: EnvConfig = UNIFIED_CONFIG\n\n/**\n * 获取当前环境\n */\nexport function getEnv(): 'development' | 'production' | 'test' {\n  // #ifdef MP-WEIXIN\n  // 微信小程序环境判断\n  const accountInfo = uni.getAccountInfoSync()\n  const envVersion = accountInfo.miniProgram.envVersion\n  \n  if (envVersion === 'release') {\n    return 'production'\n  } else if (envVersion === 'trial') {\n    return 'test'\n  } else {\n    return 'development'\n  }\n  // #endif\n  \n  // #ifdef H5\n  // H5环境判断\n  const hostname = window.location.hostname\n  if (hostname.includes('zmkj.live')) {\n    return 'production'\n  } else if (hostname.includes('test')) {\n    return 'test'\n  } else {\n    return 'development'\n  }\n  // #endif\n  \n  // #ifdef APP-PLUS\n  // App环境判断\n  // 可以通过打包时的环境变量或其他方式判断\n  return process.env.NODE_ENV === 'production' ? 'production' : 'development'\n  // #endif\n  \n  // 默认开发环境\n  return 'development'\n}\n\n/**\n * 获取当前环境配置\n */\nexport function getConfig(): EnvConfig {\n  const env = getEnv()\n  \n  switch (env) {\n    case 'production':\n      return PROD_CONFIG\n    case 'test':\n      return TEST_CONFIG\n    case 'development':\n    default:\n      return DEV_CONFIG\n  }\n}\n\n// 导出当前环境配置\nexport const CONFIG = getConfig()\n\n/**\n * 获取完整的API地址\n * @param path API路径\n * @returns 完整的API地址\n */\nexport function getApiUrl(path: string): string {\n  // 确保path以/开头\n  if (!path.startsWith('/')) {\n    path = '/' + path\n  }\n  \n  return CONFIG.API_BASE_URL + path\n}\n\n/**\n * 获取完整的CDN地址\n * @param path 资源路径\n * @returns 完整的CDN地址\n */\nexport function getCdnUrl(path: string): string {\n  if (!path) return ''\n  \n  // 如果已经是完整URL，直接返回\n  if (path.startsWith('http://') || path.startsWith('https://')) {\n    return path\n  }\n  \n  // 确保path以/开头\n  if (!path.startsWith('/')) {\n    path = '/' + path\n  }\n  \n  return CONFIG.CDN_BASE_URL + path\n}\n\n/**\n * 获取WebSocket地址\n * @param path WebSocket路径\n * @returns 完整的WebSocket地址\n */\nexport function getWsUrl(path: string = ''): string {\n  if (!CONFIG.WS_BASE_URL) {\n    console.warn('WebSocket URL not configured')\n    return ''\n  }\n  \n  // 确保path以/开头\n  if (path && !path.startsWith('/')) {\n    path = '/' + path\n  }\n  \n  return CONFIG.WS_BASE_URL + path\n}\n\n/**\n * 是否为开发环境\n */\nexport function isDev(): boolean {\n  return getEnv() === 'development'\n}\n\n/**\n * 是否为生产环境\n */\nexport function isProd(): boolean {\n  return getEnv() === 'production'\n}\n\n/**\n * 是否为测试环境\n */\nexport function isTest(): boolean {\n  return getEnv() === 'test'\n}\n\n/**\n * 打印当前环境信息\n */\nexport function printEnvInfo(): void {\n  const env = getEnv()\n  const config = getConfig()\n  \n  console.log('=== 环境信息 ===')\n  console.log('当前环境:', env)\n  console.log('应用名称:', config.APP_NAME)\n  console.log('版本号:', config.VERSION)\n  console.log('API地址:', config.API_BASE_URL)\n  console.log('CDN地址:', config.CDN_BASE_URL)\n  console.log('WebSocket地址:', config.WS_BASE_URL)\n  console.log('===============')\n}\n\n// 开发环境下打印环境信息\nif (isDev()) {\n  printEnvInfo()\n}\n\nexport default {\n  CONFIG,\n  getConfig,\n  getApiUrl,\n  getCdnUrl,\n  getWsUrl,\n  isDev,\n  isProd,\n  isTest,\n  printEnvInfo\n}\n"], "names": ["uni"], "mappings": ";;AAeA,MAAM,cAAc;AACpB,MAAM,aAAa;AAGnB,MAAM,iBAA4B;AAAA,EAChC,cAAc,UAAU,WAAW;AAAA,EACnC,cAAc,UAAU,UAAU;AAAA,EAClC,aAAa,QAAQ,WAAW;AAAA,EAChC,UAAU;AAAA,EACV,SAAS;;AAIX,MAAM,aAAwB;AAG9B,MAAM,cAAyB;AAG/B,MAAM,cAAyB;SAKf,SAAM;AAGpB,QAAM,cAAcA,oBAAI;AACxB,QAAM,aAAa,YAAY,YAAY;AAE3C,MAAI,eAAe,WAAW;AAC5B,WAAO;AAAA,EACR,WAAU,eAAe,SAAS;AACjC,WAAO;AAAA,EACR,OAAM;AACL,WAAO;AAAA,EACR;AAuBH;SAKgB,YAAS;AACvB,QAAM,MAAM;AAEZ,UAAQ,KAAG;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL;AACE,aAAO;AAAA,EACV;AACH;AAGa,MAAA,SAAS,UAAW;AAO3B,SAAU,UAAU,MAAY;AAEpC,MAAI,CAAC,KAAK,WAAW,GAAG,GAAG;AACzB,WAAO,MAAM;AAAA,EACd;AAED,SAAO,OAAO,eAAe;AAC/B;SA6CgB,QAAK;AACnB,SAAO,OAAQ,MAAK;AACtB;SAmBgB,eAAY;AAC1B,QAAM,MAAM;AACZ,QAAM,SAAS;AAEfA,gBAAAA,MAAY,MAAA,OAAA,0BAAA,cAAc;AAC1BA,gBAAA,MAAA,MAAA,OAAA,0BAAY,SAAS,GAAG;AACxBA,gBAAY,MAAA,MAAA,OAAA,0BAAA,SAAS,OAAO,QAAQ;AACpCA,gBAAY,MAAA,MAAA,OAAA,0BAAA,QAAQ,OAAO,OAAO;AAClCA,gBAAA,MAAA,MAAA,OAAA,0BAAY,UAAU,OAAO,YAAY;AACzCA,gBAAA,MAAA,MAAA,OAAA,0BAAY,UAAU,OAAO,YAAY;AACzCA,gBAAY,MAAA,MAAA,OAAA,0BAAA,gBAAgB,OAAO,WAAW;AAC9CA,gBAAAA,MAAY,MAAA,OAAA,0BAAA,iBAAiB;AAC/B;AAGA,IAAI,SAAS;AACX;AACD;;;;;"}