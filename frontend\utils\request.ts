/**
 * 网络请求封装
 * 统一处理请求和响应，支持拦截器、错误处理等
 */

import { getApiUrl, isDev } from './config'
import { getToken, removeToken } from './auth'

// 请求方法类型
export type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// 请求配置接口
export interface RequestConfig {
  url: string
  method?: RequestMethod
  data?: any
  params?: Record<string, any>
  header?: Record<string, string>
  timeout?: number
  showLoading?: boolean
  loadingText?: string
  showError?: boolean
  skipAuth?: boolean
}

// 响应数据接口
export interface ResponseData<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 请求拦截器类型
export type RequestInterceptor = (config: RequestConfig) => RequestConfig | Promise<RequestConfig>

// 响应拦截器类型
export type ResponseInterceptor = (response: any) => any | Promise<any>

// 错误拦截器类型
export type ErrorInterceptor = (error: any) => any | Promise<any>

class HttpRequest {
  private requestInterceptors: RequestInterceptor[] = []
  private responseInterceptors: ResponseInterceptor[] = []
  private errorInterceptors: ErrorInterceptor[] = []
  
  // 默认配置
  private defaultConfig: Partial<RequestConfig> = {
    method: 'GET',
    timeout: 10000,
    showLoading: false,
    loadingText: '加载中...',
    showError: true,
    skipAuth: false,
    header: {
      'Content-Type': 'application/json'
    }
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor: RequestInterceptor) {
    this.requestInterceptors.push(interceptor)
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor: ResponseInterceptor) {
    this.responseInterceptors.push(interceptor)
  }

  /**
   * 添加错误拦截器
   */
  addErrorInterceptor(interceptor: ErrorInterceptor) {
    this.errorInterceptors.push(interceptor)
  }

  /**
   * 执行请求拦截器
   */
  private async runRequestInterceptors(config: RequestConfig): Promise<RequestConfig> {
    let result = config
    for (const interceptor of this.requestInterceptors) {
      result = await interceptor(result)
    }
    return result
  }

  /**
   * 执行响应拦截器
   */
  private async runResponseInterceptors(response: any): Promise<any> {
    let result = response
    for (const interceptor of this.responseInterceptors) {
      result = await interceptor(result)
    }
    return result
  }

  /**
   * 执行错误拦截器
   */
  private async runErrorInterceptors(error: any): Promise<any> {
    let result = error
    for (const interceptor of this.errorInterceptors) {
      result = await interceptor(result)
    }
    return result
  }

  /**
   * 处理URL参数
   */
  private buildUrl(url: string, params?: Record<string, any>): string {
    if (!params || Object.keys(params).length === 0) {
      return url
    }

    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')

    return url + (url.includes('?') ? '&' : '?') + queryString
  }

  /**
   * 显示加载提示
   */
  private showLoading(text: string) {
    uni.showLoading({
      title: text,
      mask: true
    })
  }

  /**
   * 隐藏加载提示
   */
  private hideLoading() {
    uni.hideLoading()
  }

  /**
   * 显示错误提示
   */
  private showError(message: string) {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  }

  /**
   * 发送请求
   */
  async request<T = any>(config: RequestConfig): Promise<ResponseData<T>> {
    // 合并默认配置
    const finalConfig = { ...this.defaultConfig, ...config }

    try {
      // 执行请求拦截器
      const interceptedConfig = await this.runRequestInterceptors(finalConfig)

      // 构建完整URL
      const fullUrl = getApiUrl(this.buildUrl(interceptedConfig.url, interceptedConfig.params))

      // 构建请求头
      const headers = { ...interceptedConfig.header }

      // 添加认证头
      if (!interceptedConfig.skipAuth) {
        const token = getToken()
        if (token) {
          headers['Authorization'] = `Bearer ${token}`
        }
      }

      // 显示加载提示
      if (interceptedConfig.showLoading) {
        this.showLoading(interceptedConfig.loadingText!)
      }

      // 发送请求
      const response = await new Promise<any>((resolve, reject) => {
        uni.request({
          url: fullUrl,
          method: interceptedConfig.method as any,
          data: interceptedConfig.data,
          header: headers,
          timeout: interceptedConfig.timeout,
          success: resolve,
          fail: reject
        })
      })

      // 隐藏加载提示
      if (interceptedConfig.showLoading) {
        this.hideLoading()
      }

      // 执行响应拦截器
      const interceptedResponse = await this.runResponseInterceptors(response)

      // 处理响应数据
      const responseData = interceptedResponse.data as ResponseData<T>

      // 检查业务状态码
      if (responseData.code !== 0) {
        throw new Error(responseData.message || '请求失败')
      }

      return responseData

    } catch (error: any) {
      // 隐藏加载提示
      if (finalConfig.showLoading) {
        this.hideLoading()
      }

      // 执行错误拦截器
      const interceptedError = await this.runErrorInterceptors(error)

      // 显示错误提示
      if (finalConfig.showError) {
        const errorMessage = interceptedError.message || '网络请求失败'
        this.showError(errorMessage)
      }

      // 开发环境下打印错误信息
      if (isDev()) {
        console.error('Request Error:', interceptedError)
      }

      throw interceptedError
    }
  }

  /**
   * GET请求
   */
  get<T = any>(url: string, params?: Record<string, any>, config?: Partial<RequestConfig>): Promise<ResponseData<T>> {
    return this.request<T>({
      url,
      method: 'GET',
      params,
      ...config
    })
  }

  /**
   * POST请求
   */
  post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ResponseData<T>> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      ...config
    })
  }

  /**
   * PUT请求
   */
  put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ResponseData<T>> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      ...config
    })
  }

  /**
   * DELETE请求
   */
  delete<T = any>(url: string, params?: Record<string, any>, config?: Partial<RequestConfig>): Promise<ResponseData<T>> {
    return this.request<T>({
      url,
      method: 'DELETE',
      params,
      ...config
    })
  }

  /**
   * PATCH请求
   */
  patch<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ResponseData<T>> {
    return this.request<T>({
      url,
      method: 'PATCH',
      data,
      ...config
    })
  }
}

// 创建请求实例
const http = new HttpRequest()

// 添加默认请求拦截器
http.addRequestInterceptor((config) => {
  // 可以在这里添加全局请求处理逻辑
  if (isDev()) {
    console.log('Request:', config)
  }
  return config
})

// 添加默认响应拦截器
http.addResponseInterceptor((response) => {
  // 可以在这里添加全局响应处理逻辑
  if (isDev()) {
    console.log('Response:', response)
  }
  return response
})

// 添加默认错误拦截器
http.addErrorInterceptor((error) => {
  // 处理特定错误
  if (error.statusCode === 401) {
    // 未授权，清除token并跳转到登录页
    removeToken()
    uni.reLaunch({
      url: '/pages/auth/login'
    })
  }
  
  return error
})

export default http
export { http }
