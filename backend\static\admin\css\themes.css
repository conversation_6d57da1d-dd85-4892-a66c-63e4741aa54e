/* 主题样式 */
/* 默认主题 - 不需要额外样式 */

/* 酷黑主题 */
body.theme-cool-black {
    background-color: #1a1a1a;
    color: #e0e0e0;
}

body.theme-cool-black #header {
    background: #0d0d0d;
    color: #fff;
}

body.theme-cool-black .module h2, 
body.theme-cool-black .module caption {
    background: #2a2a2a;
    color: #f0f0f0;
}

body.theme-cool-black .module {
    background: #333333;
    border: 1px solid #444;
}

body.theme-cool-black a:link, 
body.theme-cool-black a:visited {
    color: #64b5f6;
}

body.theme-cool-black .button, 
body.theme-cool-black input[type="submit"], 
body.theme-cool-black input[type="button"] {
    background: #424242;
    color: #fff;
}

body.theme-cool-black .button:hover, 
body.theme-cool-black input[type="submit"]:hover, 
body.theme-cool-black input[type="button"]:hover {
    background: #616161;
}

/* 雅蓝主题 */
body.theme-elegant-blue {
    background-color: #e3f2fd;
    color: #0d47a1;
}

body.theme-elegant-blue #header {
    background: #1565c0;
    color: #bbdefb;
}

body.theme-elegant-blue .module h2, 
body.theme-elegant-blue .module caption {
    background: #1976d2;
    color: #e3f2fd;
}

body.theme-elegant-blue .module {
    background: #f5f9ff;
    border: 1px solid #90caf9;
}

body.theme-elegant-blue a:link, 
body.theme-elegant-blue a:visited {
    color: #0d47a1;
}

body.theme-elegant-blue .button, 
body.theme-elegant-blue input[type="submit"], 
body.theme-elegant-blue input[type="button"] {
    background: #1976d2;
    color: #fff;
}

body.theme-elegant-blue .button:hover, 
body.theme-elegant-blue input[type="submit"]:hover, 
body.theme-elegant-blue input[type="button"]:hover {
    background: #1565c0;
}

/* 绿柔主题 */
body.theme-soft-green {
    background-color: #e8f5e9;
    color: #1b5e20;
}

body.theme-soft-green #header {
    background: #388e3c;
    color: #c8e6c9;
}

body.theme-soft-green .module h2, 
body.theme-soft-green .module caption {
    background: #4caf50;
    color: #e8f5e9;
}

body.theme-soft-green .module {
    background: #f1f8e9;
    border: 1px solid #a5d6a7;
}

body.theme-soft-green a:link, 
body.theme-soft-green a:visited {
    color: #1b5e20;
}

body.theme-soft-green .button, 
body.theme-soft-green input[type="submit"], 
body.theme-soft-green input[type="button"] {
    background: #4caf50;
    color: #fff;
}

body.theme-soft-green .button:hover, 
body.theme-soft-green input[type="submit"]:hover, 
body.theme-soft-green input[type="button"]:hover {
    background: #388e3c;
}
