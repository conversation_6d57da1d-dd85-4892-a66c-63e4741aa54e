/**
 * 常量定义
 * 统一管理应用中使用的常量
 */

// API相关常量
export const API_CONSTANTS = {
  // 请求超时时间
  TIMEOUT: 10000,
  
  // 分页大小
  PAGE_SIZE: 20,
  
  // 最大上传文件大小 (10MB)
  MAX_UPLOAD_SIZE: 10 * 1024 * 1024,
  
  // 支持的图片格式
  SUPPORTED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  
  // 支持的视频格式
  SUPPORTED_VIDEO_TYPES: ['mp4', 'avi', 'mov', 'wmv', 'flv'],
} as const

// 存储相关常量
export const STORAGE_KEYS = {
  // 用户相关
  USER_TOKEN: 'user_token',
  USER_INFO: 'user_info',
  LOGIN_TIME: 'login_time',
  
  // 应用设置
  APP_THEME: 'app_theme',
  APP_LANGUAGE: 'app_language',
  APP_VERSION: 'app_version',
  
  // 购物车
  CART_ITEMS: 'cart_items',
  
  // 搜索历史
  SEARCH_HISTORY: 'search_history',
  
  // 浏览历史
  BROWSE_HISTORY: 'browse_history',
  
  // 收货地址
  DEFAULT_ADDRESS: 'default_address',
} as const

// 页面路径常量
export const PAGE_PATHS = {
  // 首页
  INDEX: '/pages/index/index',
  
  // 认证页面
  LOGIN: '/pages/auth/login',
  REGISTER: '/pages/auth/register',
  
  // 产品页面
  PRODUCT_LIST: '/pages/products/list',
  PRODUCT_DETAIL: '/pages/products/detail',
  PRODUCT_SEARCH: '/pages/products/search',
  
  // 购物车
  CART: '/pages/cart/cart',
  
  // 订单页面
  ORDER_LIST: '/pages/orders/list',
  ORDER_DETAIL: '/pages/orders/detail',
  ORDER_CONFIRM: '/pages/orders/confirm',
  
  // 用户中心
  USER_PROFILE: '/pages/user/profile',
  USER_SETTINGS: '/pages/user/settings',
  USER_ADDRESS: '/pages/user/address',
  
  // 分销中心
  DISTRIBUTION_INDEX: '/pages/distribution/index',
  DISTRIBUTION_TEAM: '/pages/distribution/team',
  DISTRIBUTION_COMMISSION: '/pages/distribution/commission',
  DISTRIBUTION_PROMOTION: '/pages/distribution/promotion',
} as const

// 订单状态常量
export const ORDER_STATUS = {
  PENDING: 'pending',           // 待付款
  PAID: 'paid',                // 已付款
  SHIPPED: 'shipped',          // 已发货
  DELIVERED: 'delivered',      // 已送达
  COMPLETED: 'completed',      // 已完成
  CANCELLED: 'cancelled',      // 已取消
  REFUNDING: 'refunding',      // 退款中
  REFUNDED: 'refunded',        // 已退款
} as const

// 订单状态文本映射
export const ORDER_STATUS_TEXT = {
  [ORDER_STATUS.PENDING]: '待付款',
  [ORDER_STATUS.PAID]: '已付款',
  [ORDER_STATUS.SHIPPED]: '已发货',
  [ORDER_STATUS.DELIVERED]: '已送达',
  [ORDER_STATUS.COMPLETED]: '已完成',
  [ORDER_STATUS.CANCELLED]: '已取消',
  [ORDER_STATUS.REFUNDING]: '退款中',
  [ORDER_STATUS.REFUNDED]: '已退款',
} as const

// 支付方式常量
export const PAYMENT_METHODS = {
  WECHAT: 'wechat',            // 微信支付
  ALIPAY: 'alipay',            // 支付宝
  BALANCE: 'balance',          // 余额支付
} as const

// 支付方式文本映射
export const PAYMENT_METHOD_TEXT = {
  [PAYMENT_METHODS.WECHAT]: '微信支付',
  [PAYMENT_METHODS.ALIPAY]: '支付宝',
  [PAYMENT_METHODS.BALANCE]: '余额支付',
} as const

// 分销等级常量
export const DISTRIBUTOR_LEVELS = {
  LEVEL_0: 0,                  // 普通用户
  LEVEL_1: 1,                  // 一级分销商
  LEVEL_2: 2,                  // 二级分销商
  LEVEL_3: 3,                  // 三级分销商
} as const

// 分销等级文本映射
export const DISTRIBUTOR_LEVEL_TEXT = {
  [DISTRIBUTOR_LEVELS.LEVEL_0]: '普通用户',
  [DISTRIBUTOR_LEVELS.LEVEL_1]: '一级分销商',
  [DISTRIBUTOR_LEVELS.LEVEL_2]: '二级分销商',
  [DISTRIBUTOR_LEVELS.LEVEL_3]: '三级分销商',
} as const

// 佣金状态常量
export const COMMISSION_STATUS = {
  PENDING: 'pending',          // 待结算
  SETTLED: 'settled',          // 已结算
  WITHDRAWN: 'withdrawn',      // 已提现
  FROZEN: 'frozen',            // 已冻结
} as const

// 佣金状态文本映射
export const COMMISSION_STATUS_TEXT = {
  [COMMISSION_STATUS.PENDING]: '待结算',
  [COMMISSION_STATUS.SETTLED]: '已结算',
  [COMMISSION_STATUS.WITHDRAWN]: '已提现',
  [COMMISSION_STATUS.FROZEN]: '已冻结',
} as const

// 产品状态常量
export const PRODUCT_STATUS = {
  ACTIVE: 'active',            // 上架
  INACTIVE: 'inactive',        // 下架
  DRAFT: 'draft',              // 草稿
  DELETED: 'deleted',          // 已删除
} as const

// 产品状态文本映射
export const PRODUCT_STATUS_TEXT = {
  [PRODUCT_STATUS.ACTIVE]: '上架',
  [PRODUCT_STATUS.INACTIVE]: '下架',
  [PRODUCT_STATUS.DRAFT]: '草稿',
  [PRODUCT_STATUS.DELETED]: '已删除',
} as const

// 性别常量
export const GENDER = {
  UNKNOWN: 0,                  // 未知
  MALE: 1,                     // 男
  FEMALE: 2,                   // 女
} as const

// 性别文本映射
export const GENDER_TEXT = {
  [GENDER.UNKNOWN]: '未知',
  [GENDER.MALE]: '男',
  [GENDER.FEMALE]: '女',
} as const

// 主题常量
export const THEMES = {
  LIGHT: 'light',              // 浅色主题
  DARK: 'dark',                // 深色主题
  AUTO: 'auto',                // 自动
} as const

// 语言常量
export const LANGUAGES = {
  ZH_CN: 'zh-CN',              // 简体中文
  EN_US: 'en-US',              // 英语
} as const

// 正则表达式常量
export const REGEX_PATTERNS = {
  // 手机号
  PHONE: /^1[3-9]\d{9}$/,
  
  // 邮箱
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  
  // 身份证号
  ID_CARD: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  
  // 密码（6-20位，包含字母和数字）
  PASSWORD: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,20}$/,
  
  // 中文姓名
  CHINESE_NAME: /^[\u4e00-\u9fa5]{2,10}$/,
  
  // 数字
  NUMBER: /^\d+$/,
  
  // 小数
  DECIMAL: /^\d+(\.\d+)?$/,
  
  // URL
  URL: /^https?:\/\/(([a-zA-Z0-9_-])+(\.)?)*(:\d+)?(\/((\.)?(\?)?=?&?[a-zA-Z0-9_-](\?)?)*)*$/i,
} as const

// 错误码常量
export const ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  
  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INVALID_TOKEN: 'INVALID_TOKEN',
  
  // 业务错误
  INVALID_PARAMS: 'INVALID_PARAMS',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  
  // 支付错误
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
  
  // 库存错误
  OUT_OF_STOCK: 'OUT_OF_STOCK',
  INSUFFICIENT_STOCK: 'INSUFFICIENT_STOCK',
} as const

// 错误消息映射
export const ERROR_MESSAGES = {
  [ERROR_CODES.UNKNOWN_ERROR]: '未知错误',
  [ERROR_CODES.NETWORK_ERROR]: '网络连接失败',
  [ERROR_CODES.TIMEOUT_ERROR]: '请求超时',
  [ERROR_CODES.UNAUTHORIZED]: '未授权访问',
  [ERROR_CODES.TOKEN_EXPIRED]: '登录已过期',
  [ERROR_CODES.INVALID_TOKEN]: '无效的访问令牌',
  [ERROR_CODES.INVALID_PARAMS]: '参数错误',
  [ERROR_CODES.RESOURCE_NOT_FOUND]: '资源不存在',
  [ERROR_CODES.PERMISSION_DENIED]: '权限不足',
  [ERROR_CODES.PAYMENT_FAILED]: '支付失败',
  [ERROR_CODES.INSUFFICIENT_BALANCE]: '余额不足',
  [ERROR_CODES.OUT_OF_STOCK]: '商品已售罄',
  [ERROR_CODES.INSUFFICIENT_STOCK]: '库存不足',
} as const

// 默认配置
export const DEFAULT_CONFIG = {
  // 默认头像
  DEFAULT_AVATAR: '/static/images/default-avatar.png',
  
  // 默认商品图片
  DEFAULT_PRODUCT_IMAGE: '/static/images/default-product.png',
  
  // 默认分页大小
  DEFAULT_PAGE_SIZE: 20,
  
  // 默认搜索历史保存数量
  MAX_SEARCH_HISTORY: 10,
  
  // 默认浏览历史保存数量
  MAX_BROWSE_HISTORY: 50,
  
  // 默认缓存时间（30分钟）
  DEFAULT_CACHE_TIME: 30 * 60 * 1000,
  
  // Token过期时间（7天）
  TOKEN_EXPIRE_TIME: 7 * 24 * 60 * 60 * 1000,
} as const
