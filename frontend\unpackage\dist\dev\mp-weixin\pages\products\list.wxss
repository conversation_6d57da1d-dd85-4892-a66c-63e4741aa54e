@import "../../uvue.wxss";
:host{display:flex;flex-direction:column}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 图标相关变量 */
/* 图标颜色预设 */
/* 全局图标基础样式 */
.icon-base.data-v-7abd9408 {
  font-family: "iconfont";
  font-style: normal;
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
}

/* 图标动画 */
@keyframes icon-rotate-7abd9408 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
.icon-spin.data-v-7abd9408 {
  animation: icon-rotate-7abd9408 1s linear infinite;
}
.container.data-v-7abd9408 {
  background-color: #f5f5f5;
  min-height: 100vh;
}
.search-header.data-v-7abd9408 {
  padding: 10px 15px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}
.search-header .search-bar.data-v-7abd9408 {
  display: flex;
  align-items: center;
  height: 36px;
  padding: 0 12px;
  background-color: #f5f5f5;
  border-radius: 18px;
}
.search-header .search-bar .search-placeholder.data-v-7abd9408 {
  margin-left: 8px;
  font-size: 14px;
  color: #999;
}
.filter-bar.data-v-7abd9408 {
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}
.filter-bar .filter-item.data-v-7abd9408 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  position: relative;
}
.filter-bar .filter-item.active .filter-text.data-v-7abd9408 {
  color: #007aff;
}
.filter-bar .filter-item.active.data-v-7abd9408::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: #007aff;
}
.filter-bar .filter-item .filter-text.data-v-7abd9408 {
  font-size: 14px;
  color: #333;
  margin-right: 4px;
}
.product-list.data-v-7abd9408 {
  padding: 10px 15px;
}
.product-list .product-item.data-v-7abd9408 {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.product-list .product-item .product-image.data-v-7abd9408 {
  width: 100px;
  height: 100px;
  border-radius: 6px;
  margin-right: 12px;
}
.product-list .product-item .product-info.data-v-7abd9408 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.product-list .product-item .product-info .product-name.data-v-7abd9408 {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.product-list .product-item .product-info .product-price.data-v-7abd9408 {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.product-list .product-item .product-info .product-price .price-current.data-v-7abd9408 {
  font-size: 16px;
  font-weight: 600;
  color: #ff3b30;
  margin-right: 8px;
}
.product-list .product-item .product-info .product-price .price-original.data-v-7abd9408 {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}
.product-list .product-item .product-info .product-tags.data-v-7abd9408 {
  display: flex;
  margin-bottom: 8px;
}
.product-list .product-item .product-info .product-tags .tag.data-v-7abd9408 {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 2px;
  margin-right: 4px;
}
.product-list .product-item .product-info .product-tags .tag.hot.data-v-7abd9408 {
  background-color: #ff3b30;
  color: #fff;
}
.product-list .product-item .product-info .product-tags .tag.new.data-v-7abd9408 {
  background-color: #34c759;
  color: #fff;
}
.product-list .product-item .product-info .product-sales .sales-text.data-v-7abd9408 {
  font-size: 12px;
  color: #999;
}
.load-more.data-v-7abd9408 {
  padding: 20px;
  text-align: center;
}
.load-more .load-text.data-v-7abd9408 {
  font-size: 14px;
  color: #999;
}
.empty-state.data-v-7abd9408 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}
.empty-state .empty-text.data-v-7abd9408 {
  font-size: 14px;
  color: #999;
  margin-top: 16px;
}