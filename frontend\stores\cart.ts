/**
 * 购物车状态管理
 * 管理购物车商品、数量、结算等
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import http from '@/utils/request'
import { getCdnUrl } from '@/utils/config'
import type { Product, ProductSpec } from './product'

// 购物车商品接口
export interface CartItem {
  id: number
  product_id: number
  product_name: string
  product_image: string
  product_price: number
  spec_id?: number
  spec_name?: string
  spec_attrs?: Record<string, string>
  quantity: number
  selected: boolean
  stock: number
  created_at: string
}

export const useCartStore = defineStore('cart', () => {
  // 状态
  const items = ref<CartItem[]>([])
  const isLoading = ref(false)
  const isAllSelected = ref(false)

  // 计算属性
  const totalCount = computed(() => {
    return items.value.reduce((total, item) => total + item.quantity, 0)
  })

  const selectedCount = computed(() => {
    return items.value
      .filter(item => item.selected)
      .reduce((total, item) => total + item.quantity, 0)
  })

  const selectedItems = computed(() => {
    return items.value.filter(item => item.selected)
  })

  const totalAmount = computed(() => {
    return items.value
      .filter(item => item.selected)
      .reduce((total, item) => total + (item.product_price * item.quantity), 0)
  })

  const isEmpty = computed(() => {
    return items.value.length === 0
  })

  const hasSelected = computed(() => {
    return items.value.some(item => item.selected)
  })

  // 方法
  /**
   * 获取购物车列表
   */
  const getCartItems = async (): Promise<boolean> => {
    try {
      isLoading.value = true
      
      const response = await http.get('/cart/list')
      
      if (response.success) {
        items.value = response.data.map((item: any) => ({
          ...item,
          product_image: getCdnUrl(item.product_image),
          selected: false
        }))
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('获取购物车失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 添加商品到购物车
   */
  const addToCart = async (params: {
    product_id: number
    spec_id?: number
    quantity: number
  }): Promise<boolean> => {
    try {
      const response = await http.post('/cart/add', params)
      
      if (response.success) {
        // 重新获取购物车列表
        await getCartItems()
        
        uni.showToast({
          title: '已添加到购物车',
          icon: 'success'
        })
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('添加到购物车失败:', error)
      return false
    }
  }

  /**
   * 更新购物车商品数量
   */
  const updateQuantity = async (id: number, quantity: number): Promise<boolean> => {
    if (quantity <= 0) {
      return removeFromCart(id)
    }
    
    try {
      const response = await http.put(`/cart/${id}`, { quantity })
      
      if (response.success) {
        // 更新本地状态
        const item = items.value.find(item => item.id === id)
        if (item) {
          item.quantity = quantity
        }
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('更新购物车数量失败:', error)
      return false
    }
  }

  /**
   * 从购物车移除商品
   */
  const removeFromCart = async (id: number): Promise<boolean> => {
    try {
      const response = await http.delete(`/cart/${id}`)
      
      if (response.success) {
        // 更新本地状态
        items.value = items.value.filter(item => item.id !== id)
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('移除购物车商品失败:', error)
      return false
    }
  }

  /**
   * 批量删除购物车商品
   */
  const batchRemove = async (ids: number[]): Promise<boolean> => {
    try {
      const response = await http.post('/cart/batch-remove', { ids })
      
      if (response.success) {
        // 更新本地状态
        items.value = items.value.filter(item => !ids.includes(item.id))
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('批量删除购物车商品失败:', error)
      return false
    }
  }

  /**
   * 清空购物车
   */
  const clearCart = async (): Promise<boolean> => {
    try {
      const response = await http.post('/cart/clear')
      
      if (response.success) {
        items.value = []
        return true
      }
      
      return false
    } catch (error) {
      console.error('清空购物车失败:', error)
      return false
    }
  }

  /**
   * 选择/取消选择商品
   */
  const toggleSelect = (id: number): void => {
    const item = items.value.find(item => item.id === id)
    if (item) {
      item.selected = !item.selected
    }
    
    // 更新全选状态
    updateAllSelectedState()
  }

  /**
   * 全选/取消全选
   */
  const toggleSelectAll = (): void => {
    const newState = !isAllSelected.value
    items.value.forEach(item => {
      item.selected = newState
    })
    isAllSelected.value = newState
  }

  /**
   * 更新全选状态
   */
  const updateAllSelectedState = (): void => {
    isAllSelected.value = items.value.length > 0 && items.value.every(item => item.selected)
  }

  /**
   * 增加商品数量
   */
  const increaseQuantity = async (id: number): Promise<boolean> => {
    const item = items.value.find(item => item.id === id)
    if (!item) return false
    
    if (item.quantity >= item.stock) {
      uni.showToast({
        title: '库存不足',
        icon: 'none'
      })
      return false
    }
    
    return updateQuantity(id, item.quantity + 1)
  }

  /**
   * 减少商品数量
   */
  const decreaseQuantity = async (id: number): Promise<boolean> => {
    const item = items.value.find(item => item.id === id)
    if (!item) return false
    
    return updateQuantity(id, item.quantity - 1)
  }

  /**
   * 检查库存
   */
  const checkStock = async (): Promise<boolean> => {
    try {
      const selectedIds = selectedItems.value.map(item => item.id)
      if (selectedIds.length === 0) return true
      
      const response = await http.post('/cart/check-stock', { ids: selectedIds })
      
      if (response.success) {
        // 更新库存信息
        response.data.forEach((stockInfo: any) => {
          const item = items.value.find(item => item.id === stockInfo.id)
          if (item) {
            item.stock = stockInfo.stock
          }
        })
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('检查库存失败:', error)
      return false
    }
  }

  /**
   * 获取结算信息
   */
  const getCheckoutInfo = async (): Promise<any> => {
    try {
      const selectedIds = selectedItems.value.map(item => item.id)
      if (selectedIds.length === 0) return null
      
      const response = await http.post('/cart/checkout-info', { ids: selectedIds })
      
      if (response.success) {
        return response.data
      }
      
      return null
    } catch (error) {
      console.error('获取结算信息失败:', error)
      return null
    }
  }

  /**
   * 立即购买（不加入购物车）
   */
  const buyNow = async (params: {
    product_id: number
    spec_id?: number
    quantity: number
  }): Promise<any> => {
    try {
      const response = await http.post('/cart/buy-now', params)
      
      if (response.success) {
        return response.data
      }
      
      return null
    } catch (error) {
      console.error('立即购买失败:', error)
      return null
    }
  }

  /**
   * 重置购物车状态
   */
  const resetCart = (): void => {
    items.value = []
    isAllSelected.value = false
  }

  return {
    // 状态
    items,
    isLoading,
    isAllSelected,
    
    // 计算属性
    totalCount,
    selectedCount,
    selectedItems,
    totalAmount,
    isEmpty,
    hasSelected,
    
    // 方法
    getCartItems,
    addToCart,
    updateQuantity,
    removeFromCart,
    batchRemove,
    clearCart,
    toggleSelect,
    toggleSelectAll,
    updateAllSelectedState,
    increaseQuantity,
    decreaseQuantity,
    checkStock,
    getCheckoutInfo,
    buyNow,
    resetCart
  }
}, {
  // 持久化配置
  persist: {
    key: 'cart-store',
    storage: {
      getItem: (key: string) => uni.getStorageSync(key),
      setItem: (key: string, value: string) => uni.setStorageSync(key, value),
      removeItem: (key: string) => uni.removeStorageSync(key)
    },
    // 不持久化购物车数据，每次启动时从服务器获取
    paths: []
  }
})
