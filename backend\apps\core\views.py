from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework.decorators import action
from rest_framework import serializers
from django.conf import settings
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db.models import F
import logging
from .models import Banner
from .serializers import BannerSerializer, BannerListSerializer

logger = logging.getLogger(__name__)


class SystemInfoSerializer(serializers.Serializer):
    """系统信息序列化器"""
    name = serializers.CharField()
    version = serializers.CharField()
    environment = serializers.CharField()
    debug = serializers.BooleanField()
    server_time = serializers.DateTimeField()
    timezone = serializers.CharField()
    api_version = serializers.CharField()
    description = serializers.CharField()


class HealthCheckSerializer(serializers.Serializer):
    """健康检查序列化器"""
    status = serializers.CharField()
    database = serializers.CharField()
    cache = serializers.CharField()
    timestamp = serializers.DateTimeField()


class SystemStatsUsersSerializer(serializers.Serializer):
    """系统统计用户信息序列化器"""
    total = serializers.IntegerField()
    active = serializers.IntegerField()
    verified = serializers.IntegerField()


class SystemStatsProductsSerializer(serializers.Serializer):
    """系统统计产品信息序列化器"""
    total = serializers.IntegerField()
    active = serializers.IntegerField()
    featured = serializers.IntegerField()


class SystemStatsOrdersSerializer(serializers.Serializer):
    """系统统计订单信息序列化器"""
    total = serializers.IntegerField()
    pending = serializers.IntegerField()
    completed = serializers.IntegerField()


class SystemStatsSerializer(serializers.Serializer):
    """系统统计信息序列化器"""
    users = SystemStatsUsersSerializer()
    products = SystemStatsProductsSerializer()
    orders = SystemStatsOrdersSerializer()
    timestamp = serializers.DateTimeField()


class SystemInfoView(APIView):
    """系统信息API"""
    permission_classes = [AllowAny]
    serializer_class = SystemInfoSerializer
    
    def get(self, request):
        try:
            serializer = SystemInfoSerializer({
                'name': '智梦科技系统',
                'version': '1.0.0',
                'environment': settings.ENVIRONMENT,
                'debug': settings.DEBUG,
                'server_time': timezone.now(),
                'timezone': str(timezone.get_current_timezone()),
                'api_version': 'v1',
                'description': '智梦科技企业级系统 - 基于Django 5.2.4构建'
            })
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error in SystemInfoView: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class HealthCheckView(APIView):
    """健康检查API"""
    permission_classes = [AllowAny]
    serializer_class = HealthCheckSerializer
    
    def get(self, request):
        try:
            # 检查数据库连接
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            
            # 检查缓存连接
            from django.core.cache import cache
            cache.set('health_check', 'ok', 10)
            cache_status = cache.get('health_check') == 'ok'
            
            data = {
                'status': 'healthy',
                'database': 'connected',
                'cache': 'connected' if cache_status else 'disconnected',
                'timestamp': timezone.now()
            }
        except Exception as e:
            logger.error(f"Error in HealthCheckView: {str(e)}")
            data = {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': timezone.now()
            }
            return Response(data, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        
        serializer = HealthCheckSerializer(data)
        return Response(serializer.data)


class SystemStatsView(APIView):
    """系统统计信息（需要登录）"""
    serializer_class = SystemStatsSerializer
    
    def get(self, request):
        from apps.users.models import User
        from apps.products.models import Product
        from apps.orders.models import Order
        
        stats = {
            'users': {
                'total': User.objects.filter(is_deleted=False).count(),
                'active': User.objects.filter(is_deleted=False, is_active=True).count(),
                'verified': User.objects.filter(is_deleted=False, is_verified=True).count(),
            },
            'products': {
                'total': Product.objects.filter(is_deleted=False).count(),
                'active': Product.objects.filter(is_deleted=False, status='active').count(),
                'featured': Product.objects.filter(is_deleted=False, is_featured=True).count(),
            },
            'orders': {
                'total': Order.objects.filter(is_deleted=False).count(),
                'pending': Order.objects.filter(is_deleted=False, status='pending').count(),
                'completed': Order.objects.filter(is_deleted=False, status='completed').count(),
            },
            'timestamp': timezone.now()
        }
        
        serializer = SystemStatsSerializer(stats)
        return Response(serializer.data)


class BannerViewSet(ReadOnlyModelViewSet):
    """轮播图视图集"""
    serializer_class = BannerSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        """获取轮播图查询集"""
        queryset = Banner.objects.filter(is_deleted=False, is_active=True)

        # 过滤有效时间内的轮播图
        from django.db import models
        now = timezone.now()
        queryset = queryset.filter(
            models.Q(start_time__isnull=True) | models.Q(start_time__lte=now),
            models.Q(end_time__isnull=True) | models.Q(end_time__gte=now)
        )

        # 按位置和排序字段排序
        return queryset.order_by('position', 'sort_order', '-created_at')

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return BannerListSerializer
        return BannerSerializer

    def list(self, request, *args, **kwargs):
        """轮播图列表"""
        position = request.query_params.get('position', 'home')

        queryset = self.get_queryset().filter(position=position)
        serializer = self.get_serializer(queryset, many=True, context={'request': request})

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })

    def retrieve(self, request, *args, **kwargs):
        """轮播图详情"""
        instance = self.get_object()

        # 增加点击次数
        Banner.objects.filter(id=instance.id).update(click_count=F('click_count') + 1)

        serializer = self.get_serializer(instance, context={'request': request})
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })

    @action(detail=False, methods=['get'])
    def positions(self, request):
        """获取所有轮播图位置"""
        positions = Banner.POSITION_CHOICES
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': [{'key': key, 'label': label} for key, label in positions]
        })