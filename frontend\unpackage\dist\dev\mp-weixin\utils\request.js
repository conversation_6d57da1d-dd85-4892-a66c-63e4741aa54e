"use strict";
const common_vendor = require("../common/vendor.js");
const utils_config = require("./config.js");
const utils_auth = require("./auth.js");
class HttpRequest {
  constructor() {
    this.requestInterceptors = [];
    this.responseInterceptors = [];
    this.errorInterceptors = [];
    this.defaultConfig = {
      method: "GET",
      timeout: 1e4,
      showLoading: false,
      loadingText: "加载中...",
      showError: true,
      skipAuth: false,
      header: {
        "Content-Type": "application/json"
      }
    };
  }
  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor) {
    this.requestInterceptors.push(interceptor);
  }
  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor) {
    this.responseInterceptors.push(interceptor);
  }
  /**
   * 添加错误拦截器
   */
  addErrorInterceptor(interceptor) {
    this.errorInterceptors.push(interceptor);
  }
  /**
   * 执行请求拦截器
   */
  runRequestInterceptors(config) {
    return common_vendor.__awaiter(this, void 0, void 0, function* () {
      var e_1, _a;
      let result = config;
      try {
        for (var _b = common_vendor.__values(this.requestInterceptors), _c = _b.next(); !_c.done; _c = _b.next()) {
          var interceptor = _c.value;
          result = yield interceptor(result);
        }
      } catch (e_1_1) {
        e_1 = { error: e_1_1 };
      } finally {
        try {
          if (_c && !_c.done && (_a = _b.return))
            _a.call(_b);
        } finally {
          if (e_1)
            throw e_1.error;
        }
      }
      return result;
    });
  }
  /**
   * 执行响应拦截器
   */
  runResponseInterceptors(response) {
    return common_vendor.__awaiter(this, void 0, void 0, function* () {
      var e_2, _a;
      let result = response;
      try {
        for (var _b = common_vendor.__values(this.responseInterceptors), _c = _b.next(); !_c.done; _c = _b.next()) {
          var interceptor = _c.value;
          result = yield interceptor(result);
        }
      } catch (e_2_1) {
        e_2 = { error: e_2_1 };
      } finally {
        try {
          if (_c && !_c.done && (_a = _b.return))
            _a.call(_b);
        } finally {
          if (e_2)
            throw e_2.error;
        }
      }
      return result;
    });
  }
  /**
   * 执行错误拦截器
   */
  runErrorInterceptors(error) {
    return common_vendor.__awaiter(this, void 0, void 0, function* () {
      var e_3, _a;
      let result = error;
      try {
        for (var _b = common_vendor.__values(this.errorInterceptors), _c = _b.next(); !_c.done; _c = _b.next()) {
          var interceptor = _c.value;
          result = yield interceptor(result);
        }
      } catch (e_3_1) {
        e_3 = { error: e_3_1 };
      } finally {
        try {
          if (_c && !_c.done && (_a = _b.return))
            _a.call(_b);
        } finally {
          if (e_3)
            throw e_3.error;
        }
      }
      return result;
    });
  }
  /**
   * 处理URL参数
   */
  buildUrl(url, params) {
    if (!params || Object.keys(params).length === 0) {
      return url;
    }
    const queryString = Object.keys(params).map((key) => {
      return `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`;
    }).join("&");
    return url + (url.includes("?") ? "&" : "?") + queryString;
  }
  /**
   * 显示加载提示
   */
  showLoading(text) {
    common_vendor.index.showLoading({
      title: text,
      mask: true
    });
  }
  /**
   * 隐藏加载提示
   */
  hideLoading() {
    common_vendor.index.hideLoading();
  }
  /**
   * 显示错误提示
   */
  showError(message) {
    common_vendor.index.showToast({
      title: message,
      icon: "none",
      duration: 2e3
    });
  }
  /**
   * 发送请求
   */
  request(config) {
    return common_vendor.__awaiter(this, void 0, void 0, function* () {
      const finalConfig = Object.assign(Object.assign({}, this.defaultConfig), config);
      try {
        const interceptedConfig = yield this.runRequestInterceptors(finalConfig);
        const fullUrl = utils_config.getApiUrl(this.buildUrl(interceptedConfig.url, interceptedConfig.params));
        const headers = Object.assign({}, interceptedConfig.header);
        if (!interceptedConfig.skipAuth) {
          const token = utils_auth.getToken();
          if (token) {
            headers["Authorization"] = `Bearer ${token}`;
          }
        }
        if (interceptedConfig.showLoading) {
          this.showLoading(interceptedConfig.loadingText);
        }
        const response = yield new Promise((resolve, reject) => {
          common_vendor.index.request({
            url: fullUrl,
            method: interceptedConfig.method,
            data: interceptedConfig.data,
            header: headers,
            timeout: interceptedConfig.timeout,
            success: resolve,
            fail: reject
          });
        });
        if (interceptedConfig.showLoading) {
          this.hideLoading();
        }
        const interceptedResponse = yield this.runResponseInterceptors(response);
        const responseData = interceptedResponse.data;
        if (responseData.code !== 0) {
          throw new Error(responseData.message || "请求失败");
        }
        return responseData;
      } catch (error) {
        if (finalConfig.showLoading) {
          this.hideLoading();
        }
        const interceptedError = yield this.runErrorInterceptors(error);
        if (finalConfig.showError) {
          const errorMessage = interceptedError.message || "网络请求失败";
          this.showError(errorMessage);
        }
        if (utils_config.isDev()) {
          common_vendor.index.__f__("error", "at utils/request.ts:236", "Request Error:", interceptedError);
        }
        throw interceptedError;
      }
    });
  }
  /**
   * GET请求
   */
  get(url, params, config) {
    return this.request(Object.assign({ url, method: "GET", params }, config));
  }
  /**
   * POST请求
   */
  post(url, data, config) {
    return this.request(Object.assign({ url, method: "POST", data }, config));
  }
  /**
   * PUT请求
   */
  put(url, data, config) {
    return this.request(Object.assign({ url, method: "PUT", data }, config));
  }
  /**
   * DELETE请求
   */
  delete(url, params, config) {
    return this.request(Object.assign({ url, method: "DELETE", params }, config));
  }
  /**
   * PATCH请求
   */
  patch(url, data, config) {
    return this.request(Object.assign({ url, method: "PATCH", data }, config));
  }
}
const http = new HttpRequest();
http.addRequestInterceptor((config) => {
  if (utils_config.isDev()) {
    common_vendor.index.__f__("log", "at utils/request.ts:311", "Request:", config);
  }
  return config;
});
http.addResponseInterceptor((response) => {
  if (utils_config.isDev()) {
    common_vendor.index.__f__("log", "at utils/request.ts:320", "Response:", response);
  }
  return response;
});
http.addErrorInterceptor((error) => {
  if (error.statusCode === 401) {
    utils_auth.removeToken();
    common_vendor.index.reLaunch({
      url: "/pages/auth/login"
    });
  }
  return error;
});
exports.http = http;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/request.js.map
