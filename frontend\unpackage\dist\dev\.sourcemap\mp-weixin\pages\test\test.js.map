{"version": 3, "file": "test.js", "sources": ["pages/test/test.uvue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdGVzdC90ZXN0LnV2dWU"], "sourcesContent": ["<template>\n\t<view class=\"test-page\">\n\t\t<view class=\"header\">\n\t\t\t<text class=\"title\">API测试页面</text>\n\t\t</view>\n\t\t\n\t\t<view class=\"test-section\">\n\t\t\t<text class=\"section-title\">轮播图API测试</text>\n\t\t\t<button @click=\"testBannerApi\" class=\"test-btn\">测试轮播图API</button>\n\t\t\t\n\t\t\t<view v-if=\"bannerLoading\" class=\"loading\">\n\t\t\t\t<text>加载中...</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view v-if=\"bannerError\" class=\"error\">\n\t\t\t\t<text>错误: {{ bannerError }}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view v-if=\"bannerData.length > 0\" class=\"result\">\n\t\t\t\t<text class=\"result-title\">轮播图数据:</text>\n\t\t\t\t<view v-for=\"(banner, index) in bannerData\" :key=\"index\" class=\"banner-item\">\n\t\t\t\t\t<text class=\"banner-title\">{{ banner.title }}</text>\n\t\t\t\t\t<text class=\"banner-url\">{{ banner.image }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"test-section\">\n\t\t\t<text class=\"section-title\">配置信息</text>\n\t\t\t<view class=\"config-info\">\n\t\t\t\t<text>API地址: {{ config.API_BASE_URL }}</text>\n\t\t\t\t<text>CDN地址: {{ config.CDN_BASE_URL }}</text>\n\t\t\t\t<text>当前环境: {{ currentEnv }}</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup lang=\"uts\">\nimport { ref, onMounted } from 'vue'\nimport { http } from '@/utils/request'\nimport { CONFIG, getEnv } from '@/utils/config'\n\n// 响应式数据\nconst bannerLoading = ref(false)\nconst bannerError = ref('')\nconst bannerData = ref<any[]>([])\nconst config = ref(CONFIG)\nconst currentEnv = ref(getEnv())\n\n// 测试轮播图API\nconst testBannerApi = async () => {\n\tbannerLoading.value = true\n\tbannerError.value = ''\n\tbannerData.value = []\n\t\n\ttry {\n\t\tuni.__f__('log','at pages/test/test.uvue:58','开始测试轮播图API...')\n\t\tuni.__f__('log','at pages/test/test.uvue:59','API地址:', config.value.API_BASE_URL)\n\t\t\n\t\tconst response = await http.get('/core/banners/', {\n\t\t\tposition: 'home'\n\t\t})\n\t\t\n\t\tuni.__f__('log','at pages/test/test.uvue:65','API响应:', response)\n\t\t\n\t\tif (response.code === 0 && response.data) {\n\t\t\tbannerData.value = response.data\n\t\t\tuni.__f__('log','at pages/test/test.uvue:69','轮播图数据:', bannerData.value)\n\t\t} else {\n\t\t\tbannerError.value = response.message || '获取数据失败'\n\t\t}\n\t} catch (error: any) {\n\t\tuni.__f__('error','at pages/test/test.uvue:74','API调用失败:', error)\n\t\tbannerError.value = error.message || '网络请求失败'\n\t} finally {\n\t\tbannerLoading.value = false\n\t}\n}\n\n// 页面加载时自动测试\nonMounted(() => {\n\tuni.__f__('log','at pages/test/test.uvue:83','测试页面已加载')\n\tuni.__f__('log','at pages/test/test.uvue:84','当前配置:', config.value)\n\tuni.__f__('log','at pages/test/test.uvue:85','当前协议检测结果:', config.value.API_BASE_URL.startsWith('http://') ? 'HTTP' : 'HTTPS')\n\ttestBannerApi()\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.test-page {\n\tpadding: 20px;\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n}\n\n.header {\n\ttext-align: center;\n\tmargin-bottom: 30px;\n\t\n\t.title {\n\t\tfont-size: 24px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n}\n\n.test-section {\n\tbackground-color: white;\n\tborder-radius: 8px;\n\tpadding: 20px;\n\tmargin-bottom: 20px;\n\tbox-shadow: 0 2px 4px rgba(0,0,0,0.1);\n\t\n\t.section-title {\n\t\tfont-size: 18px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 15px;\n\t\tdisplay: block;\n\t}\n}\n\n.test-btn {\n\tbackground-color: #007aff;\n\tcolor: white;\n\tborder: none;\n\tborder-radius: 6px;\n\tpadding: 12px 24px;\n\tfont-size: 16px;\n\tmargin-bottom: 15px;\n}\n\n.loading {\n\ttext-align: center;\n\tcolor: #666;\n\tpadding: 20px;\n}\n\n.error {\n\tbackground-color: #ffebee;\n\tcolor: #c62828;\n\tpadding: 10px;\n\tborder-radius: 4px;\n\tmargin-bottom: 15px;\n}\n\n.result {\n\t.result-title {\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 10px;\n\t\tdisplay: block;\n\t}\n}\n\n.banner-item {\n\tbackground-color: #f8f9fa;\n\tpadding: 10px;\n\tborder-radius: 4px;\n\tmargin-bottom: 10px;\n\t\n\t.banner-title {\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 5px;\n\t}\n\t\n\t.banner-url {\n\t\tcolor: #666;\n\t\tfont-size: 12px;\n\t\tword-break: break-all;\n\t\tdisplay: block;\n\t}\n}\n\n.config-info {\n\ttext {\n\t\tdisplay: block;\n\t\tmargin-bottom: 8px;\n\t\tcolor: #666;\n\t\tfont-size: 14px;\n\t}\n}\n</style>\n", "import MiniProgramPage from 'F:/zmkj-system/frontend/pages/test/test.uvue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "CONFIG", "getEnv", "__awaiter", "uni", "http", "onMounted"], "mappings": ";;;;;;;AA4CA,UAAM,gBAAgBA,kBAAI,KAAK;AAC/B,UAAM,cAAcA,kBAAI,EAAE;AAC1B,UAAM,aAAaA,kBAAW,CAAA,CAAE;AAChC,UAAM,SAASA,kBAAIC,aAAAA,MAAM;AACzB,UAAM,aAAaD,cAAAA,IAAIE,aAAM,OAAA,CAAE;AAG/B,UAAM,gBAAgB,MAAA;AAAA,aAAAC,cAAA,UAAA,MAAA,QAAA,QAAA,aAAA;AACrB,sBAAc,QAAQ;AACtB,oBAAY,QAAQ;AACpB,mBAAW,QAAQ;AAEnB,YAAI;AACHC,wBAAAA,MAAI,MAAM,OAAM,8BAA6B,eAAe;AAC5DA,8BAAI,MAAM,OAAM,8BAA6B,UAAU,OAAO,MAAM,YAAY;AAEhF,gBAAM,WAAW,MAAMC,cAAI,KAAC,IAAI,kBAAkB,IAAA,cAAA;AAAA,YACjD,UAAU;AAAA,UACV,CAAA,CAAA;AAEDD,wBAAG,MAAC,MAAM,OAAM,8BAA6B,UAAU,QAAQ;AAE/D,cAAI,SAAS,SAAS,KAAK,SAAS,MAAM;AACzC,uBAAW,QAAQ,SAAS;AAC5BA,0BAAG,MAAC,MAAM,OAAM,8BAA6B,UAAU,WAAW,KAAK;AAAA,UACvE,OAAM;AACN,wBAAY,QAAQ,SAAS,WAAW;AAAA,UACxC;AAAA,QACD,SAAQ,OAAY;AACpBA,wBAAG,MAAC,MAAM,SAAQ,8BAA6B,YAAY,KAAK;AAChE,sBAAY,QAAQ,MAAM,WAAW;AAAA,QACrC,UAAS;AACT,wBAAc,QAAQ;AAAA,QACtB;AAAA,MACD,CAAA;AAAA;AAGDE,kBAAAA,UAAU,MAAA;AACTF,oBAAAA,MAAI,MAAM,OAAM,8BAA6B,SAAS;AACtDA,oBAAG,MAAC,MAAM,OAAM,8BAA6B,SAAS,OAAO,KAAK;AAClEA,oBAAAA,MAAI,MAAM,OAAM,8BAA6B,aAAa,OAAO,MAAM,aAAa,WAAW,SAAS,IAAI,SAAS,OAAO;AAC5H;IACD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrFD,GAAG,WAAW,eAAe;"}