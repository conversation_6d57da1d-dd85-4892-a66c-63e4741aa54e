#!/usr/bin/env python
"""
运行所有测试脚本
用于一次性运行所有Django应用的测试用例
"""

import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 配置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zmkj.settings.development')
django.setup()

def run_all_tests():
    """运行所有测试用例"""
    TestRunner = get_runner(settings)
    test_runner = TestRunner(verbosity=2)
    
    # 定义要测试的应用
    test_apps = [
        'apps.core',
        'apps.users',
        'apps.products',
        'apps.orders',
        'apps.distribution',
        'apps.wechat'
    ]
    
    print("开始运行所有测试...")
    print(f"测试应用: {', '.join(test_apps)}")
    print("-" * 50)
    
    # 运行测试
    failures = test_runner.run_tests(test_apps)
    
    # 输出结果
    print("-" * 50)
    if failures:
        print(f"测试完成，发现 {failures} 个失败。")
        sys.exit(1)
    else:
        print("所有测试通过!")
        sys.exit(0)

if __name__ == '__main__':
    run_all_tests()
