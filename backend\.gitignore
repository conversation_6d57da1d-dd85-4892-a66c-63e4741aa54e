# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
static/admin/

# Virtual Environment
venv/
env/
ENV/

# Environment variables
.env
!.env.example

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/

# Certificates
certs/

# Other
.DS_Store
*~