/* 智梦科技系统 - 自定义Admin样式 */

/* 全局样式优化 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
}

/* 头部样式 */
#header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#branding h1 {
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* 导航样式 */
.breadcrumbs {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 10px 20px;
}

/* 表格样式优化 */
.results table {
    border-collapse: collapse;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.results table th {
    background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.results table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.results table tr:hover {
    background-color: #e3f2fd;
    transition: background-color 0.2s ease;
}

/* 按钮样式优化 */
.button, input[type=submit], input[type=button], .submit-row input, a.button {
    background: linear-gradient(to bottom, #007bff, #0056b3);
    border: none;
    border-radius: 4px;
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.button:hover, input[type=submit]:hover, input[type=button]:hover, .submit-row input:hover, a.button:hover {
    background: linear-gradient(to bottom, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 删除按钮特殊样式 */
.deletelink, .deletelink-box {
    background: linear-gradient(to bottom, #dc3545, #c82333) !important;
}

.deletelink:hover, .deletelink-box:hover {
    background: linear-gradient(to bottom, #c82333, #a71e2a) !important;
}

/* 添加按钮特殊样式 */
.addlink {
    background: linear-gradient(to bottom, #28a745, #1e7e34) !important;
}

.addlink:hover {
    background: linear-gradient(to bottom, #1e7e34, #155724) !important;
}

/* 表单样式优化 */
.form-row {
    margin-bottom: 15px;
}

.form-row label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    display: block;
}

input[type="text"], input[type="email"], input[type="password"], input[type="url"], 
input[type="number"], input[type="tel"], textarea, select {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px 12px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

input[type="text"]:focus, input[type="email"]:focus, input[type="password"]:focus, 
input[type="url"]:focus, input[type="number"]:focus, input[type="tel"]:focus, 
textarea:focus, select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    outline: none;
}

/* 错误样式 */
.errorlist {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    color: #721c24;
    padding: 10px;
    margin-bottom: 10px;
}

.errorlist li {
    list-style: none;
    margin: 0;
}

/* 成功消息样式 */
.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    color: #155724;
    padding: 10px;
    margin-bottom: 10px;
}

/* 警告消息样式 */
.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    color: #856404;
    padding: 10px;
    margin-bottom: 10px;
}

/* 信息消息样式 */
.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 4px;
    color: #0c5460;
    padding: 10px;
    margin-bottom: 10px;
}

/* 分页样式 */
.paginator {
    text-align: center;
    margin: 20px 0;
}

.paginator a, .paginator .this-page {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    text-decoration: none;
    color: #007bff;
    transition: all 0.2s ease;
}

.paginator a:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.paginator .this-page {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

/* 过滤器样式 */
#changelist-filter {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
}

#changelist-filter h2 {
    color: #495057;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #dee2e6;
}

#changelist-filter ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

#changelist-filter li {
    margin-bottom: 5px;
}

#changelist-filter a {
    color: #007bff;
    text-decoration: none;
    padding: 2px 5px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

#changelist-filter a:hover {
    background: #e9ecef;
}

#changelist-filter a.selected {
    background: #007bff;
    color: white;
}

/* 搜索框样式 */
#changelist-search {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

#searchbar {
    width: 100%;
    max-width: 300px;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    margin-right: 10px;
}

/* 内联表单样式 */
.inline-group {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 20px;
    overflow: hidden;
}

.inline-group .tabular {
    margin: 0;
}

.inline-group h2 {
    background: #f8f9fa;
    color: #495057;
    padding: 10px 15px;
    margin: 0;
    border-bottom: 1px solid #dee2e6;
    font-size: 14px;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .results table {
        font-size: 12px;
    }
    
    .results table th,
    .results table td {
        padding: 6px 8px;
    }
    
    #changelist-filter {
        margin-top: 20px;
    }
    
    .button, input[type=submit], input[type=button], .submit-row input, a.button {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .results table {
        background-color: #2d2d2d;
        color: #e0e0e0;
    }
    
    .results table th {
        background: linear-gradient(to bottom, #3d3d3d, #2d2d2d);
        color: #e0e0e0;
        border-bottom-color: #4d4d4d;
    }
    
    .results table tr:nth-child(even) {
        background-color: #2a2a2a;
    }
    
    .results table tr:hover {
        background-color: #3a3a3a;
    }
    
    input[type="text"], input[type="email"], input[type="password"], input[type="url"], 
    input[type="number"], input[type="tel"], textarea, select {
        background-color: #2d2d2d;
        border-color: #4d4d4d;
        color: #e0e0e0;
    }
    
    #changelist-filter {
        background-color: #2d2d2d;
        border-color: #4d4d4d;
    }
    
    #changelist-search {
        background-color: #2d2d2d;
        border-color: #4d4d4d;
    }
}
