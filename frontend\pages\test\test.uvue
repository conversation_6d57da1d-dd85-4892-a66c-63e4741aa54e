<template>
	<view class="test-page">
		<view class="header">
			<text class="title">API测试页面</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">轮播图API测试</text>
			<button @click="testBannerApi" class="test-btn">测试轮播图API</button>
			
			<view v-if="bannerLoading" class="loading">
				<text>加载中...</text>
			</view>
			
			<view v-if="bannerError" class="error">
				<text>错误: {{ bannerError }}</text>
			</view>
			
			<view v-if="bannerData.length > 0" class="result">
				<text class="result-title">轮播图数据:</text>
				<view v-for="(banner, index) in bannerData" :key="index" class="banner-item">
					<text class="banner-title">{{ banner.title }}</text>
					<text class="banner-url">{{ banner.image }}</text>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">配置信息</text>
			<view class="config-info">
				<text>API地址: {{ config.API_BASE_URL }}</text>
				<text>CDN地址: {{ config.CDN_BASE_URL }}</text>
				<text>当前环境: {{ currentEnv }}</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { http } from '@/utils/request'
import { CONFIG, getEnv } from '@/utils/config'

// 响应式数据
const bannerLoading = ref(false)
const bannerError = ref('')
const bannerData = ref<any[]>([])
const config = ref(CONFIG)
const currentEnv = ref(getEnv())

// 测试轮播图API
const testBannerApi = async () => {
	bannerLoading.value = true
	bannerError.value = ''
	bannerData.value = []
	
	try {
		console.log('开始测试轮播图API...')
		console.log('API地址:', config.value.API_BASE_URL)
		
		const response = await http.get('/core/banners/', {
			position: 'home'
		})
		
		console.log('API响应:', response)
		
		if (response.code === 0 && response.data) {
			bannerData.value = response.data
			console.log('轮播图数据:', bannerData.value)
		} else {
			bannerError.value = response.message || '获取数据失败'
		}
	} catch (error: any) {
		console.error('API调用失败:', error)
		bannerError.value = error.message || '网络请求失败'
	} finally {
		bannerLoading.value = false
	}
}

// 页面加载时自动测试
onMounted(() => {
	console.log('测试页面已加载')
	console.log('当前配置:', config.value)
	console.log('当前协议检测结果:', config.value.API_BASE_URL.startsWith('http://') ? 'HTTP' : 'HTTPS')
	testBannerApi()
})
</script>

<style lang="scss" scoped>
.test-page {
	padding: 20px;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30px;
	
	.title {
		font-size: 24px;
		font-weight: bold;
		color: #333;
	}
}

.test-section {
	background-color: white;
	border-radius: 8px;
	padding: 20px;
	margin-bottom: 20px;
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
	
	.section-title {
		font-size: 18px;
		font-weight: bold;
		color: #333;
		margin-bottom: 15px;
		display: block;
	}
}

.test-btn {
	background-color: #007aff;
	color: white;
	border: none;
	border-radius: 6px;
	padding: 12px 24px;
	font-size: 16px;
	margin-bottom: 15px;
}

.loading {
	text-align: center;
	color: #666;
	padding: 20px;
}

.error {
	background-color: #ffebee;
	color: #c62828;
	padding: 10px;
	border-radius: 4px;
	margin-bottom: 15px;
}

.result {
	.result-title {
		font-weight: bold;
		color: #333;
		margin-bottom: 10px;
		display: block;
	}
}

.banner-item {
	background-color: #f8f9fa;
	padding: 10px;
	border-radius: 4px;
	margin-bottom: 10px;
	
	.banner-title {
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 5px;
	}
	
	.banner-url {
		color: #666;
		font-size: 12px;
		word-break: break-all;
		display: block;
	}
}

.config-info {
	text {
		display: block;
		margin-bottom: 8px;
		color: #666;
		font-size: 14px;
	}
}
</style>
