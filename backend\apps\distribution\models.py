from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid
from apps.core.models import BaseModel

User = get_user_model()


class DistributionConfig(BaseModel):
    """分销配置模型"""
    name = models.CharField(verbose_name='配置名称', max_length=100, help_text='配置项名称')
    key = models.CharField(verbose_name='配置键', max_length=50, unique=True, help_text='配置键名')
    value = models.TextField(verbose_name='配置值', help_text='配置项的值')
    description = models.TextField(verbose_name='配置说明', blank=True, help_text='配置项说明')
    is_active = models.BooleanField(verbose_name='是否启用', default=True, help_text='是否启用此配置')

    class Meta:
        verbose_name = '分销配置'
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['key']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.name}({self.key})"


class DistributionLevel(BaseModel):
    """分销等级模型"""
    name = models.CharField(verbose_name='等级名称', max_length=50, help_text='分销等级名称')
    level = models.PositiveIntegerField(verbose_name='等级数值', unique=True, help_text='等级数值，数值越大等级越高')
    commission_rate = models.DecimalField(
        verbose_name='佣金比例',
        max_digits=5,
        decimal_places=4,
        validators=[MinValueValidator(Decimal('0')), MaxValueValidator(Decimal('1'))],
        help_text='佣金比例，0-1之间的小数'
    )
    min_sales = models.DecimalField(
        verbose_name='最低销售额',
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0'))],
        help_text='达到此等级所需的最低销售额'
    )
    min_referrals = models.PositiveIntegerField(
        verbose_name='最低推荐人数',
        default=0,
        help_text='达到此等级所需的最低推荐人数'
    )
    description = models.TextField(verbose_name='等级描述', blank=True, help_text='等级说明')
    is_active = models.BooleanField(verbose_name='是否启用', default=True, help_text='是否启用此等级')

    class Meta:
        verbose_name = '分销等级'
        verbose_name_plural = verbose_name
        ordering = ['level']
        indexes = [
            models.Index(fields=['level']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.name}(L{self.level})"


class DistributionRelation(BaseModel):
    """分销关系模型"""
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='distribution_relation',
        verbose_name='用户',
        help_text='分销商用户'
    )
    parent = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='children',
        verbose_name='上级分销商',
        help_text='推荐人/上级分销商'
    )
    level = models.ForeignKey(
        DistributionLevel,
        on_delete=models.PROTECT,
        related_name='distributors',
        verbose_name='分销等级',
        help_text='当前分销等级'
    )
    invitation_code = models.CharField(
        verbose_name='邀请码',
        max_length=20,
        unique=True,
        help_text='个人专属邀请码'
    )
    total_sales = models.DecimalField(
        verbose_name='累计销售额',
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0'))],
        help_text='累计销售金额'
    )
    total_commission = models.DecimalField(
        verbose_name='累计佣金',
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0'))],
        help_text='累计获得佣金'
    )
    available_commission = models.DecimalField(
        verbose_name='可提现佣金',
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0'))],
        help_text='可提现的佣金余额'
    )
    referral_count = models.PositiveIntegerField(
        verbose_name='推荐人数',
        default=0,
        help_text='直接推荐的用户数量'
    )
    is_active = models.BooleanField(verbose_name='是否激活', default=True, help_text='分销商是否激活')
    activated_at = models.DateTimeField(verbose_name='激活时间', null=True, blank=True, help_text='分销商激活时间')

    class Meta:
        verbose_name = '分销关系'
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['parent']),
            models.Index(fields=['invitation_code']),
            models.Index(fields=['is_active']),
            models.Index(fields=['level']),
        ]

    def __str__(self):
        return f"{self.user.username}的分销关系"

    def save(self, *args, **kwargs):
        """保存时自动生成邀请码"""
        if not self.invitation_code:
            self.invitation_code = self.generate_invitation_code()
        super().save(*args, **kwargs)

    def generate_invitation_code(self):
        """生成唯一邀请码"""
        import random
        import string
        while True:
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
            if not DistributionRelation.objects.filter(invitation_code=code).exists():
                return code

    def get_children_count(self):
        """获取下级分销商数量"""
        return self.children.filter(is_active=True).count()

    def get_total_team_count(self):
        """获取团队总人数（递归计算）"""
        count = 0
        for child in self.children.filter(is_active=True):
            count += 1 + child.get_total_team_count()
        return count


class CommissionRecord(BaseModel):
    """佣金记录模型"""
    COMMISSION_TYPES = [
        ('direct', '直推佣金'),
        ('indirect', '间推佣金'),
        ('team', '团队佣金'),
        ('bonus', '奖励佣金'),
        ('activity', '活动佣金'),
    ]

    STATUS_CHOICES = [
        ('pending', '待结算'),
        ('confirmed', '已确认'),
        ('settled', '已结算'),
        ('cancelled', '已取消'),
        ('frozen', '已冻结'),
    ]

    distributor = models.ForeignKey(
        DistributionRelation,
        on_delete=models.CASCADE,
        related_name='commission_records',
        verbose_name='分销商',
        help_text='获得佣金的分销商'
    )
    order = models.ForeignKey(
        'orders.Order',
        on_delete=models.CASCADE,
        related_name='commission_records',
        verbose_name='关联订单',
        help_text='产生佣金的订单'
    )
    commission_type = models.CharField(
        verbose_name='佣金类型',
        max_length=20,
        choices=COMMISSION_TYPES,
        help_text='佣金类型'
    )
    commission_rate = models.DecimalField(
        verbose_name='佣金比例',
        max_digits=5,
        decimal_places=4,
        validators=[MinValueValidator(Decimal('0')), MaxValueValidator(Decimal('1'))],
        help_text='计算佣金时使用的比例'
    )
    order_amount = models.DecimalField(
        verbose_name='订单金额',
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text='计算佣金的订单金额'
    )
    commission_amount = models.DecimalField(
        verbose_name='佣金金额',
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text='实际佣金金额'
    )
    status = models.CharField(
        verbose_name='状态',
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text='佣金状态'
    )
    settled_at = models.DateTimeField(verbose_name='结算时间', null=True, blank=True, help_text='佣金结算时间')
    remark = models.TextField(verbose_name='备注', blank=True, help_text='佣金备注信息')

    class Meta:
        verbose_name = '佣金记录'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['distributor', 'status']),
            models.Index(fields=['order']),
            models.Index(fields=['commission_type']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.distributor.user.username} - {self.get_commission_type_display()} - ¥{self.commission_amount}"

    def can_settle(self):
        """判断是否可以结算"""
        return self.status == 'confirmed'

    def settle(self):
        """结算佣金"""
        if self.can_settle():
            from django.utils import timezone
            self.status = 'settled'
            self.settled_at = timezone.now()
            self.save()

            # 更新分销商的可提现佣金
            self.distributor.available_commission += self.commission_amount
            self.distributor.save()

            return True
        return False


class WithdrawalRecord(BaseModel):
    """提现记录模型"""
    STATUS_CHOICES = [
        ('pending', '待审核'),
        ('approved', '已审核'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('rejected', '已拒绝'),
        ('cancelled', '已取消'),
    ]

    WITHDRAWAL_METHODS = [
        ('wechat', '微信零钱'),
        ('alipay', '支付宝'),
        ('bank', '银行卡'),
    ]

    distributor = models.ForeignKey(
        DistributionRelation,
        on_delete=models.CASCADE,
        related_name='withdrawal_records',
        verbose_name='分销商',
        help_text='申请提现的分销商'
    )
    withdrawal_no = models.CharField(
        verbose_name='提现单号',
        max_length=32,
        unique=True,
        help_text='系统生成的提现单号'
    )
    amount = models.DecimalField(
        verbose_name='提现金额',
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text='申请提现的金额'
    )
    fee = models.DecimalField(
        verbose_name='手续费',
        max_digits=8,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0'))],
        help_text='提现手续费'
    )
    actual_amount = models.DecimalField(
        verbose_name='实际到账金额',
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text='扣除手续费后的实际金额'
    )
    method = models.CharField(
        verbose_name='提现方式',
        max_length=20,
        choices=WITHDRAWAL_METHODS,
        help_text='提现方式'
    )
    account_info = models.JSONField(
        verbose_name='账户信息',
        help_text='提现账户信息（加密存储）'
    )
    status = models.CharField(
        verbose_name='状态',
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text='提现状态'
    )
    processed_at = models.DateTimeField(verbose_name='处理时间', null=True, blank=True, help_text='提现处理时间')
    remark = models.TextField(verbose_name='备注', blank=True, help_text='提现备注')
    admin_remark = models.TextField(verbose_name='管理员备注', blank=True, help_text='管理员处理备注')

    class Meta:
        verbose_name = '提现记录'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['distributor', 'status']),
            models.Index(fields=['withdrawal_no']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.distributor.user.username} - 提现¥{self.amount}"

    def save(self, *args, **kwargs):
        """保存时自动生成提现单号和计算实际金额"""
        if not self.withdrawal_no:
            self.withdrawal_no = self.generate_withdrawal_no()
        self.actual_amount = self.amount - self.fee
        super().save(*args, **kwargs)

    @staticmethod
    def generate_withdrawal_no():
        """生成唯一提现单号"""
        import time
        timestamp = str(int(time.time()))
        random_str = str(uuid.uuid4().hex)[:8].upper()
        return f"WD{timestamp}{random_str}"


class PromotionCode(BaseModel):
    """推广码模型"""
    CODE_TYPES = [
        ('personal', '个人推广码'),
        ('activity', '活动推广码'),
        ('product', '产品推广码'),
        ('category', '分类推广码'),
    ]

    STATUS_CHOICES = [
        ('active', '有效'),
        ('inactive', '无效'),
        ('expired', '已过期'),
        ('disabled', '已禁用'),
    ]

    distributor = models.ForeignKey(
        DistributionRelation,
        on_delete=models.CASCADE,
        related_name='promotion_codes',
        verbose_name='分销商',
        help_text='推广码所属分销商'
    )
    code = models.CharField(
        verbose_name='推广码',
        max_length=20,
        unique=True,
        help_text='推广码内容'
    )
    name = models.CharField(verbose_name='推广码名称', max_length=100, help_text='推广码显示名称')
    code_type = models.CharField(
        verbose_name='推广码类型',
        max_length=20,
        choices=CODE_TYPES,
        help_text='推广码类型'
    )
    target_id = models.PositiveIntegerField(
        verbose_name='目标ID',
        null=True,
        blank=True,
        help_text='关联的产品或分类ID'
    )
    commission_rate = models.DecimalField(
        verbose_name='佣金比例',
        max_digits=5,
        decimal_places=4,
        validators=[MinValueValidator(Decimal('0')), MaxValueValidator(Decimal('1'))],
        null=True,
        blank=True,
        help_text='特殊佣金比例，为空则使用默认比例'
    )
    usage_limit = models.PositiveIntegerField(
        verbose_name='使用次数限制',
        null=True,
        blank=True,
        help_text='推广码使用次数限制，为空表示无限制'
    )
    used_count = models.PositiveIntegerField(
        verbose_name='已使用次数',
        default=0,
        help_text='推广码已使用次数'
    )
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True, help_text='推广码生效时间')
    end_time = models.DateTimeField(verbose_name='结束时间', null=True, blank=True, help_text='推广码失效时间')
    status = models.CharField(
        verbose_name='状态',
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        help_text='推广码状态'
    )
    description = models.TextField(verbose_name='描述', blank=True, help_text='推广码描述')

    class Meta:
        verbose_name = '推广码'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['distributor']),
            models.Index(fields=['code']),
            models.Index(fields=['code_type']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.name}({self.code})"

    def is_valid(self):
        """检查推广码是否有效"""
        from django.utils import timezone
        now = timezone.now()

        # 检查状态
        if self.status != 'active':
            return False

        # 检查时间范围
        if self.start_time and now < self.start_time:
            return False
        if self.end_time and now > self.end_time:
            return False

        # 检查使用次数
        if self.usage_limit and self.used_count >= self.usage_limit:
            return False

        return True

    def use(self):
        """使用推广码"""
        if self.is_valid():
            self.used_count += 1
            self.save()
            return True
        return False


class MarketingActivity(BaseModel):
    """营销活动模型"""
    ACTIVITY_TYPES = [
        ('discount', '折扣活动'),
        ('cashback', '返现活动'),
        ('commission_boost', '佣金加成'),
        ('referral_bonus', '推荐奖励'),
        ('group_buy', '团购活动'),
    ]

    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('active', '进行中'),
        ('paused', '已暂停'),
        ('ended', '已结束'),
        ('cancelled', '已取消'),
    ]

    name = models.CharField(verbose_name='活动名称', max_length=200, help_text='营销活动名称')
    activity_type = models.CharField(
        verbose_name='活动类型',
        max_length=20,
        choices=ACTIVITY_TYPES,
        help_text='营销活动类型'
    )
    description = models.TextField(verbose_name='活动描述', help_text='活动详细描述')
    rules = models.JSONField(verbose_name='活动规则', help_text='活动规则配置（JSON格式）')
    start_time = models.DateTimeField(verbose_name='开始时间', help_text='活动开始时间')
    end_time = models.DateTimeField(verbose_name='结束时间', help_text='活动结束时间')
    target_users = models.JSONField(
        verbose_name='目标用户',
        default=dict,
        help_text='目标用户条件（JSON格式）'
    )
    budget = models.DecimalField(
        verbose_name='活动预算',
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0'))],
        help_text='活动预算金额'
    )
    used_budget = models.DecimalField(
        verbose_name='已用预算',
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0'))],
        help_text='已使用的预算金额'
    )
    participant_count = models.PositiveIntegerField(
        verbose_name='参与人数',
        default=0,
        help_text='活动参与人数'
    )
    status = models.CharField(
        verbose_name='状态',
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        help_text='活动状态'
    )
    creator = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_activities',
        verbose_name='创建者',
        help_text='活动创建者'
    )

    class Meta:
        verbose_name = '营销活动'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['activity_type']),
            models.Index(fields=['status']),
            models.Index(fields=['start_time', 'end_time']),
            models.Index(fields=['creator']),
        ]

    def __str__(self):
        return self.name

    def is_active(self):
        """检查活动是否进行中"""
        from django.utils import timezone
        now = timezone.now()
        return (
            self.status == 'active' and
            self.start_time <= now <= self.end_time
        )

    def can_participate(self, user):
        """检查用户是否可以参与活动"""
        if not self.is_active():
            return False

        # 检查预算限制
        if self.budget and self.used_budget >= self.budget:
            return False

        # 检查目标用户条件（这里可以根据具体需求扩展）
        # 例如：检查用户等级、注册时间、购买历史等

        return True
