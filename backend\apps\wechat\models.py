from django.db import models
from django.core.cache import cache
from apps.core.models import BaseModel
from apps.users.models import User


class WechatUser(BaseModel):
    """微信用户信息表"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='wechat_profile', verbose_name='关联用户')
    openid = models.CharField(max_length=100, unique=True, verbose_name='微信OpenID')
    unionid = models.CharField(max_length=100, blank=True, null=True, verbose_name='微信UnionID')
    nickname = models.CharField(max_length=100, blank=True, null=True, verbose_name='微信昵称')
    avatar_url = models.URLField(blank=True, null=True, verbose_name='微信头像URL')
    gender = models.IntegerField(choices=[(0, '未知'), (1, '男'), (2, '女')], default=0, verbose_name='性别')
    city = models.CharField(max_length=50, blank=True, null=True, verbose_name='城市')
    province = models.CharField(max_length=50, blank=True, null=True, verbose_name='省份')
    country = models.CharField(max_length=50, blank=True, null=True, verbose_name='国家')
    language = models.CharField(max_length=20, default='zh_CN', verbose_name='语言')
    subscribe = models.BooleanField(default=False, verbose_name='是否关注公众号')
    subscribe_time = models.DateTimeField(blank=True, null=True, verbose_name='关注时间')
    
    class Meta:
        verbose_name = '微信用户'
        verbose_name_plural = '微信用户'
        indexes = [
            models.Index(fields=['openid']),
            models.Index(fields=['unionid']),
        ]
    
    def __str__(self):
        return f"{self.nickname or self.openid} - {self.user.username}"


class WechatMessage(BaseModel):
    """微信消息记录表"""
    MESSAGE_TYPES = [
        ('text', '文本消息'),
        ('image', '图片消息'),
        ('voice', '语音消息'),
        ('video', '视频消息'),
        ('music', '音乐消息'),
        ('news', '图文消息'),
        ('location', '位置消息'),
        ('link', '链接消息'),
        ('event', '事件消息'),
    ]
    
    DIRECTION_CHOICES = [
        ('in', '接收'),
        ('out', '发送'),
    ]
    
    wechat_user = models.ForeignKey(WechatUser, on_delete=models.CASCADE, related_name='messages', verbose_name='微信用户')
    msg_id = models.CharField(max_length=100, unique=True, verbose_name='消息ID')
    msg_type = models.CharField(max_length=20, choices=MESSAGE_TYPES, verbose_name='消息类型')
    direction = models.CharField(max_length=3, choices=DIRECTION_CHOICES, verbose_name='消息方向')
    content = models.TextField(verbose_name='消息内容')
    media_id = models.CharField(max_length=100, blank=True, null=True, verbose_name='媒体ID')
    pic_url = models.URLField(blank=True, null=True, verbose_name='图片URL')
    location_x = models.FloatField(blank=True, null=True, verbose_name='地理位置纬度')
    location_y = models.FloatField(blank=True, null=True, verbose_name='地理位置经度')
    scale = models.IntegerField(blank=True, null=True, verbose_name='地图缩放大小')
    label = models.CharField(max_length=200, blank=True, null=True, verbose_name='地理位置信息')
    title = models.CharField(max_length=200, blank=True, null=True, verbose_name='消息标题')
    description = models.TextField(blank=True, null=True, verbose_name='消息描述')
    url = models.URLField(blank=True, null=True, verbose_name='消息链接')
    event = models.CharField(max_length=50, blank=True, null=True, verbose_name='事件类型')
    event_key = models.CharField(max_length=200, blank=True, null=True, verbose_name='事件KEY值')
    
    class Meta:
        verbose_name = '微信消息'
        verbose_name_plural = '微信消息'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['msg_id']),
            models.Index(fields=['wechat_user', '-created_at']),
            models.Index(fields=['msg_type']),
            models.Index(fields=['direction']),
        ]
    
    def __str__(self):
        return f"{self.wechat_user.nickname} - {self.get_msg_type_display()} - {self.created_at}"


class WechatMenu(BaseModel):
    """微信菜单配置表"""
    MENU_TYPES = [
        ('click', '点击推事件'),
        ('view', '跳转URL'),
        ('scancode_push', '扫码推事件'),
        ('scancode_waitmsg', '扫码推事件且弹出"消息接收中"提示框'),
        ('pic_sysphoto', '弹出系统拍照发图'),
        ('pic_photo_or_album', '弹出拍照或者相册发图'),
        ('pic_weixin', '弹出微信相册发图器'),
        ('location_select', '弹出地理位置选择器'),
        ('media_id', '下发消息（除文本消息）'),
        ('view_limited', '跳转图文消息URL'),
    ]
    
    name = models.CharField(max_length=40, verbose_name='菜单标题')
    type = models.CharField(max_length=20, choices=MENU_TYPES, blank=True, null=True, verbose_name='菜单类型')
    key = models.CharField(max_length=128, blank=True, null=True, verbose_name='菜单KEY值')
    url = models.URLField(blank=True, null=True, verbose_name='网页链接')
    media_id = models.CharField(max_length=100, blank=True, null=True, verbose_name='媒体文件ID')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='children', verbose_name='父菜单')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    class Meta:
        verbose_name = '微信菜单'
        verbose_name_plural = '微信菜单'
        ordering = ['sort_order', 'id']
    
    def __str__(self):
        return self.name


class WechatTemplate(BaseModel):
    """微信模板消息配置表"""
    template_id = models.CharField(max_length=100, unique=True, verbose_name='模板ID')
    title = models.CharField(max_length=100, verbose_name='模板标题')
    primary_industry = models.CharField(max_length=50, verbose_name='主行业')
    deputy_industry = models.CharField(max_length=50, verbose_name='副行业')
    content = models.TextField(verbose_name='模板内容')
    example = models.TextField(blank=True, null=True, verbose_name='模板示例')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    class Meta:
        verbose_name = '微信模板消息'
        verbose_name_plural = '微信模板消息'
    
    def __str__(self):
        return self.title


class WechatTemplateMessage(BaseModel):
    """微信模板消息发送记录表"""
    STATUS_CHOICES = [
        ('pending', '待发送'),
        ('sent', '已发送'),
        ('failed', '发送失败'),
    ]
    
    wechat_user = models.ForeignKey(WechatUser, on_delete=models.CASCADE, related_name='template_messages', verbose_name='微信用户')
    template = models.ForeignKey(WechatTemplate, on_delete=models.CASCADE, related_name='messages', verbose_name='模板')
    data = models.JSONField(verbose_name='模板数据')
    url = models.URLField(blank=True, null=True, verbose_name='跳转链接')
    miniprogram = models.JSONField(blank=True, null=True, verbose_name='小程序信息')
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending', verbose_name='发送状态')
    msg_id = models.CharField(max_length=100, blank=True, null=True, verbose_name='消息ID')
    error_msg = models.TextField(blank=True, null=True, verbose_name='错误信息')
    sent_at = models.DateTimeField(blank=True, null=True, verbose_name='发送时间')
    
    class Meta:
        verbose_name = '模板消息记录'
        verbose_name_plural = '模板消息记录'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.wechat_user.nickname} - {self.template.title} - {self.get_status_display()}"


class WechatConfig(BaseModel):
    """微信配置表"""
    PLATFORM_CHOICES = [
        ('official', '公众号'),
        ('miniprogram', '小程序'),
        ('work', '企业微信'),
        ('payment', '微信支付'),
    ]

    platform = models.CharField(max_length=20, choices=PLATFORM_CHOICES, verbose_name='平台类型')
    name = models.CharField(max_length=100, verbose_name='配置名称')
    app_id = models.CharField(max_length=100, blank=True, null=True, verbose_name='应用ID')
    app_secret = models.CharField(max_length=200, blank=True, null=True, verbose_name='应用密钥')
    token = models.CharField(max_length=100, blank=True, null=True, verbose_name='Token')
    encoding_aes_key = models.CharField(max_length=200, blank=True, null=True, verbose_name='消息加解密密钥')

    # 企业微信专用
    corp_id = models.CharField(max_length=100, blank=True, null=True, verbose_name='企业ID')
    corp_secret = models.CharField(max_length=200, blank=True, null=True, verbose_name='企业密钥')
    agent_id = models.CharField(max_length=50, blank=True, null=True, verbose_name='应用ID')

    # 微信支付专用
    mch_id = models.CharField(max_length=50, blank=True, null=True, verbose_name='商户号')
    mch_key = models.CharField(max_length=200, blank=True, null=True, verbose_name='商户密钥')
    cert_path = models.CharField(max_length=500, blank=True, null=True, verbose_name='证书路径')
    key_path = models.CharField(max_length=500, blank=True, null=True, verbose_name='私钥路径')

    # 其他配置
    extra_config = models.JSONField(default=dict, blank=True, verbose_name='额外配置')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    description = models.TextField(blank=True, null=True, verbose_name='配置说明')

    class Meta:
        verbose_name = '微信配置'
        verbose_name_plural = '微信配置'
        unique_together = ['platform', 'name']
        ordering = ['platform', 'name']

    def __str__(self):
        return f"{self.get_platform_display()} - {self.name}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # 清除缓存
        cache.delete(f'wechat_config_{self.platform}_{self.name}')
        cache.delete(f'wechat_config_{self.platform}')

    @classmethod
    def get_config(cls, platform, name='default'):
        """获取配置"""
        cache_key = f'wechat_config_{platform}_{name}'
        config = cache.get(cache_key)

        if config is None:
            try:
                config_obj = cls.objects.get(platform=platform, name=name, is_active=True)
                config = {
                    'app_id': config_obj.app_id,
                    'app_secret': config_obj.app_secret,
                    'token': config_obj.token,
                    'encoding_aes_key': config_obj.encoding_aes_key,
                    'corp_id': config_obj.corp_id,
                    'corp_secret': config_obj.corp_secret,
                    'agent_id': config_obj.agent_id,
                    'mch_id': config_obj.mch_id,
                    'mch_key': config_obj.mch_key,
                    'cert_path': config_obj.cert_path,
                    'key_path': config_obj.key_path,
                    **config_obj.extra_config
                }
                cache.set(cache_key, config, 3600)  # 缓存1小时
            except cls.DoesNotExist:
                config = {}
                cache.set(cache_key, config, 300)  # 缓存5分钟

        return config

    @classmethod
    def get_platform_configs(cls, platform):
        """获取平台所有配置"""
        cache_key = f'wechat_config_{platform}'
        configs = cache.get(cache_key)

        if configs is None:
            config_objs = cls.objects.filter(platform=platform, is_active=True)
            configs = {}
            for config_obj in config_objs:
                configs[config_obj.name] = {
                    'app_id': config_obj.app_id,
                    'app_secret': config_obj.app_secret,
                    'token': config_obj.token,
                    'encoding_aes_key': config_obj.encoding_aes_key,
                    'corp_id': config_obj.corp_id,
                    'corp_secret': config_obj.corp_secret,
                    'agent_id': config_obj.agent_id,
                    'mch_id': config_obj.mch_id,
                    'mch_key': config_obj.mch_key,
                    'cert_path': config_obj.cert_path,
                    'key_path': config_obj.key_path,
                    **config_obj.extra_config
                }
            cache.set(cache_key, configs, 3600)  # 缓存1小时

        return configs
