/**
 * 产品状态管理
 * 管理产品列表、分类、搜索等
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import http from '@/utils/request'
import { getCdnUrl } from '@/utils/config'

// 产品接口
export interface Product {
  id: number
  name: string
  description: string
  price: number
  original_price?: number
  stock: number
  sales: number
  images: string[]
  category_id: number
  category_name: string
  status: string
  is_hot: boolean
  is_new: boolean
  is_recommended: boolean
  specs?: ProductSpec[]
  created_at: string
  updated_at: string
}

// 产品规格接口
export interface ProductSpec {
  id: number
  name: string
  price: number
  stock: number
  image?: string
  attrs: Record<string, string>
}

// 产品分类接口
export interface Category {
  id: number
  name: string
  icon?: string
  image?: string
  parent_id?: number
  children?: Category[]
  sort: number
}

// 搜索历史接口
export interface SearchHistory {
  keyword: string
  timestamp: number
}

export const useProductStore = defineStore('product', () => {
  // 状态
  const categories = ref<Category[]>([])
  const products = ref<Product[]>([])
  const currentProduct = ref<Product | null>(null)
  const searchHistory = ref<SearchHistory[]>([])
  const hotKeywords = ref<string[]>([])
  const isLoading = ref(false)
  const hasMore = ref(true)
  const currentPage = ref(1)
  const pageSize = ref(20)

  // 计算属性
  const productImages = computed(() => {
    return (images: string[]) => {
      return images.map(img => getCdnUrl(img))
    }
  })

  const productMainImage = computed(() => {
    return (product: Product) => {
      return product.images.length > 0 
        ? getCdnUrl(product.images[0])
        : '/static/images/default-product.png'
    }
  })

  const categoryTree = computed(() => {
    const tree: Category[] = []
    const map = new Map<number, Category>()
    
    // 先创建映射
    categories.value.forEach(category => {
      map.set(category.id, { ...category, children: [] })
    })
    
    // 构建树形结构
    categories.value.forEach(category => {
      const item = map.get(category.id)!
      if (category.parent_id) {
        const parent = map.get(category.parent_id)
        if (parent) {
          parent.children!.push(item)
        }
      } else {
        tree.push(item)
      }
    })
    
    return tree
  })

  // 方法
  /**
   * 获取产品分类
   */
  const getCategories = async (): Promise<boolean> => {
    try {
      const response = await http.get('/products/categories')
      
      if (response.success) {
        categories.value = response.data
        return true
      }
      
      return false
    } catch (error) {
      console.error('获取产品分类失败:', error)
      return false
    }
  }

  /**
   * 获取产品列表
   */
  const getProducts = async (params: {
    page?: number
    page_size?: number
    category_id?: number
    keyword?: string
    is_hot?: boolean
    is_new?: boolean
    is_recommended?: boolean
    min_price?: number
    max_price?: number
    sort?: string
  } = {}): Promise<boolean> => {
    try {
      isLoading.value = true
      
      const queryParams = {
        page: params.page || currentPage.value,
        page_size: params.page_size || pageSize.value,
        ...params
      }
      
      const response = await http.get('/products/list', queryParams)
      
      if (response.success) {
        const { data, pagination } = response.data
        
        if (queryParams.page === 1) {
          products.value = data
        } else {
          products.value.push(...data)
        }
        
        currentPage.value = pagination.current_page
        hasMore.value = pagination.current_page < pagination.total_pages
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('获取产品列表失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 获取产品详情
   */
  const getProductDetail = async (id: number): Promise<boolean> => {
    try {
      const response = await http.get(`/products/${id}`)
      
      if (response.success) {
        currentProduct.value = response.data
        
        // 添加到浏览历史
        addBrowseHistory(response.data)
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('获取产品详情失败:', error)
      return false
    }
  }

  /**
   * 搜索产品
   */
  const searchProducts = async (keyword: string, page: number = 1): Promise<boolean> => {
    if (keyword.trim()) {
      // 添加到搜索历史
      addSearchHistory(keyword)
    }
    
    return getProducts({
      keyword: keyword.trim(),
      page
    })
  }

  /**
   * 获取热门搜索关键词
   */
  const getHotKeywords = async (): Promise<boolean> => {
    try {
      const response = await http.get('/products/hot-keywords')
      
      if (response.success) {
        hotKeywords.value = response.data
        return true
      }
      
      return false
    } catch (error) {
      console.error('获取热门关键词失败:', error)
      return false
    }
  }

  /**
   * 添加搜索历史
   */
  const addSearchHistory = (keyword: string): void => {
    if (!keyword.trim()) return
    
    // 移除已存在的相同关键词
    const filtered = searchHistory.value.filter(item => item.keyword !== keyword)
    
    // 添加到开头
    filtered.unshift({
      keyword,
      timestamp: Date.now()
    })
    
    // 限制历史记录数量
    searchHistory.value = filtered.slice(0, 10)
  }

  /**
   * 清除搜索历史
   */
  const clearSearchHistory = (): void => {
    searchHistory.value = []
  }

  /**
   * 删除单个搜索历史
   */
  const removeSearchHistory = (keyword: string): void => {
    searchHistory.value = searchHistory.value.filter(item => item.keyword !== keyword)
  }

  /**
   * 添加浏览历史
   */
  const addBrowseHistory = (product: Product): void => {
    // 这里可以实现浏览历史逻辑
    // 可以存储到本地或发送到服务器
  }

  /**
   * 重置产品列表
   */
  const resetProducts = (): void => {
    products.value = []
    currentPage.value = 1
    hasMore.value = true
  }

  /**
   * 加载更多产品
   */
  const loadMoreProducts = async (params: any = {}): Promise<boolean> => {
    if (!hasMore.value || isLoading.value) {
      return false
    }
    
    return getProducts({
      ...params,
      page: currentPage.value + 1
    })
  }

  /**
   * 获取推荐产品
   */
  const getRecommendedProducts = async (limit: number = 10): Promise<Product[]> => {
    try {
      const response = await http.get('/products/recommended', { limit })
      
      if (response.success) {
        return response.data
      }
      
      return []
    } catch (error) {
      console.error('获取推荐产品失败:', error)
      return []
    }
  }

  /**
   * 获取热门产品
   */
  const getHotProducts = async (limit: number = 10): Promise<Product[]> => {
    try {
      const response = await http.get('/products/hot', { limit })
      
      if (response.success) {
        return response.data
      }
      
      return []
    } catch (error) {
      console.error('获取热门产品失败:', error)
      return []
    }
  }

  /**
   * 获取新品
   */
  const getNewProducts = async (limit: number = 10): Promise<Product[]> => {
    try {
      const response = await http.get('/products/new', { limit })
      
      if (response.success) {
        return response.data
      }
      
      return []
    } catch (error) {
      console.error('获取新品失败:', error)
      return []
    }
  }

  return {
    // 状态
    categories,
    products,
    currentProduct,
    searchHistory,
    hotKeywords,
    isLoading,
    hasMore,
    currentPage,
    pageSize,
    
    // 计算属性
    productImages,
    productMainImage,
    categoryTree,
    
    // 方法
    getCategories,
    getProducts,
    getProductDetail,
    searchProducts,
    getHotKeywords,
    addSearchHistory,
    clearSearchHistory,
    removeSearchHistory,
    addBrowseHistory,
    resetProducts,
    loadMoreProducts,
    getRecommendedProducts,
    getHotProducts,
    getNewProducts
  }
}, {
  // 持久化配置
  persist: {
    key: 'product-store',
    storage: {
      getItem: (key: string) => uni.getStorageSync(key),
      setItem: (key: string, value: string) => uni.setStorageSync(key, value),
      removeItem: (key: string) => uni.removeStorageSync(key)
    },
    // 只持久化搜索历史
    paths: ['searchHistory']
  }
})
