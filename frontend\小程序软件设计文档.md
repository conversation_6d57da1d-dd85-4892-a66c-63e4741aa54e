# 智梦科技小程序端软件设计文档

## 项目概述

**项目名称**：智梦科技小程序端  
**技术栈**：uniapp-x + Vue3 + TypeScript  
**开发工具**：HBuilderX  
**目标平台**：微信小程序、支付宝小程序、百度小程序、字节跳动小程序  
**后端API**：Django REST Framework  

### 核心功能
- 🏠 首页展示
- 🛒 产品浏览与购买
- 🛍️ 购物车管理
- 📋 订单管理
- 👤 用户中心
- 💰 分销中心
- 🔐 微信登录
- 💳 微信支付
- 📱 分享推广

## 技术架构设计

### 前端技术栈
- **框架**：uniapp-x (Vue3 + TypeScript)
- **状态管理**：Pinia
- **网络请求**：uni.request封装
- **UI组件**：uni-ui + 自定义组件
- **样式**：SCSS + UnoCSS
- **图标**：代码图标 (CSS + Unicode)

### 项目结构设计
```
frontend/
├── App.uvue                    # 应用入口
├── main.uts                    # 主入口文件
├── manifest.json               # 应用配置
├── pages.json                  # 页面配置
├── uni.scss                    # 全局样式
├── index.html                  # H5入口
│
├── pages/                      # 页面目录
│   ├── index/                  # 首页
│   │   └── index.uvue
│   ├── auth/                   # 认证页面
│   │   ├── login.uvue          # 登录页
│   │   └── register.uvue       # 注册页
│   ├── products/               # 产品页面
│   │   ├── list.uvue           # 产品列表
│   │   ├── detail.uvue         # 产品详情
│   │   └── search.uvue         # 产品搜索
│   ├── cart/                   # 购物车
│   │   └── cart.uvue
│   ├── orders/                 # 订单页面
│   │   ├── list.uvue           # 订单列表
│   │   ├── detail.uvue         # 订单详情
│   │   └── confirm.uvue        # 订单确认
│   ├── user/                   # 用户中心
│   │   ├── profile.uvue        # 个人资料
│   │   ├── settings.uvue       # 设置页面
│   │   └── address.uvue        # 地址管理
│   └── distribution/           # 分销中心
│       ├── index.uvue          # 分销首页
│       ├── team.uvue           # 我的团队
│       ├── commission.uvue     # 佣金记录
│       └── promotion.uvue      # 推广工具
│
├── components/                 # 组件目录
│   ├── common/                 # 通用组件
│   │   ├── Header.uvue         # 页面头部
│   │   ├── Footer.uvue         # 页面底部
│   │   ├── Loading.uvue        # 加载组件
│   │   ├── Empty.uvue          # 空状态
│   │   ├── Modal.uvue          # 弹窗组件
│   │   └── Tabbar.uvue         # 底部导航
│   ├── product/                # 产品组件
│   │   ├── ProductCard.uvue    # 产品卡片
│   │   ├── ProductList.uvue    # 产品列表
│   │   └── ProductSku.uvue     # 规格选择
│   ├── order/                  # 订单组件
│   │   ├── OrderCard.uvue      # 订单卡片
│   │   └── OrderStatus.uvue    # 订单状态
│   └── distribution/           # 分销组件
│       ├── TeamCard.uvue       # 团队成员卡片
│       └── CommissionCard.uvue # 佣金记录卡片
│
├── utils/                      # 工具函数
│   ├── request.ts              # 网络请求封装
│   ├── auth.ts                 # 认证工具
│   ├── wechat.ts               # 微信API封装
│   ├── storage.ts              # 本地存储
│   ├── constants.ts            # 常量定义
│   ├── validator.ts            # 数据验证
│   └── format.ts               # 格式化工具
│
├── stores/                     # 状态管理
│   ├── index.ts                # store入口
│   ├── user.ts                 # 用户状态
│   ├── product.ts              # 产品状态
│   ├── cart.ts                 # 购物车状态
│   ├── order.ts                # 订单状态
│   └── distribution.ts         # 分销状态
│
├── types/                      # 类型定义
│   ├── api.ts                  # API类型
│   ├── user.ts                 # 用户类型
│   ├── product.ts              # 产品类型
│   ├── order.ts                # 订单类型
│   └── distribution.ts         # 分销类型
│
├── static/                     # 静态资源
│   ├── images/                 # 图片资源(仅必要图片)
│   └── fonts/                  # 字体文件(如需要)
│
└── tests/                      # 测试文件
    ├── unit/                   # 单元测试
    └── e2e/                    # 端到端测试
```

## 页面设计规范

### 页面配置标准
```json
{
  "path": "pages/xxx/xxx",
  "style": {
    "navigationBarTitleText": "页面标题",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black",
    "backgroundColor": "#f8f8f8",
    "enablePullDownRefresh": true,
    "onReachBottomDistance": 50
  }
}
```

### 全局样式规范
- **主色调**：#007AFF (蓝色)
- **辅助色**：#34C759 (绿色)、#FF3B30 (红色)
- **文字色**：#000000 (主文字)、#666666 (次要文字)、#999999 (辅助文字)
- **背景色**：#FFFFFF (主背景)、#F8F8F8 (次要背景)
- **边框色**：#E5E5E5
- **字体大小**：28rpx (正文)、32rpx (标题)、24rpx (辅助文字)

## API接口设计

### 环境配置
```typescript
// 环境配置
interface EnvConfig {
  API_BASE_URL: string      // API基础地址
  CDN_BASE_URL: string      // CDN基础地址
  WS_BASE_URL?: string      // WebSocket地址(如需要)
}

// 开发环境配置
const DEV_CONFIG: EnvConfig = {
  API_BASE_URL: 'http://zmkj.nat100.top/api/v1',
  CDN_BASE_URL: 'http://zmkj.nat100.top/media'
}

// 生产环境配置
const PROD_CONFIG: EnvConfig = {
  API_BASE_URL: 'https://gw.zmkj.live/api/v1',
  CDN_BASE_URL: 'https://imggw.zmkj.live'
}

// 当前环境配置
const ENV_CONFIG = process.env.NODE_ENV === 'production' ? PROD_CONFIG : DEV_CONFIG

// 接口基础配置
const API_TIMEOUT = 10000

// 请求头配置
const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
```

### 主要接口模块
1. **用户认证**：`/auth/`
2. **产品管理**：`/products/`
3. **订单管理**：`/orders/`
4. **购物车**：`/cart/`
5. **分销系统**：`/distribution/`
6. **微信功能**：`/wechat/`

## 状态管理设计

### Pinia Store结构
```typescript
// 用户状态
interface UserState {
  userInfo: UserInfo | null
  isLogin: boolean
  token: string
}

// 产品状态
interface ProductState {
  categories: Category[]
  products: Product[]
  currentProduct: Product | null
}

// 购物车状态
interface CartState {
  items: CartItem[]
  totalCount: number
  totalAmount: number
}
```

## 代码图标设计方案

### 图标实现方式
采用纯CSS + Unicode字符实现图标，无需引入图片文件，具有以下优势：
- **轻量级**：无需加载图片文件，减少包体积
- **可定制**：支持颜色、大小、动画等样式定制
- **高清适配**：矢量图标，支持任意分辨率
- **加载快速**：无网络请求，即时显示

### 图标组件设计
```typescript
// 图标组件接口
interface IconProps {
  name: string          // 图标名称
  size?: number         // 图标大小 (rpx)
  color?: string        // 图标颜色
  className?: string    // 自定义样式类
}

// 图标组件实现
<template>
  <text
    :class="['icon', `icon-${name}`, className]"
    :style="{ fontSize: size + 'rpx', color }"
  />
</template>
```

### 常用图标定义
```scss
// 图标基础样式
.icon {
  font-family: 'iconfont';
  font-style: normal;
  display: inline-block;
  vertical-align: middle;
}

// 具体图标定义 (使用Unicode字符)
.icon-home::before { content: '\e001'; }        // 首页
.icon-product::before { content: '\e002'; }     // 产品
.icon-cart::before { content: '\e003'; }        // 购物车
.icon-order::before { content: '\e004'; }       // 订单
.icon-user::before { content: '\e005'; }        // 用户
.icon-search::before { content: '\e006'; }      // 搜索
.icon-share::before { content: '\e007'; }       // 分享
.icon-star::before { content: '\e008'; }        // 收藏
.icon-location::before { content: '\e009'; }    // 位置
.icon-phone::before { content: '\e010'; }       // 电话
.icon-wechat::before { content: '\e011'; }      // 微信
.icon-money::before { content: '\e012'; }       // 金钱
.icon-team::before { content: '\e013'; }        // 团队
.icon-setting::before { content: '\e014'; }     // 设置
.icon-arrow-right::before { content: '\e015'; } // 右箭头
.icon-arrow-left::before { content: '\e016'; }  // 左箭头
.icon-close::before { content: '\e017'; }       // 关闭
.icon-check::before { content: '\e018'; }       // 选中
.icon-plus::before { content: '\e019'; }        // 加号
.icon-minus::before { content: '\e020'; }       // 减号
```

### 图标使用示例
```vue
<!-- 基础使用 -->
<Icon name="home" :size="32" color="#007AFF" />

<!-- 带样式类 -->
<Icon name="cart" :size="28" className="cart-icon" />

<!-- 在按钮中使用 -->
<button class="btn">
  <Icon name="share" :size="24" />
  <text>分享</text>
</button>
```

## 组件设计规范

### 组件命名规范
- **页面组件**：PascalCase (如：ProductList)
- **通用组件**：PascalCase + 功能描述 (如：LoadingSpinner)
- **业务组件**：业务前缀 + PascalCase (如：ProductCard)

### 组件属性设计
```typescript
// 组件Props接口
interface ComponentProps {
  // 必需属性
  title: string
  // 可选属性
  subtitle?: string
  // 事件回调
  onConfirm?: () => void
}
```

## 微信小程序特性

### 微信登录流程
1. 调用 `wx.login()` 获取 code
2. 发送 code 到后端换取 token
3. 存储用户信息和 token
4. 更新登录状态

### 微信支付流程
1. 创建订单获取支付参数
2. 调用 `wx.requestPayment()` 发起支付
3. 监听支付结果
4. 更新订单状态

### 微信分享功能
1. 设置分享参数
2. 监听分享事件
3. 记录分享数据
4. 处理分享回调

## 性能优化策略

### 页面加载优化
- 使用分包加载
- 图片懒加载
- 数据预加载
- 缓存策略

### 内存优化
- 及时清理定时器
- 避免内存泄漏
- 合理使用缓存

### 网络优化
- 请求合并
- 数据缓存
- 离线处理

## 安全策略

### 数据安全
- Token 安全存储
- 敏感信息加密
- 请求签名验证

### 接口安全
- 请求频率限制
- 参数验证
- 错误处理

## 测试策略

### 单元测试
- 工具函数测试
- 组件逻辑测试
- 状态管理测试

### 集成测试
- API接口测试
- 页面流程测试
- 用户场景测试

---

**文档版本**：v1.0  
**创建时间**：2025年7月28日  
**维护人员**：智梦科技开发团队
