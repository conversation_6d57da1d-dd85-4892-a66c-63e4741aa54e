from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from .models import User, UserAddress, UserLoginLog
from .serializers import (
    UserSerializer, UserRegisterSerializer, UserLoginSerializer,
    UserAddressSerializer, UserLoginLogSerializer, UserProfileUpdateSerializer
)

class UserViewSet(viewsets.ModelViewSet):
    """用户管理视图集"""
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """根据不同操作设置不同权限"""
        if self.action == 'create':
            return [permissions.AllowAny()]
        return super().get_permissions()

    def get_serializer_class(self):
        """根据不同操作返回不同序列化器"""
        if self.action == 'create':
            return UserRegisterSerializer
        elif self.action == 'update_profile':
            return UserProfileUpdateSerializer
        return UserSerializer

    def create(self, request, *args, **kwargs):
        """用户注册"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        refresh = RefreshToken.for_user(user)
        return Response({
            'user': UserSerializer(user).data,
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=['get'])
    def me(self, request):
        """获取当前用户信息"""
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)

    @action(detail=False, methods=['put'], url_path='profile')
    def update_profile(self, request):
        """更新用户资料"""
        serializer = self.get_serializer(request.user, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class UserLoginView(APIView):
    """用户登录视图"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = UserLoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # 获取登录凭证
        username = serializer.validated_data.get('username')
        phone = serializer.validated_data.get('phone')
        email = serializer.validated_data.get('email')
        password = serializer.validated_data.get('password')

        # 尝试使用不同方式认证
        user = None
        if username:
            user = authenticate(username=username, password=password)
        elif phone:
            try:
                user_obj = User.objects.get(phone=phone)
                user = authenticate(username=user_obj.username, password=password)
            except User.DoesNotExist:
                pass
        elif email:
            try:
                user_obj = User.objects.get(email=email)
                user = authenticate(username=user_obj.username, password=password)
            except User.DoesNotExist:
                pass

        if user is None:
            return Response({
                'error': '用户名、手机号码或邮箱不存在，或密码错误'
            }, status=status.HTTP_401_UNAUTHORIZED)

        # 记录登录日志
        UserLoginLog.objects.create(
            user=user,
            ip_address=request.META.get('REMOTE_ADDR'),
            device_info=request.META.get('HTTP_USER_AGENT', '')[:200],
            browser_info=request.META.get('HTTP_USER_AGENT', '')[:200]
        )

        # 生成JWT令牌
        refresh = RefreshToken.for_user(user)
        return Response({
            'user': UserSerializer(user).data,
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        })


class UserAddressViewSet(viewsets.ModelViewSet):
    """用户地址管理视图集"""
    serializer_class = UserAddressSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """只返回当前用户的地址"""
        return UserAddress.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        """创建地址时自动关联当前用户"""
        serializer.save(user=self.request.user)


class UserLoginLogViewSet(viewsets.ReadOnlyModelViewSet):
    """用户登录日志视图集（只读）"""
    serializer_class = UserLoginLogSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """只返回当前用户的登录日志"""
        return UserLoginLog.objects.filter(user=self.request.user).order_by('-login_time')