from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse, path
from django.utils.safestring import mark_safe
from django.http import HttpResponse
from django.template.response import TemplateResponse
from django.db.models import Sum, Count, Avg, Q
from django.utils import timezone
from decimal import Decimal
from .models import (
    DistributionConfig, DistributionLevel, DistributionRelation,
    CommissionRecord, WithdrawalRecord, PromotionCode, MarketingActivity
)


@admin.register(DistributionConfig)
class DistributionConfigAdmin(admin.ModelAdmin):
    """分销配置管理"""
    list_display = ['name', 'key', 'value', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'key']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'key', 'value', 'description')
        }),
        ('状态', {
            'fields': ('is_active',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(DistributionLevel)
class DistributionLevelAdmin(admin.ModelAdmin):
    """分销等级管理"""
    list_display = ['name', 'level', 'commission_rate', 'min_sales', 'min_referrals', 'distributor_count', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name']
    readonly_fields = ['created_at', 'updated_at', 'distributor_count']
    ordering = ['level']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'level', 'description')
        }),
        ('佣金设置', {
            'fields': ('commission_rate',)
        }),
        ('升级条件', {
            'fields': ('min_sales', 'min_referrals')
        }),
        ('状态', {
            'fields': ('is_active',)
        }),
        ('统计信息', {
            'fields': ('distributor_count',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def distributor_count(self, obj):
        """获取该等级的分销商数量"""
        count = obj.distributors.filter(is_active=True).count()
        return format_html('<span style="color: #007cba;">{}</span>', count)
    distributor_count.short_description = '分销商数量'


@admin.register(DistributionRelation)
class DistributionRelationAdmin(admin.ModelAdmin):
    """分销关系管理"""
    list_display = [
        'user_info', 'parent_info', 'level', 'invitation_code',
        'total_sales', 'total_commission', 'available_commission',
        'referral_count', 'team_size', 'is_active', 'activated_at'
    ]
    list_filter = ['level', 'is_active', 'activated_at', 'created_at']
    search_fields = ['user__username', 'user__nickname', 'invitation_code']
    readonly_fields = [
        'invitation_code', 'total_sales', 'total_commission',
        'available_commission', 'referral_count', 'team_size', 'created_at', 'updated_at'
    ]
    raw_id_fields = ['user', 'parent']
    actions = ['activate_distributors', 'deactivate_distributors', 'upgrade_level']

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'parent', 'level', 'invitation_code')
        }),
        ('统计信息', {
            'fields': ('total_sales', 'total_commission', 'available_commission', 'referral_count')
        }),
        ('状态', {
            'fields': ('is_active', 'activated_at')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def user_info(self, obj):
        """用户信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:users_user_change', args=[obj.user.pk]),
            obj.user.username
        )
    user_info.short_description = '用户'

    def parent_info(self, obj):
        """上级信息"""
        if obj.parent:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:distribution_distributionrelation_change', args=[obj.parent.pk]),
                obj.parent.user.username
            )
        return '-'
    parent_info.short_description = '上级分销商'

    def team_size(self, obj):
        """团队规模"""
        from .services import DistributionRelationService
        stats = DistributionRelationService.get_team_statistics(obj)
        return format_html(
            '<span title="直接下级: {} | 团队总数: {}">{}</span>',
            stats['direct_children'],
            stats['total_team_size'],
            stats['total_team_size']
        )
    team_size.short_description = '团队规模'

    def activate_distributors(self, request, queryset):
        """批量激活分销商"""
        from django.utils import timezone
        updated = queryset.filter(is_active=False).update(
            is_active=True,
            activated_at=timezone.now()
        )
        self.message_user(request, f'成功激活 {updated} 个分销商')
    activate_distributors.short_description = '激活选中的分销商'

    def deactivate_distributors(self, request, queryset):
        """批量停用分销商"""
        updated = queryset.filter(is_active=True).update(is_active=False)
        self.message_user(request, f'成功停用 {updated} 个分销商')
    deactivate_distributors.short_description = '停用选中的分销商'

    def upgrade_level(self, request, queryset):
        """批量检查等级升级"""
        from .utils import check_level_upgrade
        upgraded_count = 0
        for relation in queryset:
            old_level = relation.level
            check_level_upgrade(relation)
            if relation.level != old_level:
                upgraded_count += 1
        self.message_user(request, f'检查完成，{upgraded_count} 个分销商等级发生变化')
    upgrade_level.short_description = '检查等级升级'


@admin.register(CommissionRecord)
class CommissionRecordAdmin(admin.ModelAdmin):
    """佣金记录管理"""
    list_display = [
        'distributor_info', 'order_info', 'commission_type',
        'commission_rate', 'order_amount', 'commission_amount',
        'status', 'settled_at', 'created_at'
    ]
    list_filter = ['commission_type', 'status', 'created_at', 'settled_at']
    search_fields = ['distributor__user__username', 'order__order_no']
    readonly_fields = ['created_at', 'updated_at']
    raw_id_fields = ['distributor', 'order']
    date_hierarchy = 'created_at'
    actions = ['settle_commissions', 'cancel_commissions']

    fieldsets = (
        ('基本信息', {
            'fields': ('distributor', 'order', 'commission_type')
        }),
        ('佣金信息', {
            'fields': ('commission_rate', 'order_amount', 'commission_amount')
        }),
        ('状态', {
            'fields': ('status', 'settled_at', 'remark')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def distributor_info(self, obj):
        """分销商信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:distribution_distributionrelation_change', args=[obj.distributor.pk]),
            obj.distributor.user.username
        )
    distributor_info.short_description = '分销商'

    def order_info(self, obj):
        """订单信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:orders_order_change', args=[obj.order.pk]),
            obj.order.order_no
        )
    order_info.short_description = '订单'

    def settle_commissions(self, request, queryset):
        """批量结算佣金"""
        from django.utils import timezone
        updated = queryset.filter(status='pending').update(
            status='settled',
            settled_at=timezone.now()
        )
        # 更新分销商的可用佣金
        for record in queryset.filter(status='settled'):
            record.distributor.available_commission += record.commission_amount
            record.distributor.save()

        self.message_user(request, f'成功结算 {updated} 条佣金记录')
    settle_commissions.short_description = '结算选中的佣金'

    def cancel_commissions(self, request, queryset):
        """批量取消佣金"""
        updated = queryset.filter(status='pending').update(status='cancelled')
        self.message_user(request, f'成功取消 {updated} 条佣金记录')
    cancel_commissions.short_description = '取消选中的佣金'


@admin.register(WithdrawalRecord)
class WithdrawalRecordAdmin(admin.ModelAdmin):
    """提现记录管理"""
    list_display = [
        'distributor_info', 'withdrawal_no', 'amount', 'fee',
        'actual_amount', 'method', 'status', 'processed_at', 'created_at'
    ]
    list_filter = ['method', 'status', 'created_at', 'processed_at']
    search_fields = ['distributor__user__username', 'withdrawal_no']
    readonly_fields = ['withdrawal_no', 'actual_amount', 'created_at', 'updated_at']
    raw_id_fields = ['distributor']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('基本信息', {
            'fields': ('distributor', 'withdrawal_no', 'method')
        }),
        ('金额信息', {
            'fields': ('amount', 'fee', 'actual_amount')
        }),
        ('账户信息', {
            'fields': ('account_info',)
        }),
        ('状态', {
            'fields': ('status', 'processed_at')
        }),
        ('备注', {
            'fields': ('remark', 'admin_remark')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['approve_withdrawals', 'reject_withdrawals']

    def distributor_info(self, obj):
        """分销商信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:distribution_distributionrelation_change', args=[obj.distributor.pk]),
            obj.distributor.user.username
        )
    distributor_info.short_description = '分销商'

    def approve_withdrawals(self, request, queryset):
        """批量审批提现"""
        from django.utils import timezone
        updated = queryset.filter(status='pending').update(
            status='approved',
            processed_at=timezone.now()
        )
        self.message_user(request, f'成功审批 {updated} 条提现申请')
    approve_withdrawals.short_description = '批量审批选中的提现申请'

    def reject_withdrawals(self, request, queryset):
        """批量拒绝提现"""
        from django.utils import timezone
        for withdrawal in queryset.filter(status='pending'):
            withdrawal.status = 'rejected'
            withdrawal.processed_at = timezone.now()
            withdrawal.admin_remark = '批量拒绝'
            withdrawal.save()

            # 退还冻结的佣金
            withdrawal.distributor.available_commission += withdrawal.amount
            withdrawal.distributor.save()

        self.message_user(request, f'成功拒绝 {queryset.count()} 条提现申请')
    reject_withdrawals.short_description = '批量拒绝选中的提现申请'


@admin.register(PromotionCode)
class PromotionCodeAdmin(admin.ModelAdmin):
    """推广码管理"""
    list_display = [
        'name', 'code', 'distributor_info', 'code_type',
        'usage_limit', 'used_count', 'status', 'is_valid_status', 'created_at'
    ]
    list_filter = ['code_type', 'status', 'created_at']
    search_fields = ['name', 'code', 'distributor__user__username']
    readonly_fields = ['used_count', 'created_at', 'updated_at']
    raw_id_fields = ['distributor']
    actions = ['activate_codes', 'deactivate_codes', 'reset_usage']

    fieldsets = (
        ('基本信息', {
            'fields': ('distributor', 'name', 'code', 'code_type', 'target_id')
        }),
        ('使用限制', {
            'fields': ('usage_limit', 'used_count', 'start_time', 'end_time')
        }),
        ('佣金设置', {
            'fields': ('commission_rate',)
        }),
        ('状态', {
            'fields': ('status', 'description')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def distributor_info(self, obj):
        """分销商信息"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:distribution_distributionrelation_change', args=[obj.distributor.pk]),
            obj.distributor.user.username
        )
    distributor_info.short_description = '分销商'

    def is_valid_status(self, obj):
        """推广码是否有效"""
        if obj.is_valid():
            return format_html('<span style="color: green;">✓ 有效</span>')
        else:
            return format_html('<span style="color: red;">✗ 无效</span>')
    is_valid_status.short_description = '有效性'

    def activate_codes(self, request, queryset):
        """批量激活推广码"""
        updated = queryset.filter(status='inactive').update(status='active')
        self.message_user(request, f'成功激活 {updated} 个推广码')
    activate_codes.short_description = '激活选中的推广码'

    def deactivate_codes(self, request, queryset):
        """批量停用推广码"""
        updated = queryset.filter(status='active').update(status='inactive')
        self.message_user(request, f'成功停用 {updated} 个推广码')
    deactivate_codes.short_description = '停用选中的推广码'

    def reset_usage(self, request, queryset):
        """重置使用次数"""
        updated = queryset.update(used_count=0)
        self.message_user(request, f'成功重置 {updated} 个推广码的使用次数')
    reset_usage.short_description = '重置选中推广码的使用次数'


@admin.register(MarketingActivity)
class MarketingActivityAdmin(admin.ModelAdmin):
    """营销活动管理"""
    list_display = [
        'name', 'activity_type', 'start_time', 'end_time',
        'budget', 'used_budget', 'participant_count',
        'status', 'is_active_status', 'creator'
    ]
    list_filter = ['activity_type', 'status', 'start_time', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['used_budget', 'participant_count', 'created_at', 'updated_at']
    raw_id_fields = ['creator']
    date_hierarchy = 'start_time'
    actions = ['start_activities', 'end_activities', 'pause_activities']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'activity_type', 'description', 'creator')
        }),
        ('活动时间', {
            'fields': ('start_time', 'end_time')
        }),
        ('活动规则', {
            'fields': ('rules', 'target_users')
        }),
        ('预算信息', {
            'fields': ('budget', 'used_budget')
        }),
        ('统计信息', {
            'fields': ('participant_count',)
        }),
        ('状态', {
            'fields': ('status',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def is_active_status(self, obj):
        """活动是否进行中"""
        if obj.is_active():
            return format_html('<span style="color: green;">✓ 进行中</span>')
        else:
            return format_html('<span style="color: gray;">✗ 未进行</span>')
    is_active_status.short_description = '活动状态'

    def start_activities(self, request, queryset):
        """批量开始活动"""
        updated = queryset.filter(status='draft').update(status='active')
        self.message_user(request, f'成功开始 {updated} 个活动')
    start_activities.short_description = '开始选中的活动'

    def end_activities(self, request, queryset):
        """批量结束活动"""
        updated = queryset.filter(status='active').update(status='ended')
        self.message_user(request, f'成功结束 {updated} 个活动')
    end_activities.short_description = '结束选中的活动'

    def pause_activities(self, request, queryset):
        """批量暂停活动"""
        updated = queryset.filter(status='active').update(status='paused')
        self.message_user(request, f'成功暂停 {updated} 个活动')
    pause_activities.short_description = '暂停选中的活动'


# 自定义管理站点，添加分销系统统计页面
class DistributionAdminSite(admin.AdminSite):
    """分销系统管理站点"""
    site_header = '智梦科技 - 分销系统管理'
    site_title = '分销系统管理'
    index_title = '分销系统控制台'

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('distribution-stats/', self.admin_view(self.distribution_stats_view), name='distribution_stats'),
        ]
        return custom_urls + urls

    def distribution_stats_view(self, request):
        """分销系统统计页面"""
        # 获取统计数据
        total_distributors = DistributionRelation.objects.filter(is_active=True).count()
        total_commission = CommissionRecord.objects.filter(status='settled').aggregate(
            total=Sum('commission_amount')
        )['total'] or Decimal('0')

        pending_withdrawals = WithdrawalRecord.objects.filter(status='pending').aggregate(
            count=Count('id'),
            amount=Sum('amount')
        )

        active_promotions = PromotionCode.objects.filter(status='active').count()
        active_activities = MarketingActivity.objects.filter(status='active').count()

        # 等级分布
        level_distribution = DistributionLevel.objects.annotate(
            distributor_count=Count('distributors', filter=Q(distributors__is_active=True))
        ).values('name', 'level', 'distributor_count')

        # 最近7天的佣金趋势
        from datetime import timedelta
        seven_days_ago = timezone.now() - timedelta(days=7)
        recent_commissions = CommissionRecord.objects.filter(
            created_at__gte=seven_days_ago,
            status='settled'
        ).extra(
            select={'day': 'date(created_at)'}
        ).values('day').annotate(
            total=Sum('commission_amount'),
            count=Count('id')
        ).order_by('day')

        context = {
            'title': '分销系统统计',
            'total_distributors': total_distributors,
            'total_commission': total_commission,
            'pending_withdrawals': pending_withdrawals,
            'active_promotions': active_promotions,
            'active_activities': active_activities,
            'level_distribution': level_distribution,
            'recent_commissions': recent_commissions,
        }

        return TemplateResponse(request, 'admin/distribution/stats.html', context)


# 创建自定义管理站点实例
distribution_admin_site = DistributionAdminSite(name='distribution_admin')

# 注册模型到默认admin站点
admin.site.register(DistributionConfig, DistributionConfigAdmin)
admin.site.register(DistributionLevel, DistributionLevelAdmin)
admin.site.register(DistributionRelation, DistributionRelationAdmin)
admin.site.register(CommissionRecord, CommissionRecordAdmin)
admin.site.register(WithdrawalRecord, WithdrawalRecordAdmin)
admin.site.register(PromotionCode, PromotionCodeAdmin)
admin.site.register(MarketingActivity, MarketingActivityAdmin)
