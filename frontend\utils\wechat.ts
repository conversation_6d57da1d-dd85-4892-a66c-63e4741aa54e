/**
 * 微信API封装
 * 封装微信小程序常用API
 */

// 微信登录结果接口
export interface WxLoginResult {
  code: string
  userInfo?: any
}

// 微信用户信息接口
export interface WxUserInfo {
  nickName: string
  avatarUrl: string
  gender: number
  city: string
  province: string
  country: string
  language: string
}

// 微信支付参数接口
export interface WxPaymentParams {
  timeStamp: string
  nonceStr: string
  package: string
  signType: string
  paySign: string
}

// 位置信息接口
export interface LocationInfo {
  latitude: number
  longitude: number
  speed: number
  accuracy: number
  altitude: number
  verticalAccuracy: number
  horizontalAccuracy: number
}

/**
 * 微信登录
 */
export function wxLogin(): Promise<WxLoginResult> {
  return new Promise((resolve, reject) => {
    uni.login({
      provider: 'weixin',
      success: (loginRes) => {
        if (loginRes.code) {
          // 获取用户信息
          uni.getUserProfile({
            desc: '用于完善用户资料',
            success: (userRes) => {
              resolve({
                code: loginRes.code,
                userInfo: userRes.userInfo
              })
            },
            fail: () => {
              // 如果用户拒绝授权，只返回code
              resolve({
                code: loginRes.code
              })
            }
          })
        } else {
          reject(new Error('登录失败：' + loginRes.errMsg))
        }
      },
      fail: (error) => {
        reject(new Error('登录失败：' + error.errMsg))
      }
    })
  })
}

/**
 * 获取用户信息
 */
export function getUserInfo(): Promise<WxUserInfo> {
  return new Promise((resolve, reject) => {
    uni.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        resolve(res.userInfo)
      },
      fail: (error) => {
        reject(new Error('获取用户信息失败：' + error.errMsg))
      }
    })
  })
}

/**
 * 微信支付
 */
export function wxPay(params: WxPaymentParams): Promise<boolean> {
  return new Promise((resolve, reject) => {
    uni.requestPayment({
      provider: 'wxpay',
      ...params,
      success: () => {
        resolve(true)
      },
      fail: (error) => {
        if (error.errMsg === 'requestPayment:fail cancel') {
          reject(new Error('用户取消支付'))
        } else {
          reject(new Error('支付失败：' + error.errMsg))
        }
      }
    })
  })
}

/**
 * 获取当前位置
 */
export function getCurrentLocation(): Promise<LocationInfo> {
  return new Promise((resolve, reject) => {
    uni.getLocation({
      type: 'gcj02',
      success: (res) => {
        resolve({
          latitude: res.latitude,
          longitude: res.longitude,
          speed: res.speed || 0,
          accuracy: res.accuracy || 0,
          altitude: res.altitude || 0,
          verticalAccuracy: res.verticalAccuracy || 0,
          horizontalAccuracy: res.horizontalAccuracy || 0
        })
      },
      fail: (error) => {
        reject(new Error('获取位置失败：' + error.errMsg))
      }
    })
  })
}

/**
 * 选择位置
 */
export function chooseLocation(): Promise<{
  name: string
  address: string
  latitude: number
  longitude: number
}> {
  return new Promise((resolve, reject) => {
    uni.chooseLocation({
      success: (res) => {
        resolve({
          name: res.name,
          address: res.address,
          latitude: res.latitude,
          longitude: res.longitude
        })
      },
      fail: (error) => {
        reject(new Error('选择位置失败：' + error.errMsg))
      }
    })
  })
}

/**
 * 打开地图
 */
export function openLocation(params: {
  latitude: number
  longitude: number
  name?: string
  address?: string
  scale?: number
}): Promise<void> {
  return new Promise((resolve, reject) => {
    uni.openLocation({
      latitude: params.latitude,
      longitude: params.longitude,
      name: params.name || '',
      address: params.address || '',
      scale: params.scale || 18,
      success: () => {
        resolve()
      },
      fail: (error) => {
        reject(new Error('打开地图失败：' + error.errMsg))
      }
    })
  })
}

/**
 * 选择图片
 */
export function chooseImage(options: {
  count?: number
  sizeType?: ('original' | 'compressed')[]
  sourceType?: ('album' | 'camera')[]
} = {}): Promise<string[]> {
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count: options.count || 9,
      sizeType: options.sizeType || ['original', 'compressed'],
      sourceType: options.sourceType || ['album', 'camera'],
      success: (res) => {
        resolve(res.tempFilePaths)
      },
      fail: (error) => {
        reject(new Error('选择图片失败：' + error.errMsg))
      }
    })
  })
}

/**
 * 预览图片
 */
export function previewImage(params: {
  current?: string
  urls: string[]
}): Promise<void> {
  return new Promise((resolve, reject) => {
    uni.previewImage({
      current: params.current || params.urls[0],
      urls: params.urls,
      success: () => {
        resolve()
      },
      fail: (error) => {
        reject(new Error('预览图片失败：' + error.errMsg))
      }
    })
  })
}

/**
 * 保存图片到相册
 */
export function saveImageToPhotosAlbum(filePath: string): Promise<void> {
  return new Promise((resolve, reject) => {
    uni.saveImageToPhotosAlbum({
      filePath,
      success: () => {
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
        resolve()
      },
      fail: (error) => {
        reject(new Error('保存图片失败：' + error.errMsg))
      }
    })
  })
}

/**
 * 扫码
 */
export function scanCode(): Promise<string> {
  return new Promise((resolve, reject) => {
    uni.scanCode({
      success: (res) => {
        resolve(res.result)
      },
      fail: (error) => {
        reject(new Error('扫码失败：' + error.errMsg))
      }
    })
  })
}

/**
 * 分享到微信
 */
export function shareToWeChat(params: {
  title: string
  desc?: string
  path?: string
  imageUrl?: string
}): Promise<void> {
  return new Promise((resolve, reject) => {
    uni.share({
      provider: 'weixin',
      scene: 'WXSceneSession',
      type: 5,
      title: params.title,
      summary: params.desc || '',
      href: params.path || '',
      imageUrl: params.imageUrl || '',
      success: () => {
        resolve()
      },
      fail: (error) => {
        reject(new Error('分享失败：' + error.errMsg))
      }
    })
  })
}

/**
 * 复制到剪贴板
 */
export function setClipboardData(data: string): Promise<void> {
  return new Promise((resolve, reject) => {
    uni.setClipboardData({
      data,
      success: () => {
        uni.showToast({
          title: '复制成功',
          icon: 'success'
        })
        resolve()
      },
      fail: (error) => {
        reject(new Error('复制失败：' + error.errMsg))
      }
    })
  })
}

/**
 * 获取剪贴板内容
 */
export function getClipboardData(): Promise<string> {
  return new Promise((resolve, reject) => {
    uni.getClipboardData({
      success: (res) => {
        resolve(res.data)
      },
      fail: (error) => {
        reject(new Error('获取剪贴板内容失败：' + error.errMsg))
      }
    })
  })
}

/**
 * 震动反馈
 */
export function vibrateShort(): void {
  uni.vibrateShort({
    type: 'medium'
  })
}

/**
 * 长震动反馈
 */
export function vibrateLong(): void {
  uni.vibrateLong()
}

/**
 * 显示Toast
 */
export function showToast(params: {
  title: string
  icon?: 'success' | 'error' | 'loading' | 'none'
  duration?: number
  mask?: boolean
}): void {
  uni.showToast({
    title: params.title,
    icon: params.icon || 'none',
    duration: params.duration || 2000,
    mask: params.mask || false
  })
}

/**
 * 显示Loading
 */
export function showLoading(title: string = '加载中...'): void {
  uni.showLoading({
    title,
    mask: true
  })
}

/**
 * 隐藏Loading
 */
export function hideLoading(): void {
  uni.hideLoading()
}

/**
 * 显示模态对话框
 */
export function showModal(params: {
  title?: string
  content: string
  showCancel?: boolean
  cancelText?: string
  confirmText?: string
}): Promise<boolean> {
  return new Promise((resolve) => {
    uni.showModal({
      title: params.title || '提示',
      content: params.content,
      showCancel: params.showCancel !== false,
      cancelText: params.cancelText || '取消',
      confirmText: params.confirmText || '确定',
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

/**
 * 显示操作菜单
 */
export function showActionSheet(itemList: string[]): Promise<number> {
  return new Promise((resolve, reject) => {
    uni.showActionSheet({
      itemList,
      success: (res) => {
        resolve(res.tapIndex)
      },
      fail: (error) => {
        reject(new Error('显示操作菜单失败：' + error.errMsg))
      }
    })
  })
}

export default {
  wxLogin,
  getUserInfo,
  wxPay,
  getCurrentLocation,
  chooseLocation,
  openLocation,
  chooseImage,
  previewImage,
  saveImageToPhotosAlbum,
  scanCode,
  shareToWeChat,
  setClipboardData,
  getClipboardData,
  vibrateShort,
  vibrateLong,
  showToast,
  showLoading,
  hideLoading,
  showModal,
  showActionSheet
}
