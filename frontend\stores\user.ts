/**
 * 用户状态管理
 * 管理用户登录状态、用户信息等
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo } from '@/utils/auth'
import { login as authLogin, logout as authLogout, wxLogin, isLoggedIn } from '@/utils/auth'
import http from '@/utils/request'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string>('')
  const isLoading = ref(false)

  // 计算属性
  const isLogin = computed(() => {
    return !!token.value && !!userInfo.value
  })

  const isDistributor = computed(() => {
    return userInfo.value?.is_distributor || false
  })

  const distributorLevel = computed(() => {
    return userInfo.value?.distributor_level || 0
  })

  const avatar = computed(() => {
    return userInfo.value?.avatar || '/static/images/default-avatar.png'
  })

  const nickname = computed(() => {
    return userInfo.value?.nickname || userInfo.value?.username || '未设置昵称'
  })

  // 方法
  /**
   * 微信登录
   */
  const wxLoginAction = async (): Promise<boolean> => {
    try {
      isLoading.value = true
      
      // 获取微信登录信息
      const wxData = await wxLogin()
      
      // 发送到后端进行登录
      const response = await http.post('/auth/wechat-login', {
        code: wxData.code,
        user_info: wxData.userInfo
      })

      if (response.success) {
        const { token: newToken, user } = response.data
        
        // 保存登录信息
        token.value = newToken
        userInfo.value = user
        authLogin(newToken, user)
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('微信登录失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 手机号登录
   */
  const phoneLogin = async (phone: string, code: string): Promise<boolean> => {
    try {
      isLoading.value = true
      
      const response = await http.post('/auth/phone-login', {
        phone,
        code
      })

      if (response.success) {
        const { token: newToken, user } = response.data
        
        token.value = newToken
        userInfo.value = user
        authLogin(newToken, user)
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('手机号登录失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 发送验证码
   */
  const sendSmsCode = async (phone: string): Promise<boolean> => {
    try {
      const response = await http.post('/auth/send-sms-code', { phone })
      return response.success
    } catch (error) {
      console.error('发送验证码失败:', error)
      return false
    }
  }

  /**
   * 获取用户信息
   */
  const getUserInfo = async (): Promise<boolean> => {
    try {
      const response = await http.get('/auth/user-info')
      
      if (response.success) {
        userInfo.value = response.data
        return true
      }
      
      return false
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return false
    }
  }

  /**
   * 更新用户信息
   */
  const updateUserInfo = async (data: Partial<UserInfo>): Promise<boolean> => {
    try {
      const response = await http.put('/auth/user-info', data)
      
      if (response.success) {
        userInfo.value = { ...userInfo.value!, ...response.data }
        return true
      }
      
      return false
    } catch (error) {
      console.error('更新用户信息失败:', error)
      return false
    }
  }

  /**
   * 上传头像
   */
  const uploadAvatar = async (filePath: string): Promise<boolean> => {
    try {
      return new Promise((resolve) => {
        uni.uploadFile({
          url: http.getApiUrl('/auth/upload-avatar'),
          filePath,
          name: 'avatar',
          header: {
            'Authorization': `Bearer ${token.value}`
          },
          success: (res) => {
            const data = JSON.parse(res.data)
            if (data.success) {
              userInfo.value!.avatar = data.data.avatar
              resolve(true)
            } else {
              resolve(false)
            }
          },
          fail: () => {
            resolve(false)
          }
        })
      })
    } catch (error) {
      console.error('上传头像失败:', error)
      return false
    }
  }

  /**
   * 绑定手机号
   */
  const bindPhone = async (phone: string, code: string): Promise<boolean> => {
    try {
      const response = await http.post('/auth/bind-phone', {
        phone,
        code
      })

      if (response.success) {
        userInfo.value!.phone = phone
        return true
      }
      
      return false
    } catch (error) {
      console.error('绑定手机号失败:', error)
      return false
    }
  }

  /**
   * 申请成为分销商
   */
  const applyDistributor = async (data: {
    real_name: string
    id_card: string
    bank_account?: string
  }): Promise<boolean> => {
    try {
      const response = await http.post('/distribution/apply', data)
      return response.success
    } catch (error) {
      console.error('申请分销商失败:', error)
      return false
    }
  }

  /**
   * 登出
   */
  const logout = async (): Promise<void> => {
    try {
      // 调用后端登出接口
      await http.post('/auth/logout')
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地状态
      token.value = ''
      userInfo.value = null
      authLogout()
    }
  }

  /**
   * 初始化用户状态
   */
  const initUserState = (): void => {
    if (isLoggedIn()) {
      // 如果本地有登录状态，获取最新用户信息
      getUserInfo()
    }
  }

  /**
   * 检查登录状态
   */
  const checkLoginStatus = (): boolean => {
    return isLoggedIn() && isLogin.value
  }

  return {
    // 状态
    userInfo,
    token,
    isLoading,
    
    // 计算属性
    isLogin,
    isDistributor,
    distributorLevel,
    avatar,
    nickname,
    
    // 方法
    wxLoginAction,
    phoneLogin,
    sendSmsCode,
    getUserInfo,
    updateUserInfo,
    uploadAvatar,
    bindPhone,
    applyDistributor,
    logout,
    initUserState,
    checkLoginStatus
  }
}, {
  // 持久化配置
  persist: {
    key: 'user-store',
    storage: {
      getItem: (key: string) => uni.getStorageSync(key),
      setItem: (key: string, value: string) => uni.setStorageSync(key, value),
      removeItem: (key: string) => uni.removeStorageSync(key)
    },
    // 只持久化必要的状态
    paths: ['userInfo', 'token']
  }
})
