# 智梦科技系统后端

## 项目结构

```
backend/
├── apps/                 # Django应用目录
│   ├── core/            # 核心应用
│   ├── users/           # 用户管理应用
│   ├── products/        # 产品管理应用
│   ├── orders/          # 订单管理应用
├── zmkj/                # Django项目配置
├── scripts/             # 脚本目录
├── tests/               # 全局测试目录
├── requirements.txt     # Python依赖
├── manage.py           # Django管理脚本
└── .env                # 环境变量配置
```

## 开发环境配置

1. 创建虚拟环境：
   ```bash
   python -m venv venv
   ```

2. 激活虚拟环境：
   ```bash
   # Windows
   venv\Scripts\activate
   
   # macOS/Linux
   source venv/bin/activate
   ```

3. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

4. 数据库迁移：
   ```bash
   python manage.py migrate
   ```

5. 创建超级用户：
   ```bash
   python manage.py createsuperuser
   ```

6. 运行开发服务器：
   ```bash
   python manage.py runserver
   ```

## 测试运行

### 运行所有测试
```bash
python manage.py test
```

### 运行特定应用测试
```bash
python manage.py test apps.users
python manage.py test apps.products
python manage.py test apps.orders
```

### 使用测试脚本运行
```bash
python scripts/run_tests.py
python scripts/run_all_tests.py
```

## API文档

启动开发服务器后，访问以下地址查看API文档：

- Swagger UI: http://localhost:8000/api/docs/
- ReDoc: http://localhost:8000/api/redoc/

## 管理后台

访问 http://localhost:8000/admin/ 进入Django管理后台

## 环境变量配置

复制 `.env.example` 文件为 `.env` 并配置相应参数：

```bash
cp .env.example .env
```

主要配置项：
- `SECRET_KEY`: Django密钥
- `DEBUG`: 调试模式开关
- `DATABASE_*`: 数据库配置
- `WECHAT_*`: 微信相关配置
