<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-header">
			<view class="search-bar" @click="goToSearch">
				<Icon name="search" size="16" color="#999" />
				<text class="search-placeholder">搜索商品</text>
			</view>
		</view>

		<!-- 筛选栏 -->
		<view class="filter-bar">
			<view class="filter-item" :class="{ active: currentFilter === 'all' }" @click="handleFilter('all')">
				<text class="filter-text">全部</text>
			</view>
			<view class="filter-item" :class="{ active: currentFilter === 'hot' }" @click="handleFilter('hot')">
				<text class="filter-text">热销</text>
			</view>
			<view class="filter-item" :class="{ active: currentFilter === 'new' }" @click="handleFilter('new')">
				<text class="filter-text">新品</text>
			</view>
			<view class="filter-item" :class="{ active: currentFilter === 'price' }" @click="handleFilter('price')">
				<text class="filter-text">价格</text>
				<Icon name="arrow-down" size="12" color="#999" />
			</view>
		</view>

		<!-- 商品列表 -->
		<view class="product-list">
			<view class="product-item" v-for="(product, index) in products" :key="index" @click="handleProductClick(product)">
				<image class="product-image" :src="product.image" mode="aspectFill" />
				<view class="product-info">
					<text class="product-name">{{ product.name }}</text>
					<view class="product-price">
						<text class="price-current">￥{{ product.price }}</text>
						<text class="price-original" v-if="product.originalPrice">￥{{ product.originalPrice }}</text>
					</view>
					<view class="product-tags">
						<text class="tag hot" v-if="product.isHot">热销</text>
						<text class="tag new" v-if="product.isNew">新品</text>
					</view>
					<view class="product-sales">
						<text class="sales-text">已售{{ product.sales }}件</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="load-more" v-if="hasMore">
			<text class="load-text" v-if="!loading">上拉加载更多</text>
			<text class="load-text" v-else>加载中...</text>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="products.length === 0 && !loading">
			<Icon name="empty" size="80" color="#ccc" />
			<text class="empty-text">暂无商品</text>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Icon from '@/components/common/Icon.uvue'

// 响应式数据
const loading = ref(false)
const hasMore = ref(true)
const currentFilter = ref('all')

// 模拟商品数据
const products = ref([
	{
		id: 1,
		name: '夏季清爽T恤 纯棉透气 多色可选',
		price: 89.00,
		originalPrice: 129.00,
		image: 'https://imggw.zmkj.live/products/tshirt1.jpg',
		isHot: true,
		isNew: false,
		sales: 1234
	},
	{
		id: 2,
		name: '无线蓝牙耳机 降噪立体声 长续航',
		price: 299.00,
		originalPrice: null,
		image: 'https://imggw.zmkj.live/products/earphone1.jpg',
		isHot: false,
		isNew: true,
		sales: 567
	},
	{
		id: 3,
		name: '简约家居摆件 北欧风格 装饰品',
		price: 59.00,
		originalPrice: 89.00,
		image: 'https://imggw.zmkj.live/products/decoration1.jpg',
		isHot: true,
		isNew: false,
		sales: 890
	},
	{
		id: 4,
		name: '天然护肤套装 补水保湿 温和无刺激',
		price: 199.00,
		originalPrice: 299.00,
		image: 'https://imggw.zmkj.live/products/skincare1.jpg',
		isHot: false,
		isNew: true,
		sales: 345
	},
	{
		id: 5,
		name: '运动休闲鞋 透气舒适 防滑耐磨',
		price: 159.00,
		originalPrice: 199.00,
		image: 'https://imggw.zmkj.live/products/shoes1.jpg',
		isHot: true,
		isNew: false,
		sales: 678
	},
	{
		id: 6,
		name: '智能手环 健康监测 运动记录',
		price: 399.00,
		originalPrice: null,
		image: 'https://imggw.zmkj.live/products/watch1.jpg',
		isHot: false,
		isNew: true,
		sales: 234
	}
])

// 生命周期
onMounted(() => {
	initPage()
})

// 方法
const initPage = async () => {
	console.log('初始化商品列表页')
	// 这里可以添加数据初始化逻辑
}

const goToSearch = () => {
	uni.navigateTo({
		url: '/pages/products/search'
	})
}

const handleFilter = (filter: string) => {
	currentFilter.value = filter
	
	// 这里可以根据筛选条件重新获取数据
	switch (filter) {
		case 'all':
			// 获取全部商品
			break
		case 'hot':
			// 获取热销商品
			break
		case 'new':
			// 获取新品
			break
		case 'price':
			// 按价格排序
			break
	}
}

const handleProductClick = (product: any) => {
	uni.navigateTo({
		url: `/pages/products/detail?id=${product.id}`
	})
}

// 下拉刷新
const onPullDownRefresh = async () => {
	await initPage()
	uni.stopPullDownRefresh()
}

// 上拉加载更多
const onReachBottom = async () => {
	if (!hasMore.value || loading.value) return
	
	loading.value = true
	// 模拟加载更多数据
	setTimeout(() => {
		loading.value = false
		// 这里可以添加更多商品数据
	}, 1000)
}
</script>

<style lang="scss" scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

// 搜索栏
.search-header {
	padding: 10px 15px;
	background-color: #fff;
	border-bottom: 1px solid #eee;
	
	.search-bar {
		display: flex;
		align-items: center;
		height: 36px;
		padding: 0 12px;
		background-color: #f5f5f5;
		border-radius: 18px;
		
		.search-placeholder {
			margin-left: 8px;
			font-size: 14px;
			color: #999;
		}
	}
}

// 筛选栏
.filter-bar {
	display: flex;
	background-color: #fff;
	border-bottom: 1px solid #eee;
	
	.filter-item {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 44px;
		position: relative;
		
		&.active {
			.filter-text {
				color: #007aff;
			}
			
			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 20px;
				height: 2px;
				background-color: #007aff;
			}
		}
		
		.filter-text {
			font-size: 14px;
			color: #333;
			margin-right: 4px;
		}
	}
}

// 商品列表
.product-list {
	padding: 10px 15px;
	
	.product-item {
		display: flex;
		background-color: #fff;
		border-radius: 8px;
		padding: 12px;
		margin-bottom: 10px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		
		.product-image {
			width: 100px;
			height: 100px;
			border-radius: 6px;
			margin-right: 12px;
		}
		
		.product-info {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			
			.product-name {
				font-size: 14px;
				color: #333;
				line-height: 1.4;
				margin-bottom: 8px;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
			}
			
			.product-price {
				display: flex;
				align-items: center;
				margin-bottom: 8px;
				
				.price-current {
					font-size: 16px;
					font-weight: 600;
					color: #ff3b30;
					margin-right: 8px;
				}
				
				.price-original {
					font-size: 12px;
					color: #999;
					text-decoration: line-through;
				}
			}
			
			.product-tags {
				display: flex;
				margin-bottom: 8px;
				
				.tag {
					font-size: 10px;
					padding: 2px 6px;
					border-radius: 2px;
					margin-right: 4px;
					
					&.hot {
						background-color: #ff3b30;
						color: #fff;
					}
					
					&.new {
						background-color: #34c759;
						color: #fff;
					}
				}
			}
			
			.product-sales {
				.sales-text {
					font-size: 12px;
					color: #999;
				}
			}
		}
	}
}

// 加载更多
.load-more {
	padding: 20px;
	text-align: center;
	
	.load-text {
		font-size: 14px;
		color: #999;
	}
}

// 空状态
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 20px;
	
	.empty-text {
		font-size: 14px;
		color: #999;
		margin-top: 16px;
	}
}
</style>
