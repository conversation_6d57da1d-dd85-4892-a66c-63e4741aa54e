"""
微信模块API文档配置
"""
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status


# 微信用户相关API文档
wechat_user_list_docs = swagger_auto_schema(
    operation_description="获取微信用户列表",
    operation_summary="微信用户列表",
    tags=['微信用户'],
    manual_parameters=[
        openapi.Parameter(
            'subscribe',
            openapi.IN_QUERY,
            description="关注状态筛选",
            type=openapi.TYPE_BOOLEAN,
            required=False
        ),
        openapi.Parameter(
            'search',
            openapi.IN_QUERY,
            description="搜索关键词（昵称、openid）",
            type=openapi.TYPE_STRING,
            required=False
        ),
    ],
    responses={
        200: openapi.Response(
            description="成功",
            examples={
                "application/json": {
                    "count": 100,
                    "next": "http://example.com/api/wechat/users/?page=2",
                    "previous": None,
                    "results": [
                        {
                            "id": 1,
                            "openid": "test_openid",
                            "nickname": "测试用户",
                            "gender": "male",
                            "city": "北京",
                            "province": "北京",
                            "country": "中国",
                            "avatar_url": "http://example.com/avatar.jpg",
                            "subscribe": True,
                            "subscribe_time": "2024-01-01T00:00:00Z",
                            "created_at": "2024-01-01T00:00:00Z"
                        }
                    ]
                }
            }
        )
    }
)

wechat_user_detail_docs = swagger_auto_schema(
    operation_description="获取微信用户详情",
    operation_summary="微信用户详情",
    tags=['微信用户'],
    responses={
        200: openapi.Response(
            description="成功",
            examples={
                "application/json": {
                    "id": 1,
                    "openid": "test_openid",
                    "nickname": "测试用户",
                    "gender": "male",
                    "city": "北京",
                    "province": "北京",
                    "country": "中国",
                    "avatar_url": "http://example.com/avatar.jpg",
                    "subscribe": True,
                    "subscribe_time": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z"
                }
            }
        ),
        404: openapi.Response(description="用户不存在")
    }
)

# 微信配置相关API文档
wechat_config_list_docs = swagger_auto_schema(
    operation_description="获取微信配置列表",
    operation_summary="微信配置列表",
    tags=['微信配置'],
    responses={
        200: openapi.Response(
            description="成功",
            examples={
                "application/json": {
                    "count": 5,
                    "results": [
                        {
                            "id": 1,
                            "platform": "official",
                            "name": "default",
                            "app_id": "wx1234567890",
                            "app_secret": "***已配置***",
                            "token": "***已配置***",
                            "description": "默认公众号配置",
                            "is_active": True,
                            "created_at": "2024-01-01T00:00:00Z"
                        }
                    ]
                }
            }
        )
    }
)

wechat_config_platforms_docs = swagger_auto_schema(
    operation_description="获取所有平台配置",
    operation_summary="平台配置列表",
    tags=['微信配置'],
    responses={
        200: openapi.Response(
            description="成功",
            examples={
                "application/json": {
                    "success": True,
                    "data": {
                        "official": {
                            "default": {
                                "app_id": "wx1234567890",
                                "token": "test_token"
                            }
                        },
                        "miniprogram": {
                            "default": {
                                "app_id": "wx0987654321"
                            }
                        }
                    }
                }
            }
        )
    }
)

# 微信支付相关API文档
wechat_payment_create_order_docs = swagger_auto_schema(
    operation_description="创建微信支付订单",
    operation_summary="创建支付订单",
    tags=['微信支付'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['out_trade_no', 'total_fee', 'body'],
        properties={
            'out_trade_no': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='商户订单号',
                max_length=32
            ),
            'total_fee': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='订单金额(分)',
                minimum=1
            ),
            'body': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='商品描述',
                max_length=128
            ),
            'notify_url': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='通知地址',
                format='uri'
            ),
            'trade_type': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='交易类型',
                enum=['JSAPI', 'NATIVE', 'APP'],
                default='JSAPI'
            ),
            'openid': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='用户openid（JSAPI支付必填）',
                max_length=128
            )
        }
    ),
    responses={
        200: openapi.Response(
            description="成功",
            examples={
                "application/json": {
                    "success": True,
                    "data": {
                        "return_code": "SUCCESS",
                        "return_msg": "OK",
                        "appid": "wx1234567890",
                        "mch_id": "1234567890",
                        "prepay_id": "wx123456789012345678901234567890",
                        "trade_type": "JSAPI"
                    }
                }
            }
        ),
        400: openapi.Response(
            description="参数错误",
            examples={
                "application/json": {
                    "success": False,
                    "error": "缺少必要参数: total_fee"
                }
            }
        )
    }
)

wechat_payment_query_order_docs = swagger_auto_schema(
    operation_description="查询微信支付订单状态",
    operation_summary="查询订单状态",
    tags=['微信支付'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'out_trade_no': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='商户订单号',
                max_length=32
            ),
            'transaction_id': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='微信交易号',
                max_length=32
            )
        }
    ),
    responses={
        200: openapi.Response(
            description="成功",
            examples={
                "application/json": {
                    "success": True,
                    "data": {
                        "return_code": "SUCCESS",
                        "trade_state": "SUCCESS",
                        "out_trade_no": "test_order_123",
                        "transaction_id": "wx123456789012345678901234567890",
                        "total_fee": "100"
                    }
                }
            }
        )
    }
)

# 微信统计相关API文档
wechat_stats_user_docs = swagger_auto_schema(
    operation_description="获取微信用户统计数据",
    operation_summary="用户统计",
    tags=['微信统计'],
    responses={
        200: openapi.Response(
            description="成功",
            examples={
                "application/json": {
                    "success": True,
                    "data": {
                        "total_users": 1000,
                        "subscribed_users": 800,
                        "today_new_users": 50,
                        "subscribe_rate": 80.0
                    }
                }
            }
        )
    }
)

wechat_stats_message_docs = swagger_auto_schema(
    operation_description="获取微信消息统计数据",
    operation_summary="消息统计",
    tags=['微信统计'],
    responses={
        200: openapi.Response(
            description="成功",
            examples={
                "application/json": {
                    "success": True,
                    "data": {
                        "total_messages": 5000,
                        "today_messages": 200,
                        "week_messages": 1500
                    }
                }
            }
        )
    }
)

# 微信公众号相关API文档
wechat_official_verify_docs = swagger_auto_schema(
    operation_description="微信公众号URL验证",
    operation_summary="公众号验证",
    tags=['微信公众号'],
    manual_parameters=[
        openapi.Parameter(
            'signature',
            openapi.IN_QUERY,
            description="微信加密签名",
            type=openapi.TYPE_STRING,
            required=True
        ),
        openapi.Parameter(
            'timestamp',
            openapi.IN_QUERY,
            description="时间戳",
            type=openapi.TYPE_STRING,
            required=True
        ),
        openapi.Parameter(
            'nonce',
            openapi.IN_QUERY,
            description="随机数",
            type=openapi.TYPE_STRING,
            required=True
        ),
        openapi.Parameter(
            'echostr',
            openapi.IN_QUERY,
            description="随机字符串",
            type=openapi.TYPE_STRING,
            required=True
        ),
    ],
    responses={
        200: openapi.Response(
            description="验证成功，返回echostr",
            examples={
                "text/plain": "test_echo_string"
            }
        ),
        403: openapi.Response(description="验证失败")
    }
)

# 微信小程序相关API文档
wechat_miniprogram_auth_docs = swagger_auto_schema(
    operation_description="微信小程序用户授权登录",
    operation_summary="小程序授权",
    tags=['微信小程序'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['code'],
        properties={
            'code': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='小程序登录凭证',
                max_length=32
            )
        }
    ),
    responses={
        200: openapi.Response(
            description="成功",
            examples={
                "application/json": {
                    "success": True,
                    "data": {
                        "openid": "test_openid",
                        "session_key": "test_session_key",
                        "unionid": "test_unionid"
                    }
                }
            }
        ),
        400: openapi.Response(
            description="授权失败",
            examples={
                "application/json": {
                    "success": False,
                    "error": "invalid code"
                }
            }
        )
    }
)
