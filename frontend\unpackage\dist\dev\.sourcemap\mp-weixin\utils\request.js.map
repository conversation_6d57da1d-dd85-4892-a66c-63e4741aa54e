{"version": 3, "file": "request.js", "sources": ["utils/request.ts"], "sourcesContent": ["/**\n * 网络请求封装\n * 统一处理请求和响应，支持拦截器、错误处理等\n */\n\nimport { getApiUrl, isDev } from './config'\nimport { getToken, removeToken } from './auth'\n\n// 请求方法类型\nexport type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'\n\n// 请求配置接口\nexport interface RequestConfig {\n  url: string\n  method?: RequestMethod\n  data?: any\n  params?: Record<string, any>\n  header?: Record<string, string>\n  timeout?: number\n  showLoading?: boolean\n  loadingText?: string\n  showError?: boolean\n  skipAuth?: boolean\n}\n\n// 响应数据接口\nexport interface ResponseData<T = any> {\n  code: number\n  message: string\n  data: T\n  success: boolean\n}\n\n// 请求拦截器类型\nexport type RequestInterceptor = (config: RequestConfig) => RequestConfig | Promise<RequestConfig>\n\n// 响应拦截器类型\nexport type ResponseInterceptor = (response: any) => any | Promise<any>\n\n// 错误拦截器类型\nexport type ErrorInterceptor = (error: any) => any | Promise<any>\n\nclass HttpRequest {\n  private requestInterceptors: RequestInterceptor[] = []\n  private responseInterceptors: ResponseInterceptor[] = []\n  private errorInterceptors: ErrorInterceptor[] = []\n  \n  // 默认配置\n  private defaultConfig: Partial<RequestConfig> = {\n    method: 'GET',\n    timeout: 10000,\n    showLoading: false,\n    loadingText: '加载中...',\n    showError: true,\n    skipAuth: false,\n    header: {\n      'Content-Type': 'application/json'\n    }\n  }\n\n  /**\n   * 添加请求拦截器\n   */\n  addRequestInterceptor(interceptor: RequestInterceptor) {\n    this.requestInterceptors.push(interceptor)\n  }\n\n  /**\n   * 添加响应拦截器\n   */\n  addResponseInterceptor(interceptor: ResponseInterceptor) {\n    this.responseInterceptors.push(interceptor)\n  }\n\n  /**\n   * 添加错误拦截器\n   */\n  addErrorInterceptor(interceptor: ErrorInterceptor) {\n    this.errorInterceptors.push(interceptor)\n  }\n\n  /**\n   * 执行请求拦截器\n   */\n  private async runRequestInterceptors(config: RequestConfig): Promise<RequestConfig> {\n    let result = config\n    for (const interceptor of this.requestInterceptors) {\n      result = await interceptor(result)\n    }\n    return result\n  }\n\n  /**\n   * 执行响应拦截器\n   */\n  private async runResponseInterceptors(response: any): Promise<any> {\n    let result = response\n    for (const interceptor of this.responseInterceptors) {\n      result = await interceptor(result)\n    }\n    return result\n  }\n\n  /**\n   * 执行错误拦截器\n   */\n  private async runErrorInterceptors(error: any): Promise<any> {\n    let result = error\n    for (const interceptor of this.errorInterceptors) {\n      result = await interceptor(result)\n    }\n    return result\n  }\n\n  /**\n   * 处理URL参数\n   */\n  private buildUrl(url: string, params?: Record<string, any>): string {\n    if (!params || Object.keys(params).length === 0) {\n      return url\n    }\n\n    const queryString = Object.keys(params)\n      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)\n      .join('&')\n\n    return url + (url.includes('?') ? '&' : '?') + queryString\n  }\n\n  /**\n   * 显示加载提示\n   */\n  private showLoading(text: string) {\n    uni.showLoading({\n      title: text,\n      mask: true\n    })\n  }\n\n  /**\n   * 隐藏加载提示\n   */\n  private hideLoading() {\n    uni.hideLoading()\n  }\n\n  /**\n   * 显示错误提示\n   */\n  private showError(message: string) {\n    uni.showToast({\n      title: message,\n      icon: 'none',\n      duration: 2000\n    })\n  }\n\n  /**\n   * 发送请求\n   */\n  async request<T = any>(config: RequestConfig): Promise<ResponseData<T>> {\n    // 合并默认配置\n    const finalConfig = { ...this.defaultConfig, ...config }\n\n    try {\n      // 执行请求拦截器\n      const interceptedConfig = await this.runRequestInterceptors(finalConfig)\n\n      // 构建完整URL\n      const fullUrl = getApiUrl(this.buildUrl(interceptedConfig.url, interceptedConfig.params))\n\n      // 构建请求头\n      const headers = { ...interceptedConfig.header }\n\n      // 添加认证头\n      if (!interceptedConfig.skipAuth) {\n        const token = getToken()\n        if (token) {\n          headers['Authorization'] = `Bearer ${token}`\n        }\n      }\n\n      // 显示加载提示\n      if (interceptedConfig.showLoading) {\n        this.showLoading(interceptedConfig.loadingText!)\n      }\n\n      // 发送请求\n      const response = await new Promise<any>((resolve, reject) => {\n        uni.request({\n          url: fullUrl,\n          method: interceptedConfig.method as any,\n          data: interceptedConfig.data,\n          header: headers,\n          timeout: interceptedConfig.timeout,\n          success: resolve,\n          fail: reject\n        })\n      })\n\n      // 隐藏加载提示\n      if (interceptedConfig.showLoading) {\n        this.hideLoading()\n      }\n\n      // 执行响应拦截器\n      const interceptedResponse = await this.runResponseInterceptors(response)\n\n      // 处理响应数据\n      const responseData = interceptedResponse.data as ResponseData<T>\n\n      // 检查业务状态码\n      if (responseData.code !== 0) {\n        throw new Error(responseData.message || '请求失败')\n      }\n\n      return responseData\n\n    } catch (error: any) {\n      // 隐藏加载提示\n      if (finalConfig.showLoading) {\n        this.hideLoading()\n      }\n\n      // 执行错误拦截器\n      const interceptedError = await this.runErrorInterceptors(error)\n\n      // 显示错误提示\n      if (finalConfig.showError) {\n        const errorMessage = interceptedError.message || '网络请求失败'\n        this.showError(errorMessage)\n      }\n\n      // 开发环境下打印错误信息\n      if (isDev()) {\n        console.error('Request Error:', interceptedError)\n      }\n\n      throw interceptedError\n    }\n  }\n\n  /**\n   * GET请求\n   */\n  get<T = any>(url: string, params?: Record<string, any>, config?: Partial<RequestConfig>): Promise<ResponseData<T>> {\n    return this.request<T>({\n      url,\n      method: 'GET',\n      params,\n      ...config\n    })\n  }\n\n  /**\n   * POST请求\n   */\n  post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ResponseData<T>> {\n    return this.request<T>({\n      url,\n      method: 'POST',\n      data,\n      ...config\n    })\n  }\n\n  /**\n   * PUT请求\n   */\n  put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ResponseData<T>> {\n    return this.request<T>({\n      url,\n      method: 'PUT',\n      data,\n      ...config\n    })\n  }\n\n  /**\n   * DELETE请求\n   */\n  delete<T = any>(url: string, params?: Record<string, any>, config?: Partial<RequestConfig>): Promise<ResponseData<T>> {\n    return this.request<T>({\n      url,\n      method: 'DELETE',\n      params,\n      ...config\n    })\n  }\n\n  /**\n   * PATCH请求\n   */\n  patch<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ResponseData<T>> {\n    return this.request<T>({\n      url,\n      method: 'PATCH',\n      data,\n      ...config\n    })\n  }\n}\n\n// 创建请求实例\nconst http = new HttpRequest()\n\n// 添加默认请求拦截器\nhttp.addRequestInterceptor((config) => {\n  // 可以在这里添加全局请求处理逻辑\n  if (isDev()) {\n    console.log('Request:', config)\n  }\n  return config\n})\n\n// 添加默认响应拦截器\nhttp.addResponseInterceptor((response) => {\n  // 可以在这里添加全局响应处理逻辑\n  if (isDev()) {\n    console.log('Response:', response)\n  }\n  return response\n})\n\n// 添加默认错误拦截器\nhttp.addErrorInterceptor((error) => {\n  // 处理特定错误\n  if (error.statusCode === 401) {\n    // 未授权，清除token并跳转到登录页\n    removeToken()\n    uni.reLaunch({\n      url: '/pages/auth/login'\n    })\n  }\n  \n  return error\n})\n\nexport default http\nexport { http }\n"], "names": ["__values", "uni", "getApiUrl", "getToken", "isDev", "removeToken"], "mappings": ";;;;AA0CA,MAAM,YAAW;AAAA,EAAjB,cAAA;AACU,SAAmB,sBAAyB;AAC5C,SAAoB,uBAA0B;AAC9C,SAAiB,oBAAuB;AAGxC,SAAA,gBAAwC;AAAA,MAC9C,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,aAAa;AAAA,MACb,aAAa;AAAA,MACb,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,QACN,gBAAgB;AAAA,MACjB;AAAA;EAoPJ;AAAA;AAAA;AAAA;AAAA,EA9OC,sBAAsB,aAA+B;AACnD,SAAK,oBAAoB,KAAK,WAAW;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAKD,uBAAuB,aAAgC;AACrD,SAAK,qBAAqB,KAAK,WAAW;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB,aAA6B;AAC/C,SAAK,kBAAkB,KAAK,WAAW;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAKa,uBAAuB,QAAqB;;;AACxD,UAAI,SAAS;;AACb,iBAA0B,KAAAA,cAAA,SAAA,KAAK,mBAAmB,gBAAA,GAAE,CAAA,GAAA,MAAA,KAAA,GAAA,QAAA;AAA/C,cAAM,cAAW,GAAA;AACpB,mBAAS,MAAM,YAAY,MAAM;AAAA,QAClC;AAAA;;;;;;;;;;;AACD,aAAO;AAAA,KACR;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAKa,wBAAwB,UAAa;;;AACjD,UAAI,SAAS;;AACb,iBAA0B,KAAAA,cAAA,SAAA,KAAK,oBAAoB,gBAAA,GAAE,CAAA,GAAA,MAAA,KAAA,GAAA,QAAA;AAAhD,cAAM,cAAW,GAAA;AACpB,mBAAS,MAAM,YAAY,MAAM;AAAA,QAClC;AAAA;;;;;;;;;;;AACD,aAAO;AAAA,KACR;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAKa,qBAAqB,OAAU;;;AAC3C,UAAI,SAAS;;AACb,iBAA0B,KAAAA,cAAA,SAAA,KAAK,iBAAiB,gBAAA,GAAE,CAAA,GAAA,MAAA,KAAA,GAAA,QAAA;AAA7C,cAAM,cAAW,GAAA;AACpB,mBAAS,MAAM,YAAY,MAAM;AAAA,QAClC;AAAA;;;;;;;;;;;AACD,aAAO;AAAA,KACR;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAKO,SAAS,KAAa,QAA4B;AACxD,QAAI,CAAC,UAAU,OAAO,KAAK,MAAM,EAAE,WAAW,GAAG;AAC/C,aAAO;AAAA,IACR;AAED,UAAM,cAAc,OAAO,KAAK,MAAM,EACnC,IAAI,SAAO;AAAA,aAAA,GAAG,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,OAAO,GAAG,CAAC,CAAC;AAAA,IAAE,CAAA,EAC1E,KAAK,GAAG;AAEX,WAAO,OAAO,IAAI,SAAS,GAAG,IAAI,MAAM,OAAO;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAKO,YAAY,MAAY;AAC9BC,kBAAAA,MAAI,YAAY;AAAA,MACd,OAAO;AAAA,MACP,MAAM;AAAA,IACP,CAAA;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKO,cAAW;AACjBA,kBAAG,MAAC,YAAW;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAKO,UAAU,SAAe;AAC/BA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IACX,CAAA;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKK,QAAiB,QAAqB;;AAE1C,YAAM,8CAAmB,KAAK,aAAa,GAAK,MAAM;AAEtD,UAAI;AAEF,cAAM,oBAAoB,MAAM,KAAK,uBAAuB,WAAW;AAGvE,cAAM,UAAUC,uBAAU,KAAK,SAAS,kBAAkB,KAAK,kBAAkB,MAAM,CAAC;AAGxF,cAAM,UAAe,OAAA,OAAA,CAAA,GAAA,kBAAkB,MAAM;AAG7C,YAAI,CAAC,kBAAkB,UAAU;AAC/B,gBAAM,QAAQC,WAAAA;AACd,cAAI,OAAO;AACT,oBAAQ,eAAe,IAAI,UAAU,KAAK;AAAA,UAC3C;AAAA,QACF;AAGD,YAAI,kBAAkB,aAAa;AACjC,eAAK,YAAY,kBAAkB,WAAY;AAAA,QAChD;AAGD,cAAM,WAAW,MAAM,IAAI,QAAa,CAAC,SAAS,WAAM;AACtDF,wBAAAA,MAAI,QAAQ;AAAA,YACV,KAAK;AAAA,YACL,QAAQ,kBAAkB;AAAA,YAC1B,MAAM,kBAAkB;AAAA,YACxB,QAAQ;AAAA,YACR,SAAS,kBAAkB;AAAA,YAC3B,SAAS;AAAA,YACT,MAAM;AAAA,UACP,CAAA;AAAA,QACH,CAAC;AAGD,YAAI,kBAAkB,aAAa;AACjC,eAAK,YAAW;AAAA,QACjB;AAGD,cAAM,sBAAsB,MAAM,KAAK,wBAAwB,QAAQ;AAGvE,cAAM,eAAe,oBAAoB;AAGzC,YAAI,aAAa,SAAS,GAAG;AAC3B,gBAAM,IAAI,MAAM,aAAa,WAAW,MAAM;AAAA,QAC/C;AAED,eAAO;AAAA,MAER,SAAQ,OAAY;AAEnB,YAAI,YAAY,aAAa;AAC3B,eAAK,YAAW;AAAA,QACjB;AAGD,cAAM,mBAAmB,MAAM,KAAK,qBAAqB,KAAK;AAG9D,YAAI,YAAY,WAAW;AACzB,gBAAM,eAAe,iBAAiB,WAAW;AACjD,eAAK,UAAU,YAAY;AAAA,QAC5B;AAGD,YAAIG,aAAK,MAAA,GAAI;AACXH,wBAAc,MAAA,MAAA,SAAA,2BAAA,kBAAkB,gBAAgB;AAAA,QACjD;AAED,cAAM;AAAA,MACP;AAAA,KACF;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAKD,IAAa,KAAa,QAA8B,QAA+B;AACrF,WAAO,KAAK,QAAO,OAAA,OAAA,EACjB,KACA,QAAQ,OACR,UACG,MAAM;EAEZ;AAAA;AAAA;AAAA;AAAA,EAKD,KAAc,KAAa,MAAY,QAA+B;AACpE,WAAO,KAAK,QAAO,OAAA,OAAA,EACjB,KACA,QAAQ,QACR,QACG,MAAM;EAEZ;AAAA;AAAA;AAAA;AAAA,EAKD,IAAa,KAAa,MAAY,QAA+B;AACnE,WAAO,KAAK,QAAO,OAAA,OAAA,EACjB,KACA,QAAQ,OACR,QACG,MAAM;EAEZ;AAAA;AAAA;AAAA;AAAA,EAKD,OAAgB,KAAa,QAA8B,QAA+B;AACxF,WAAO,KAAK,QAAO,OAAA,OAAA,EACjB,KACA,QAAQ,UACR,UACG,MAAM;EAEZ;AAAA;AAAA;AAAA;AAAA,EAKD,MAAe,KAAa,MAAY,QAA+B;AACrE,WAAO,KAAK,QAAO,OAAA,OAAA,EACjB,KACA,QAAQ,SACR,QACG,MAAM;EAEZ;AACF;AAGD,MAAM,OAAO,IAAI,YAAa;AAG9B,KAAK,sBAAsB,CAAC,WAAM;AAEhC,MAAIG,aAAK,MAAA,GAAI;AACXH,kBAAA,MAAA,MAAA,OAAA,2BAAY,YAAY,MAAM;AAAA,EAC/B;AACD,SAAO;AACT,CAAC;AAGD,KAAK,uBAAuB,CAAC,aAAQ;AAEnC,MAAIG,aAAK,MAAA,GAAI;AACXH,kBAAY,MAAA,MAAA,OAAA,2BAAA,aAAa,QAAQ;AAAA,EAClC;AACD,SAAO;AACT,CAAC;AAGD,KAAK,oBAAoB,CAAC,UAAK;AAE7B,MAAI,MAAM,eAAe,KAAK;AAE5BI,eAAAA;AACAJ,kBAAAA,MAAI,SAAS;AAAA,MACX,KAAK;AAAA,IACN,CAAA;AAAA,EACF;AAED,SAAO;AACT,CAAC;;"}