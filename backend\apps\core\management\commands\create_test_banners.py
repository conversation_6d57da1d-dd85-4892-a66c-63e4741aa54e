from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from apps.core.models import Banner
import requests
from io import BytesIO


class Command(BaseCommand):
    help = '创建测试轮播图数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='清除现有轮播图数据',
        )

    def handle(self, *args, **options):
        if options['clear']:
            Banner.objects.all().delete()
            self.stdout.write(
                self.style.SUCCESS('已清除所有轮播图数据')
            )

        # 创建测试轮播图数据
        banners_data = [
            {
                'title': '夏季新品上市',
                'link_type': 'category',
                'link_value': '1',
                'position': 'home',
                'sort_order': 1,
                'image_url': 'https://via.placeholder.com/750x300/FF6B6B/FFFFFF?text=夏季新品上市'
            },
            {
                'title': '限时特惠活动',
                'link_type': 'url',
                'link_value': '/pages/products/list?is_hot=true',
                'position': 'home',
                'sort_order': 2,
                'image_url': 'https://via.placeholder.com/750x300/4ECDC4/FFFFFF?text=限时特惠活动'
            },
            {
                'title': '分销招募中',
                'link_type': 'page',
                'link_value': '/pages/distribution/index',
                'position': 'home',
                'sort_order': 3,
                'image_url': 'https://via.placeholder.com/750x300/45B7D1/FFFFFF?text=分销招募中'
            },
            {
                'title': '品质保证',
                'link_type': 'none',
                'link_value': '',
                'position': 'home',
                'sort_order': 4,
                'image_url': 'https://via.placeholder.com/750x300/96CEB4/FFFFFF?text=品质保证'
            }
        ]

        created_count = 0
        for banner_data in banners_data:
            try:
                # 下载图片
                image_url = banner_data.pop('image_url')
                response = requests.get(image_url, timeout=10)
                if response.status_code == 200:
                    # 创建轮播图
                    banner = Banner.objects.create(**banner_data)
                    
                    # 保存图片
                    image_content = ContentFile(response.content)
                    banner.image.save(
                        f'banner_{banner.id}.jpg',
                        image_content,
                        save=True
                    )
                    
                    created_count += 1
                    self.stdout.write(f'创建轮播图: {banner.title}')
                else:
                    self.stdout.write(
                        self.style.WARNING(f'无法下载图片: {image_url}')
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'创建轮播图失败: {str(e)}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'成功创建 {created_count} 个轮播图')
        )
