from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from drf_spectacular.views import SpectacularAPIView, SpectacularRedocView, SpectacularSwaggerView

from .views import SystemInfoView, HealthCheckView, SystemStatsView, BannerViewSet

app_name = 'core'

# 创建路由器
router = DefaultRouter()
router.register(r'banners', BannerViewSet, basename='banner')

urlpatterns = [
    # API文档
    path('schema/', SpectacularAPIView.as_view(), name='schema'),
    path('docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),

    # 系统功能API
    path('system/info/', SystemInfoView.as_view(), name='system-info'),
    path('system/health/', HealthCheckView.as_view(), name='health-check'),
    path('system/stats/', SystemStatsView.as_view(), name='system-stats'),

    # 轮播图API
    path('', include(router.urls)),
]