"use strict";
const common_vendor = require("../common/vendor.js");
const utils_config = require("./config.js");
const STORAGE_PREFIX = "zmkj_";
function getStorageKey(key) {
  return STORAGE_PREFIX + key;
}
function getStorage(key, defaultValue = null) {
  try {
    const storageKey = getStorageKey(key);
    const value = common_vendor.index.getStorageSync(storageKey);
    if (utils_config.isDev()) {
      common_vendor.index.__f__("log", "at utils/storage.ts:54", `Storage Get: ${key} =`, value);
    }
    return value !== "" ? value : defaultValue;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/storage.ts:59", `获取存储失败 [${key}]:`, error);
    return defaultValue;
  }
}
function removeStorage(key) {
  try {
    const storageKey = getStorageKey(key);
    common_vendor.index.removeStorageSync(storageKey);
    if (utils_config.isDev()) {
      common_vendor.index.__f__("log", "at utils/storage.ts:75", `Storage Remove: ${key}`);
    }
    return true;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/storage.ts:80", `移除存储失败 [${key}]:`, error);
    return false;
  }
}
exports.getStorage = getStorage;
exports.removeStorage = removeStorage;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/storage.js.map
