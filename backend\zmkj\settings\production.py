"""
智梦科技系统生产环境配置
创建时间：2025年7月24日
"""

from .base import *

# 生产环境特定设置
DEBUG = False
ALLOWED_HOSTS = [
    'zmkj.live',
    'www.zmkj.live',
    'zmkj.nat100.top',  # 开发域名
    'gw.zmkj.live',     # 生产API域名
    'imggw.zmkj.live',  # CDN域名
    config('MAIN_DOMAIN', default='zmkj.live').replace('https://', '').replace('http://', ''),
]

# 生产环境数据库
DATABASES = {
    'default': {
        'ENGINE': f'django.db.backends.{config("DATABASE_ENGINE", default="postgresql")}',
        'NAME': config('DATABASE_NAME'),
        'USER': config('DATABASE_USER'),
        'PASSWORD': config('DATABASE_PASSWORD'),
        'HOST': config('DATABASE_HOST', default='localhost'),
        'PORT': config('DATABASE_PORT', default='5432'),
        'CONN_MAX_AGE': 600,  # 连接池
        'OPTIONS': {
            'charset': 'utf8mb4',
        } if config("DATABASE_ENGINE", default="postgresql") == 'mysql' else {},
    }
}

# 生产环境缓存 - 使用Redis
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': f'redis://{config("REDIS_HOST", default="localhost")}:{config("REDIS_PORT", default="6379")}/{config("REDIS_DB", default="0")}',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
        'KEY_PREFIX': 'zmkj',
        'TIMEOUT': 300,
    }
}

# Session使用Redis存储
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'

# 生产环境安全设置
SECURE_SSL_REDIRECT = True
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_SECONDS = 31536000  # 1年
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
X_FRAME_OPTIONS = 'DENY'

# Cookie安全设置
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# CORS设置 - 生产环境严格控制，支持http和https
CORS_ALLOW_ALL_ORIGINS = False
CORS_ALLOWED_ORIGINS = [
    # 主域名 - 支持http和https
    f'http://{config("MAIN_DOMAIN", default="zmkj.live")}',
    f'https://{config("MAIN_DOMAIN", default="zmkj.live")}',
    f'http://www.{config("MAIN_DOMAIN", default="zmkj.live")}',
    f'https://www.{config("MAIN_DOMAIN", default="zmkj.live")}',
    # CDN域名 - 支持http和https
    f'http://{config("CDN_DOMAIN", default="cdn.zmkj.live")}',
    f'https://{config("CDN_DOMAIN", default="cdn.zmkj.live")}',
    # 开发域名 - 支持http和https
    'http://zmkj.nat100.top',
    'https://zmkj.nat100.top',
]
CORS_ALLOW_CREDENTIALS = True

# 允许的请求头
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

# 允许的HTTP方法
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

# 静态文件设置
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# 文件存储设置
DEFAULT_FILE_STORAGE = 'django.core.files.storage.FileSystemStorage'

# 日志级别
LOGGING['loggers']['django']['level'] = 'WARNING'
LOGGING['loggers']['zmkj']['level'] = 'INFO'

# 管理员邮箱
ADMINS = [
    ('智梦科技技术团队', config('ADMIN_EMAIL', default='<EMAIL>')),
]
MANAGERS = ADMINS

# 错误报告
SERVER_EMAIL = config('SERVER_EMAIL', default='<EMAIL>')

# 邮件配置 - 生产环境使用真实SMTP
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'

# 性能优化
CONN_MAX_AGE = 60  # 数据库连接保持时间

# 压缩和优化
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# 移除开发工具
try:
    INSTALLED_APPS.remove('debug_toolbar')
except ValueError:
    pass

# 移除调试中间件
MIDDLEWARE = [m for m in MIDDLEWARE if 'debug_toolbar' not in m]

print("�� 智梦科技系统 - 生产环境已加载") 