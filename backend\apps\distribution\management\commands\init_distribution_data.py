from django.core.management.base import BaseCommand
from django.db import transaction
from decimal import Decimal
from apps.distribution.models import DistributionConfig, DistributionLevel


class Command(BaseCommand):
    help = '初始化分销系统基础数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始初始化分销系统基础数据...'))
        
        with transaction.atomic():
            # 创建分销配置
            self.create_distribution_configs()
            
            # 创建分销等级
            self.create_distribution_levels()
        
        self.stdout.write(self.style.SUCCESS('分销系统基础数据初始化完成！'))

    def create_distribution_configs(self):
        """创建分销配置"""
        configs = [
            {
                'name': '分销系统开关',
                'key': 'distribution_enabled',
                'value': 'true',
                'description': '是否启用分销系统'
            },
            {
                'name': '最大分销层级',
                'key': 'max_distribution_level',
                'value': '3',
                'description': '最大分销层级数量'
            },
            {
                'name': '最小提现金额',
                'key': 'min_withdrawal_amount',
                'value': '10.00',
                'description': '最小提现金额'
            },
            {
                'name': '提现手续费率',
                'key': 'withdrawal_fee_rate',
                'value': '0.01',
                'description': '提现手续费率（1%）'
            },
            {
                'name': '佣金结算周期',
                'key': 'commission_settlement_days',
                'value': '7',
                'description': '佣金结算周期（天）'
            },
            {
                'name': '推广码有效期',
                'key': 'promotion_code_validity_days',
                'value': '365',
                'description': '推广码默认有效期（天）'
            },
        ]
        
        for config_data in configs:
            config, created = DistributionConfig.objects.get_or_create(
                key=config_data['key'],
                defaults={
                    'name': config_data['name'],
                    'value': config_data['value'],
                    'description': config_data['description'],
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f'创建配置: {config.name}')
            else:
                self.stdout.write(f'配置已存在: {config.name}')

    def create_distribution_levels(self):
        """创建分销等级"""
        levels = [
            {
                'name': '普通分销商',
                'level': 1,
                'commission_rate': Decimal('0.05'),  # 5%
                'min_sales': Decimal('0.00'),
                'min_referrals': 0,
                'description': '初级分销商，佣金比例5%'
            },
            {
                'name': '银牌分销商',
                'level': 2,
                'commission_rate': Decimal('0.08'),  # 8%
                'min_sales': Decimal('1000.00'),
                'min_referrals': 5,
                'description': '银牌分销商，需要累计销售1000元，推荐5人'
            },
            {
                'name': '金牌分销商',
                'level': 3,
                'commission_rate': Decimal('0.12'),  # 12%
                'min_sales': Decimal('5000.00'),
                'min_referrals': 20,
                'description': '金牌分销商，需要累计销售5000元，推荐20人'
            },
            {
                'name': '钻石分销商',
                'level': 4,
                'commission_rate': Decimal('0.15'),  # 15%
                'min_sales': Decimal('20000.00'),
                'min_referrals': 50,
                'description': '钻石分销商，需要累计销售20000元，推荐50人'
            },
            {
                'name': '皇冠分销商',
                'level': 5,
                'commission_rate': Decimal('0.20'),  # 20%
                'min_sales': Decimal('50000.00'),
                'min_referrals': 100,
                'description': '皇冠分销商，需要累计销售50000元，推荐100人'
            },
        ]
        
        for level_data in levels:
            level, created = DistributionLevel.objects.get_or_create(
                level=level_data['level'],
                defaults={
                    'name': level_data['name'],
                    'commission_rate': level_data['commission_rate'],
                    'min_sales': level_data['min_sales'],
                    'min_referrals': level_data['min_referrals'],
                    'description': level_data['description'],
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f'创建等级: {level.name} (等级{level.level})')
            else:
                self.stdout.write(f'等级已存在: {level.name} (等级{level.level})')
