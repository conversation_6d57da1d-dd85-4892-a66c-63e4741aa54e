from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'wechat'

# API路由
router = DefaultRouter()
router.register(r'users', views.WechatUserViewSet, basename='wechat-users')
router.register(r'messages', views.WechatMessageViewSet, basename='wechat-messages')
router.register(r'menus', views.WechatMenuViewSet, basename='wechat-menus')
router.register(r'templates', views.WechatTemplateViewSet, basename='wechat-templates')
router.register(r'configs', views.WechatConfigViewSet, basename='wechat-configs')
router.register(r'payment', views.WechatPaymentViewSet, basename='wechat-payment')
router.register(r'stats', views.WechatStatsViewSet, basename='wechat-stats')

urlpatterns = [
    # API路由
    path('api/', include(router.urls)),
    
    # 微信公众号相关
    path('official/', include([
        path('verify/', views.WechatOfficialVerifyView.as_view(), name='official-verify'),
        path('callback/', views.WechatOfficialCallbackView.as_view(), name='official-callback'),
        path('menu/create/', views.WechatMenuCreateView.as_view(), name='menu-create'),
        path('menu/delete/', views.WechatMenuDeleteView.as_view(), name='menu-delete'),
    ])),
    
    # 企业微信相关
    path('work/', include([
        path('callback/', views.WechatWorkCallbackView.as_view(), name='work-callback'),
        path('contacts/sync/', views.WechatWorkContactsSyncView.as_view(), name='work-contacts-sync'),
    ])),
    
    # 小程序相关
    path('miniprogram/', include([
        path('auth/', views.MiniprogramAuthView.as_view(), name='miniprogram-auth'),
        path('decrypt/', views.MiniprogramDecryptView.as_view(), name='miniprogram-decrypt'),
    ])),
    
    # 消息推送相关
    path('push/', include([
        path('template/', views.TemplatePushView.as_view(), name='template-push'),
        path('custom/', views.CustomPushView.as_view(), name='custom-push'),
    ])),
]
