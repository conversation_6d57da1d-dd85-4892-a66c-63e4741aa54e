from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Sum
from apps.core.admin import admin_site
from .models import Order, OrderItem, Payment, OrderLog
from django.utils import timezone


class OrderItemInline(admin.TabularInline):
    """订单项内联管理"""
    model = OrderItem
    extra = 0
    readonly_fields = ('subtotal',)
    fields = ('product', 'product_name', 'product_sku', 'product_price', 'quantity', 'subtotal')
    
    def get_readonly_fields(self, request, obj=None):
        """如果订单已支付，设置为只读"""
        if obj and obj.status != 'pending':
            return self.readonly_fields + ('product', 'product_name', 'product_sku', 'product_price', 'quantity')
        return self.readonly_fields


class PaymentInline(admin.TabularInline):
    """支付记录内联管理"""
    model = Payment
    extra = 0
    readonly_fields = ('payment_no', 'paid_at', 'callback_data')
    fields = ('payment_method', 'payment_type', 'amount', 'status', 'third_party_no', 'paid_at', 'remark')


class OrderLogInline(admin.TabularInline):
    """订单日志内联管理"""
    model = OrderLog
    extra = 0
    readonly_fields = ('action_type', 'action_user', 'old_status', 'new_status', 'content', 'created_at')
    fields = ('action_type', 'action_user', 'old_status', 'new_status', 'content', 'created_at')
    
    def has_add_permission(self, request, obj=None):
        return False


class OrderAdmin(admin.ModelAdmin):
    """订单管理"""
    list_display = (
        'order_no', 'user', 'status_display', 'final_amount', 'payment_method',
        'recipient_name', 'created_at', 'paid_at', 'shipped_at'
    )
    list_filter = ('status', 'payment_method', 'created_at', 'paid_at')
    search_fields = ('order_no', 'user__username', 'recipient_name', 'recipient_phone')
    readonly_fields = ('order_no', 'created_at', 'updated_at', 'order_summary')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)
    
    fieldsets = (
        ('订单信息', {
            'fields': ('order_no', 'user', 'status', 'payment_method', 'order_summary')
        }),
        ('金额信息', {
            'fields': ('total_amount', 'shipping_fee', 'discount_amount', 'final_amount')
        }),
        ('收货信息', {
            'fields': ('recipient_name', 'recipient_phone', 'shipping_address')
        }),
        ('时间记录', {
            'fields': ('created_at', 'paid_at', 'shipped_at', 'delivered_at', 'completed_at'),
            'classes': ('collapse',)
        }),
        ('其他信息', {
            'fields': ('tracking_number', 'remark', 'admin_remark'),
            'classes': ('collapse',)
        }),
    )
    
    inlines = [OrderItemInline, PaymentInline, OrderLogInline]
    
    def status_display(self, obj):
        """状态显示"""
        color = obj.get_status_display_color()
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_display.short_description = '订单状态'
    
    def order_summary(self, obj):
        """订单摘要"""
        items = obj.items.all()
        if not items:
            return '暂无商品'
        
        summary = []
        for item in items[:3]:  # 只显示前3个商品
            summary.append(f"{item.product_name} × {item.quantity}")
        
        result = '<br>'.join(summary)
        if items.count() > 3:
            result += f'<br>等共 {items.count()} 个商品'
        
        return format_html(result)
    order_summary.short_description = '商品摘要'
    
    actions = ['make_paid', 'make_shipped', 'make_completed', 'make_cancelled']
    
    def make_paid(self, request, queryset):
        """批量标记为已支付"""
        count = 0
        for order in queryset:
            if order.status == 'pending':
                order.status = 'paid'
                order.paid_at = timezone.now()
                order.save()
                count += 1
        self.message_user(request, f'已将 {count} 个订单标记为已支付')
    make_paid.short_description = '标记为已支付'
    
    def make_shipped(self, request, queryset):
        """批量标记为已发货"""
        count = 0
        for order in queryset:
            if order.status in ['paid', 'processing']:
                order.status = 'shipped'
                order.shipped_at = timezone.now()
                order.save()
                count += 1
        self.message_user(request, f'已将 {count} 个订单标记为已发货')
    make_shipped.short_description = '标记为已发货'
    
    def make_completed(self, request, queryset):
        """批量标记为已完成"""
        count = 0
        for order in queryset:
            if order.status in ['shipped', 'delivered']:
                order.status = 'completed'
                order.completed_at = timezone.now()
                order.save()
                count += 1
        self.message_user(request, f'已将 {count} 个订单标记为已完成')
    make_completed.short_description = '标记为已完成'
    
    def make_cancelled(self, request, queryset):
        """批量取消订单"""
        count = 0
        for order in queryset:
            if order.can_cancel():
                order.status = 'cancelled'
                order.save()
                count += 1
        self.message_user(request, f'已取消 {count} 个订单')
    make_cancelled.short_description = '取消订单'
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user').prefetch_related('items__product')


class OrderItemAdmin(admin.ModelAdmin):
    """订单项管理"""
    list_display = ('order', 'product_name', 'product_sku', 'product_price', 'quantity', 'subtotal', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('order__order_no', 'product_name', 'product_sku')
    readonly_fields = ('subtotal', 'created_at')
    ordering = ('-created_at',)
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('order', 'product')


class PaymentAdmin(admin.ModelAdmin):
    """支付记录管理"""
    list_display = (
        'payment_no', 'order', 'payment_method', 'payment_type', 
        'amount', 'status_display', 'paid_at', 'created_at'
    )
    list_filter = ('payment_method', 'payment_type', 'status', 'created_at')
    search_fields = ('payment_no', 'third_party_no', 'order__order_no')
    readonly_fields = ('payment_no', 'paid_at', 'callback_data', 'created_at', 'updated_at')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)
    
    fieldsets = (
        ('支付信息', {
            'fields': ('payment_no', 'order', 'payment_method', 'payment_type', 'amount', 'status')
        }),
        ('第三方信息', {
            'fields': ('third_party_no', 'paid_at', 'callback_data')
        }),
        ('其他信息', {
            'fields': ('remark', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def status_display(self, obj):
        """状态显示"""
        color_map = {
            'pending': '#f39c12',
            'processing': '#3498db',
            'success': '#27ae60',
            'failed': '#e74c3c',
            'cancelled': '#95a5a6',
            'refunded': '#e67e22',
        }
        color = color_map.get(obj.status, '#95a5a6')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_display.short_description = '支付状态'
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('order')


class OrderLogAdmin(admin.ModelAdmin):
    """订单日志管理"""
    list_display = ('order', 'action_type', 'action_user', 'old_status', 'new_status', 'created_at')
    list_filter = ('action_type', 'created_at')
    search_fields = ('order__order_no', 'content')
    readonly_fields = ('created_at',)
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)
    
    fieldsets = (
        ('日志信息', {
            'fields': ('order', 'action_type', 'action_user', 'content')
        }),
        ('状态变更', {
            'fields': ('old_status', 'new_status')
        }),
        ('额外数据', {
            'fields': ('extra_data', 'created_at'),
            'classes': ('collapse',)
        }),
    )
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('order', 'action_user')


# 注册到自定义管理站点
admin_site.register(Order, OrderAdmin)
admin_site.register(OrderItem, OrderItemAdmin)
admin_site.register(Payment, PaymentAdmin)
admin_site.register(OrderLog, OrderLogAdmin)
