<template>
	<view class="header-container" :class="headerClass" :style="headerStyle">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }" v-if="showStatusBar"></view>
		
		<!-- 导航栏内容 -->
		<view class="header-content" :style="{ height: navBarHeight + 'px' }">
			<!-- 左侧内容 -->
			<view class="header-left">
				<view class="back-button" @click="handleBack" v-if="showBack">
					<Icon name="arrow-left" size="18" :color="iconColor" />
				</view>
				<slot name="left"></slot>
			</view>
			
			<!-- 中间标题 -->
			<view class="header-center">
				<text class="header-title" :style="titleStyle" v-if="title">{{ title }}</text>
				<slot name="center"></slot>
			</view>
			
			<!-- 右侧内容 -->
			<view class="header-right">
				<slot name="right"></slot>
				<view class="home-button" @click="handleHome" v-if="showHome">
					<Icon name="home" size="18" :color="iconColor" />
				</view>
			</view>
		</view>
		
		<!-- 底部分割线 -->
		<view class="header-border" v-if="showBorder"></view>
	</view>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import Icon from './Icon.uvue'

// Props定义
interface Props {
	title?: string
	showBack?: boolean
	showHome?: boolean
	showBorder?: boolean
	showStatusBar?: boolean
	backgroundColor?: string
	textColor?: string
	iconColor?: string
	fixed?: boolean
	transparent?: boolean
	type?: 'default' | 'transparent' | 'gradient'
}

const props = withDefaults(defineProps<Props>(), {
	title: '',
	showBack: true,
	showHome: false,
	showBorder: true,
	showStatusBar: true,
	backgroundColor: '#FFFFFF',
	textColor: '#333333',
	iconColor: '#333333',
	fixed: true,
	transparent: false,
	type: 'default'
})

// Emits定义
const emit = defineEmits<{
	back: []
	home: []
}>()

// 响应式数据
const statusBarHeight = ref(0)
const navBarHeight = ref(44)

// 计算属性
const headerClass = computed(() => {
	const classes = ['header']
	if (props.fixed) classes.push('header-fixed')
	if (props.transparent) classes.push('header-transparent')
	classes.push(`header-${props.type}`)
	return classes.join(' ')
})

const headerStyle = computed(() => {
	const style: any = {}
	
	if (!props.transparent) {
		style.backgroundColor = props.backgroundColor
	}
	
	if (props.type === 'gradient') {
		style.background = 'linear-gradient(135deg, #007AFF 0%, #5856D6 100%)'
	}
	
	return style
})

const titleStyle = computed(() => {
	return {
		color: props.textColor
	}
})

// 生命周期
onMounted(() => {
	// 获取系统信息
	const systemInfo = uni.getSystemInfoSync()
	statusBarHeight.value = systemInfo.statusBarHeight || 0
	
	// 根据平台设置导航栏高度
	if (systemInfo.platform === 'ios') {
		navBarHeight.value = 44
	} else {
		navBarHeight.value = 48
	}
})

// 方法
const handleBack = () => {
	emit('back')
	// 默认返回行为
	const pages = getCurrentPages()
	if (pages.length > 1) {
		uni.navigateBack()
	} else {
		uni.switchTab({
			url: '/pages/index/index'
		})
	}
}

const handleHome = () => {
	emit('home')
	uni.switchTab({
		url: '/pages/index/index'
	})
}
</script>

<style lang="scss" scoped>
.header-container {
	position: relative;
	
	&.header-fixed {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
	}
	
	&.header-transparent {
		background-color: transparent !important;
	}
	
	&.header-gradient {
		background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
		
		.header-title {
			color: #FFFFFF !important;
		}
		
		.back-button,
		.home-button {
			color: #FFFFFF !important;
		}
	}
	
	.status-bar {
		width: 100%;
	}
	
	.header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 15px;
		position: relative;
		
		.header-left,
		.header-right {
			display: flex;
			align-items: center;
			min-width: 60px;
		}
		
		.header-left {
			justify-content: flex-start;
		}
		
		.header-right {
			justify-content: flex-end;
		}
		
		.header-center {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			position: absolute;
			left: 60px;
			right: 60px;
			
			.header-title {
				font-size: 18px;
				font-weight: 600;
				color: #333333;
				text-align: center;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
		
		.back-button,
		.home-button {
			width: 40px;
			height: 40px;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 20px;
			
			&:active {
				background-color: rgba(0, 0, 0, 0.1);
			}
		}
	}
	
	.header-border {
		height: 1px;
		background-color: #EEEEEE;
	}
}
</style>
