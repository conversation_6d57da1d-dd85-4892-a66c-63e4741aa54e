# 智梦科技系统后端开发Makefile

# 变量定义
PYTHON = python
MANAGE = ./manage.py
TEST_SCRIPT = ./scripts/run_all_tests.py

# 帮助信息
.PHONY: help
help:
	@echo "智梦科技系统后端开发命令"
	@echo ""
	@echo "可用命令:"
	@echo "  setup      - 设置开发环境"
	@echo "  install    - 安装依赖包"
	@echo "  migrate    - 运行数据库迁移"
	@echo "  run        - 启动开发服务器"
	@echo "  test       - 运行所有测试"
	@echo "  coverage   - 运行测试并生成覆盖率报告"
	@echo "  superuser  - 创建超级用户"
	@echo "  collect    - 收集静态文件"
	@echo "  clean      - 清理临时文件"
	@echo "  check      - 检查代码质量"
	@echo "  docs       - 生成API文档"
	@echo "  backup     - 备份数据库"
	@echo "  restore    - 恢复数据库"

# 设置开发环境
.PHONY: setup
setup:
	@echo "设置开发环境..."
	python -m venv venv
	venv\Scripts\activate && pip install -r requirements.txt

# 安装依赖包
.PHONY: install
install:
	@echo "安装依赖包..."
	pip install -r requirements.txt

# 运行数据库迁移
.PHONY: migrate
migrate:
	@echo "运行数据库迁移..."
	$(PYTHON) $(MANAGE) migrate

# 启动开发服务器
.PHONY: run
run:
	@echo "启动开发服务器..."
	$(PYTHON) $(MANAGE) runserver

# 运行所有测试
.PHONY: test
test:
	@echo "运行所有测试..."
	$(PYTHON) $(TEST_SCRIPT)

# 运行测试并生成覆盖率报告
.PHONY: coverage
coverage:
	@echo "运行测试并生成覆盖率报告..."
	coverage run --source='.' $(MANAGE) test
	coverage report
	coverage html

# 创建超级用户
.PHONY: superuser
superuser:
	@echo "创建超级用户..."
	$(PYTHON) $(MANAGE) createsuperuser

# 收集静态文件
.PHONY: collect
collect:
	@echo "收集静态文件..."
	$(PYTHON) $(MANAGE) collectstatic --noinput

# 清理临时文件
.PHONY: clean
clean:
	@echo "清理临时文件..."
	find . -name "*.pyc" -delete
	find . -name "__pycache__" -delete
	rm -rf *.log
	rm -rf coverage_html_report/
	rm -rf htmlcov/

# 检查代码质量
.PHONY: check
check:
	@echo "检查代码质量..."
	$(PYTHON) $(MANAGE) check

# 生成API文档
.PHONY: docs
docs:
	@echo "生成API文档..."
	$(PYTHON) $(MANAGE) spectacular --file schema.yml

# 备份数据库
.PHONY: backup
backup:
	@echo "备份数据库..."
	$(PYTHON) $(MANAGE) dumpdata > backup_$(shell date +%Y%m%d_%H%M%S).json

# 恢复数据库
.PHONY: restore
restore:
	@echo "恢复数据库..."
	@echo "请指定要恢复的备份文件: make restore file=backup_file.json"
	@if [ -n "$(file)" ]; then \
		$(PYTHON) $(MANAGE) loaddata $(file); \
	else \
		echo "错误: 请指定要恢复的备份文件"; \
	fi
