{"version": 3, "file": "auth.js", "sources": ["utils/auth.ts"], "sourcesContent": ["/**\n * 认证相关工具函数\n * 处理用户登录状态、Token管理等\n */\n\nimport { getStorage, setStorage, removeStorage } from './storage'\n\n// Token存储键名\nconst TOKEN_KEY = 'user_token'\nconst USER_INFO_KEY = 'user_info'\nconst LOGIN_TIME_KEY = 'login_time'\n\n// Token过期时间（7天，单位：毫秒）\nconst TOKEN_EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000\n\n// 用户信息接口\nexport interface UserInfo {\n  id: number\n  username: string\n  nickname: string\n  avatar: string\n  phone: string\n  email?: string\n  is_distributor: boolean\n  distributor_level?: number\n  parent_id?: number\n  created_at: string\n  updated_at: string\n}\n\n/**\n * 设置Token\n * @param token 用户Token\n */\nexport function setToken(token: string): void {\n  setStorage(TOKEN_KEY, token)\n  setStorage(LOGIN_TIME_KEY, Date.now())\n}\n\n/**\n * 获取Token\n * @returns Token字符串或null\n */\nexport function getToken(): string | null {\n  const token = getStorage(TOKEN_KEY)\n  const loginTime = getStorage(LOGIN_TIME_KEY)\n  \n  if (!token || !loginTime) {\n    return null\n  }\n  \n  // 检查Token是否过期\n  const now = Date.now()\n  if (now - loginTime > TOKEN_EXPIRE_TIME) {\n    removeToken()\n    return null\n  }\n  \n  return token\n}\n\n/**\n * 移除Token\n */\nexport function removeToken(): void {\n  removeStorage(TOKEN_KEY)\n  removeStorage(LOGIN_TIME_KEY)\n  removeStorage(USER_INFO_KEY)\n}\n\n/**\n * 检查Token是否有效\n * @returns 是否有效\n */\nexport function isTokenValid(): boolean {\n  return getToken() !== null\n}\n\n/**\n * 设置用户信息\n * @param userInfo 用户信息\n */\nexport function setUserInfo(userInfo: UserInfo): void {\n  setStorage(USER_INFO_KEY, userInfo)\n}\n\n/**\n * 获取用户信息\n * @returns 用户信息或null\n */\nexport function getUserInfo(): UserInfo | null {\n  return getStorage(USER_INFO_KEY)\n}\n\n/**\n * 移除用户信息\n */\nexport function removeUserInfo(): void {\n  removeStorage(USER_INFO_KEY)\n}\n\n/**\n * 检查用户是否已登录\n * @returns 是否已登录\n */\nexport function isLoggedIn(): boolean {\n  return isTokenValid() && getUserInfo() !== null\n}\n\n/**\n * 用户登录\n * @param token 用户Token\n * @param userInfo 用户信息\n */\nexport function login(token: string, userInfo: UserInfo): void {\n  setToken(token)\n  setUserInfo(userInfo)\n}\n\n/**\n * 用户登出\n */\nexport function logout(): void {\n  removeToken()\n  removeUserInfo()\n}\n\n/**\n * 检查用户是否为分销商\n * @returns 是否为分销商\n */\nexport function isDistributor(): boolean {\n  const userInfo = getUserInfo()\n  return userInfo?.is_distributor || false\n}\n\n/**\n * 获取用户分销等级\n * @returns 分销等级\n */\nexport function getDistributorLevel(): number {\n  const userInfo = getUserInfo()\n  return userInfo?.distributor_level || 0\n}\n\n/**\n * 获取用户上级ID\n * @returns 上级用户ID\n */\nexport function getParentId(): number | null {\n  const userInfo = getUserInfo()\n  return userInfo?.parent_id || null\n}\n\n/**\n * 微信登录\n * @returns Promise<{code: string, userInfo?: any}>\n */\nexport function wxLogin(): Promise<{code: string, userInfo?: any}> {\n  return new Promise((resolve, reject) => {\n    // 获取微信登录code\n    uni.login({\n      provider: 'weixin',\n      success: (loginRes) => {\n        if (loginRes.code) {\n          // 获取用户信息\n          uni.getUserProfile({\n            desc: '用于完善用户资料',\n            success: (userRes) => {\n              resolve({\n                code: loginRes.code,\n                userInfo: userRes.userInfo\n              })\n            },\n            fail: () => {\n              // 即使获取用户信息失败，也返回code\n              resolve({\n                code: loginRes.code\n              })\n            }\n          })\n        } else {\n          reject(new Error('获取微信登录code失败'))\n        }\n      },\n      fail: (error) => {\n        reject(error)\n      }\n    })\n  })\n}\n\n/**\n * 检查微信授权状态\n * @param scope 授权范围\n * @returns Promise<boolean>\n */\nexport function checkWxAuth(scope: string): Promise<boolean> {\n  return new Promise((resolve) => {\n    uni.getSetting({\n      success: (res) => {\n        resolve(!!res.authSetting[scope])\n      },\n      fail: () => {\n        resolve(false)\n      }\n    })\n  })\n}\n\n/**\n * 请求微信授权\n * @param scope 授权范围\n * @returns Promise<boolean>\n */\nexport function requestWxAuth(scope: string): Promise<boolean> {\n  return new Promise((resolve) => {\n    uni.authorize({\n      scope,\n      success: () => {\n        resolve(true)\n      },\n      fail: () => {\n        resolve(false)\n      }\n    })\n  })\n}\n\n/**\n * 跳转到登录页面\n * @param redirect 登录成功后的重定向页面\n */\nexport function redirectToLogin(redirect?: string): void {\n  const url = redirect \n    ? `/pages/auth/login?redirect=${encodeURIComponent(redirect)}`\n    : '/pages/auth/login'\n    \n  uni.reLaunch({ url })\n}\n\n/**\n * 检查登录状态，未登录则跳转到登录页\n * @param redirect 登录成功后的重定向页面\n * @returns 是否已登录\n */\nexport function requireLogin(redirect?: string): boolean {\n  if (!isLoggedIn()) {\n    redirectToLogin(redirect)\n    return false\n  }\n  return true\n}\n\n/**\n * 获取Token剩余有效时间（毫秒）\n * @returns 剩余时间或0\n */\nexport function getTokenRemainingTime(): number {\n  const loginTime = getStorage(LOGIN_TIME_KEY)\n  if (!loginTime) {\n    return 0\n  }\n  \n  const now = Date.now()\n  const remaining = TOKEN_EXPIRE_TIME - (now - loginTime)\n  return Math.max(0, remaining)\n}\n\n/**\n * 检查Token是否即将过期（1天内）\n * @returns 是否即将过期\n */\nexport function isTokenExpiringSoon(): boolean {\n  const remaining = getTokenRemainingTime()\n  const oneDayInMs = 24 * 60 * 60 * 1000\n  return remaining > 0 && remaining < oneDayInMs\n}\n\n/**\n * 刷新Token（如果后端支持）\n * @returns Promise<boolean>\n */\nexport async function refreshToken(): Promise<boolean> {\n  try {\n    // 这里需要调用后端的刷新Token接口\n    // const response = await http.post('/auth/refresh-token')\n    // if (response.success) {\n    //   setToken(response.data.token)\n    //   return true\n    // }\n    return false\n  } catch (error) {\n    console.error('刷新Token失败:', error)\n    return false\n  }\n}\n\nexport default {\n  setToken,\n  getToken,\n  removeToken,\n  isTokenValid,\n  setUserInfo,\n  getUserInfo,\n  removeUserInfo,\n  isLoggedIn,\n  login,\n  logout,\n  isDistributor,\n  getDistributorLevel,\n  getParentId,\n  wxLogin,\n  checkWxAuth,\n  requestWxAuth,\n  redirectToLogin,\n  requireLogin,\n  getTokenRemainingTime,\n  isTokenExpiringSoon,\n  refreshToken\n}\n"], "names": ["getStorage", "removeStorage"], "mappings": ";;;AAQA,MAAM,YAAY;AAClB,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AAGvB,MAAM,oBAAoB,IAAI,KAAK,KAAK,KAAK;SA8B7B,WAAQ;AACtB,QAAM,QAAQA,yBAAW,SAAS;AAClC,QAAM,YAAYA,yBAAW,cAAc;AAE3C,MAAI,CAAC,SAAS,CAAC,WAAW;AACxB,WAAO;AAAA,EACR;AAGD,QAAM,MAAM,KAAK;AACjB,MAAI,MAAM,YAAY,mBAAmB;AACvC;AACA,WAAO;AAAA,EACR;AAED,SAAO;AACT;SAKgB,cAAW;AACzBC,gBAAa,cAAC,SAAS;AACvBA,gBAAa,cAAC,cAAc;AAC5BA,gBAAa,cAAC,aAAa;AAC7B;;;"}