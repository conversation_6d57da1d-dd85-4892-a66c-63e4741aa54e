"""
初始化微信权限组管理命令
"""
from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from apps.wechat.models import WechatUser, WechatConfig, WechatMessage, WechatMenu, WechatTemplate
from apps.wechat.permissions import create_wechat_groups


class Command(BaseCommand):
    help = '初始化微信模块的权限组和权限'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='重置所有权限组（删除后重新创建）',
        )
    
    def handle(self, *args, **options):
        self.stdout.write('开始初始化微信权限组...')
        
        if options['reset']:
            self.reset_groups()
        
        # 创建权限组
        created_groups = create_wechat_groups()
        if created_groups:
            self.stdout.write(
                self.style.SUCCESS(f'成功创建权限组: {", ".join(created_groups)}')
            )
        else:
            self.stdout.write('所有权限组已存在')
        
        # 设置权限
        self.setup_permissions()
        
        self.stdout.write(self.style.SUCCESS('微信权限组初始化完成！'))
    
    def reset_groups(self):
        """重置权限组"""
        group_names = ['微信管理员', '支付管理员', '统计查看员']
        
        for group_name in group_names:
            try:
                group = Group.objects.get(name=group_name)
                group.delete()
                self.stdout.write(f'删除权限组: {group_name}')
            except Group.DoesNotExist:
                pass
    
    def setup_permissions(self):
        """设置权限"""
        # 获取微信相关模型的ContentType
        wechat_models = [
            WechatUser, WechatConfig, WechatMessage, 
            WechatMenu, WechatTemplate
        ]
        
        # 微信管理员权限
        try:
            wechat_admin_group = Group.objects.get(name='微信管理员')
            
            for model in wechat_models:
                content_type = ContentType.objects.get_for_model(model)
                permissions = Permission.objects.filter(content_type=content_type)
                
                for permission in permissions:
                    wechat_admin_group.permissions.add(permission)
            
            self.stdout.write('设置微信管理员权限完成')
            
        except Group.DoesNotExist:
            self.stdout.write(self.style.ERROR('微信管理员组不存在'))
        
        # 支付管理员权限（只针对支付相关功能）
        try:
            payment_admin_group = Group.objects.get(name='支付管理员')
            
            # 支付管理员只需要查看用户和配置的权限
            user_content_type = ContentType.objects.get_for_model(WechatUser)
            config_content_type = ContentType.objects.get_for_model(WechatConfig)
            
            view_permissions = Permission.objects.filter(
                content_type__in=[user_content_type, config_content_type],
                codename__startswith='view_'
            )
            
            for permission in view_permissions:
                payment_admin_group.permissions.add(permission)
            
            self.stdout.write('设置支付管理员权限完成')
            
        except Group.DoesNotExist:
            self.stdout.write(self.style.ERROR('支付管理员组不存在'))
        
        # 统计查看员权限（只读权限）
        try:
            stats_viewer_group = Group.objects.get(name='统计查看员')
            
            for model in wechat_models:
                content_type = ContentType.objects.get_for_model(model)
                view_permission = Permission.objects.filter(
                    content_type=content_type,
                    codename__startswith='view_'
                ).first()
                
                if view_permission:
                    stats_viewer_group.permissions.add(view_permission)
            
            self.stdout.write('设置统计查看员权限完成')
            
        except Group.DoesNotExist:
            self.stdout.write(self.style.ERROR('统计查看员组不存在'))
    
    def create_custom_permissions(self):
        """创建自定义权限"""
        custom_permissions = [
            ('can_manage_wechat_config', '可以管理微信配置'),
            ('can_view_wechat_stats', '可以查看微信统计'),
            ('can_manage_wechat_payment', '可以管理微信支付'),
            ('can_send_wechat_message', '可以发送微信消息'),
            ('can_manage_wechat_menu', '可以管理微信菜单'),
        ]
        
        # 获取微信用户模型的ContentType作为载体
        content_type = ContentType.objects.get_for_model(WechatUser)
        
        created_permissions = []
        for codename, name in custom_permissions:
            permission, created = Permission.objects.get_or_create(
                codename=codename,
                name=name,
                content_type=content_type
            )
            if created:
                created_permissions.append(name)
        
        if created_permissions:
            self.stdout.write(
                self.style.SUCCESS(f'创建自定义权限: {", ".join(created_permissions)}')
            )
        
        return created_permissions
    
    def assign_custom_permissions(self):
        """分配自定义权限"""
        content_type = ContentType.objects.get_for_model(WechatUser)
        
        # 微信管理员获得所有自定义权限
        try:
            wechat_admin_group = Group.objects.get(name='微信管理员')
            custom_permissions = Permission.objects.filter(
                content_type=content_type,
                codename__startswith='can_'
            )
            
            for permission in custom_permissions:
                wechat_admin_group.permissions.add(permission)
            
            self.stdout.write('微信管理员自定义权限分配完成')
            
        except Group.DoesNotExist:
            pass
        
        # 支付管理员获得支付相关权限
        try:
            payment_admin_group = Group.objects.get(name='支付管理员')
            payment_permissions = Permission.objects.filter(
                content_type=content_type,
                codename__in=['can_manage_wechat_payment', 'can_view_wechat_stats']
            )
            
            for permission in payment_permissions:
                payment_admin_group.permissions.add(permission)
            
            self.stdout.write('支付管理员自定义权限分配完成')
            
        except Group.DoesNotExist:
            pass
        
        # 统计查看员获得统计查看权限
        try:
            stats_viewer_group = Group.objects.get(name='统计查看员')
            stats_permission = Permission.objects.filter(
                content_type=content_type,
                codename='can_view_wechat_stats'
            ).first()
            
            if stats_permission:
                stats_viewer_group.permissions.add(stats_permission)
            
            self.stdout.write('统计查看员自定义权限分配完成')
            
        except Group.DoesNotExist:
            pass
