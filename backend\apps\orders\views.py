from django.db.models import Q, F, Sum, Count
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend
from .models import Order, OrderItem, Payment, OrderLog
from .serializers import (
    OrderListSerializer, OrderDetailSerializer, OrderCreateSerializer, OrderUpdateSerializer,
    OrderItemSerializer, PaymentSerializer, OrderLogSerializer, OrderSearchSerializer
)


class OrderViewSet(viewsets.ModelViewSet):
    """订单管理视图集"""
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['order_no', 'recipient_name', 'recipient_phone']
    filterset_fields = ['status']  # 只保留一个字段进行测试
    ordering_fields = ['created_at', 'final_amount', 'paid_at']
    ordering = ['-created_at']

    def get_queryset(self):
        """根据用户权限返回不同的查询集"""
        try:
            if self.request.user.is_staff:
                # 管理员可以查看所有订单
                return Order.objects.filter(is_deleted=False).select_related('user').prefetch_related('items__product', 'payments', 'logs')
            else:
                # 普通用户只能查看自己的订单
                return Order.objects.filter(user=self.request.user, is_deleted=False).select_related('user').prefetch_related('items__product', 'payments', 'logs')
        except Exception as e:
            logger.error(f"Error in get_queryset: {str(e)}")
            raise

    def get_serializer_class(self):
        """根据动作返回不同的序列化器"""
        try:
            if self.action == 'list':
                return OrderListSerializer
            elif self.action == 'create':
                return OrderCreateSerializer
            elif self.action in ['update', 'partial_update']:
                return OrderUpdateSerializer
            return OrderDetailSerializer
        except Exception as e:
            logger.error(f"Error in get_serializer_class: {str(e)}")
            raise

    def create(self, request, *args, **kwargs):
        """创建订单"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        order = serializer.save()
        
        # 返回订单详情
        detail_serializer = OrderDetailSerializer(order)
        return Response(detail_serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """高级搜索订单"""
        try:
            serializer = OrderSearchSerializer(data=request.query_params)
            serializer.is_valid(raise_exception=True)
            
            queryset = self.get_queryset()
            filters = Q()
            
            # 构建搜索条件
            if serializer.validated_data.get('order_no'):
                filters &= Q(order_no__icontains=serializer.validated_data['order_no'])
            
            if serializer.validated_data.get('status'):
                filters &= Q(status=serializer.validated_data['status'])
            
            if serializer.validated_data.get('payment_method'):
                filters &= Q(payment_method=serializer.validated_data['payment_method'])
            
            if serializer.validated_data.get('start_date'):
                filters &= Q(created_at__gte=serializer.validated_data['start_date'])
            
            if serializer.validated_data.get('end_date'):
                filters &= Q(created_at__lte=serializer.validated_data['end_date'])
            
            if serializer.validated_data.get('recipient_name'):
                filters &= Q(recipient_name__icontains=serializer.validated_data['recipient_name'])
            
            if serializer.validated_data.get('recipient_phone'):
                filters &= Q(recipient_phone__icontains=serializer.validated_data['recipient_phone'])
            
            # 应用过滤器
            queryset = queryset.filter(filters)
            
            # 分页
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = OrderListSerializer(page, many=True)
                return self.get_paginated_response(serializer.data)
            
            serializer = OrderListSerializer(queryset, many=True)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error in search action: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def my_orders(self, request):
        """获取当前用户的订单"""
        queryset = Order.objects.filter(user=request.user, is_deleted=False).order_by('-created_at')
        
        # 可选状态过滤
        status_filter = request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = OrderListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = OrderListSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """取消订单"""
        order = self.get_object()
        
        # 检查订单状态
        if order.status not in ['pending', 'paid']:
            return Response(
                {'error': '只有待支付或已支付的订单可以取消'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 更新订单状态
        order.status = 'cancelled'
        order.save()
        
        # 记录日志
        OrderLog.objects.create(
            order=order,
            status='cancelled',
            remark='用户取消订单'
        )
        
        return Response({'message': '订单已取消'})

    @action(detail=True, methods=['post'])
    def confirm_receipt(self, request, pk=None):
        """确认收货"""
        order = self.get_object()
        
        # 检查订单状态
        if order.status != 'delivered':
            return Response(
                {'error': '只有已送达的订单可以确认收货'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 更新订单状态
        from django.utils import timezone
        order.status = 'completed'
        order.completed_at = timezone.now()
        order.save()
        
        # 记录日志
        OrderLog.objects.create(
            order=order,
            status='completed',
            remark='用户确认收货，订单完成'
        )
        
        return Response({'message': '订单已完成'})

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """订单统计（管理员专用）"""
        if not request.user.is_staff:
            return Response({'error': '无权限访问'}, status=status.HTTP_403_FORBIDDEN)
        
        # 基本统计
        total_orders = Order.objects.filter(is_deleted=False).count()
        pending_orders = Order.objects.filter(status='pending', is_deleted=False).count()
        paid_orders = Order.objects.filter(status='paid', is_deleted=False).count()
        completed_orders = Order.objects.filter(status='completed', is_deleted=False).count()
        cancelled_orders = Order.objects.filter(status='cancelled', is_deleted=False).count()
        
        # 金额统计
        total_amount = Order.objects.filter(is_deleted=False).aggregate(
            total=Sum('final_amount')
        )['total'] or 0
        
        paid_amount = Order.objects.filter(
            status__in=['paid', 'processing', 'shipped', 'delivered', 'completed'], 
            is_deleted=False
        ).aggregate(total=Sum('final_amount'))['total'] or 0
        
        return Response({
            'total_orders': total_orders,
            'pending_orders': pending_orders,
            'paid_orders': paid_orders,
            'completed_orders': completed_orders,
            'cancelled_orders': cancelled_orders,
            'total_amount': total_amount,
            'paid_amount': paid_amount,
        })


class OrderItemViewSet(viewsets.ReadOnlyModelViewSet):
    """订单商品只读视图集"""
    queryset = OrderItem.objects.select_related('order', 'product')
    serializer_class = OrderItemSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['order']

    def get_queryset(self):
        """根据用户权限过滤"""
        if self.request.user.is_staff:
            return self.queryset
        else:
            return self.queryset.filter(order__user=self.request.user)


class PaymentViewSet(viewsets.ReadOnlyModelViewSet):
    """支付记录只读视图集"""
    queryset = Payment.objects.select_related('order')
    serializer_class = PaymentSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['order', 'payment_method', 'status']
    ordering_fields = ['created_at', 'amount']
    ordering = ['-created_at']

    def get_queryset(self):
        """根据用户权限过滤"""
        if self.request.user.is_staff:
            return self.queryset
        else:
            return self.queryset.filter(order__user=self.request.user)


class OrderLogViewSet(viewsets.ReadOnlyModelViewSet):
    """订单日志只读视图集"""
    queryset = OrderLog.objects.select_related('order')
    serializer_class = OrderLogSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['order', 'action_type']
    ordering_fields = ['created_at']
    ordering = ['created_at']

    def get_queryset(self):
        """根据用户权限过滤"""
        if self.request.user.is_staff:
            return self.queryset
        else:
            return self.queryset.filter(order__user=self.request.user)
