"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Math) {
  common_vendor.unref(Icon)();
}
const Icon = () => "../../components/common/Icon.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "cart",
  setup(__props) {
    const cartItems = common_vendor.ref([
      new UTSJSONObject({
        id: 1,
        product: new UTSJSONObject({
          id: 1,
          name: "夏季清爽T恤 纯棉透气",
          price: 89,
          image: "https://imggw.zmkj.live/products/tshirt1.jpg"
        }),
        spec: "白色 L码",
        quantity: 2,
        selected: true
      }),
      new UTSJSONObject({
        id: 2,
        product: new UTSJSONObject({
          id: 2,
          name: "无线蓝牙耳机 降噪立体声",
          price: 299,
          image: "https://imggw.zmkj.live/products/earphone1.jpg"
        }),
        spec: "黑色",
        quantity: 1,
        selected: true
      }),
      new UTSJSONObject({
        id: 3,
        product: new UTSJSONObject({
          id: 3,
          name: "简约家居摆件 北欧风格",
          price: 59,
          image: "https://imggw.zmkj.live/products/decoration1.jpg"
        }),
        spec: "白色",
        quantity: 1,
        selected: false
      })
    ]);
    const isAllSelected = common_vendor.computed(() => {
      return cartItems.value.length > 0 && cartItems.value.every((item) => {
        return item.selected;
      });
    });
    const selectedCount = common_vendor.computed(() => {
      return cartItems.value.filter((item) => {
        return item.selected;
      }).length;
    });
    const totalAmount = common_vendor.computed(() => {
      return cartItems.value.filter((item) => {
        return item.selected;
      }).reduce((total, item) => {
        return total + item.product.price * item.quantity;
      }, 0);
    });
    common_vendor.onMounted(() => {
      initCart();
    });
    const initCart = () => {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        common_vendor.index.__f__("log", "at pages/cart/cart.uvue:126", "初始化购物车");
      });
    };
    const toggleSelect = (index) => {
      cartItems.value[index].selected = !cartItems.value[index].selected;
    };
    const toggleSelectAll = () => {
      const newSelectState = !isAllSelected.value;
      cartItems.value.forEach((item) => {
        item.selected = newSelectState;
      });
    };
    const decreaseQuantity = (index) => {
      if (cartItems.value[index].quantity > 1) {
        cartItems.value[index].quantity--;
      }
    };
    const increaseQuantity = (index) => {
      cartItems.value[index].quantity++;
    };
    const removeItem = (index) => {
      common_vendor.index.showModal(new UTSJSONObject({
        title: "提示",
        content: "确定要删除这个商品吗？",
        success: (res) => {
          if (res.confirm) {
            cartItems.value.splice(index, 1);
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
          }
        }
      }));
    };
    const goShopping = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const handleCheckout = () => {
      if (selectedCount.value === 0) {
        common_vendor.index.showToast({
          title: "请选择要结算的商品",
          icon: "none"
        });
        return null;
      }
      common_vendor.index.navigateTo({
        url: "/pages/order/checkout"
      });
    };
    return (_ctx = null, _cache = null) => {
      const __returned__ = common_vendor.e(new UTSJSONObject({
        a: cartItems.value.length > 0
      }), cartItems.value.length > 0 ? new UTSJSONObject({
        b: common_vendor.f(cartItems.value, (item = null, index = null, i0 = null) => {
          return common_vendor.e(new UTSJSONObject({
            a: "70b3dcf8-0-" + i0,
            b: common_vendor.p(new UTSJSONObject({
              name: item.selected ? "checkbox-checked" : "checkbox",
              size: "20",
              color: item.selected ? "#007aff" : "#ccc"
            })),
            c: common_vendor.o(($event = null) => {
              return toggleSelect(index);
            }, index),
            d: item.product.image,
            e: common_vendor.t(item.product.name),
            f: item.spec
          }), item.spec ? new UTSJSONObject({
            g: common_vendor.t(item.spec)
          }) : new UTSJSONObject({}), new UTSJSONObject({
            h: common_vendor.t(item.product.price),
            i: "70b3dcf8-1-" + i0,
            j: common_vendor.o(($event = null) => {
              return decreaseQuantity(index);
            }, index),
            k: common_vendor.t(item.quantity),
            l: "70b3dcf8-2-" + i0,
            m: common_vendor.o(($event = null) => {
              return increaseQuantity(index);
            }, index),
            n: "70b3dcf8-3-" + i0,
            o: common_vendor.o(($event = null) => {
              return removeItem(index);
            }, index),
            p: index
          }));
        }),
        c: common_vendor.p(new UTSJSONObject({
          name: "minus",
          size: "14",
          color: "#999"
        })),
        d: common_vendor.p(new UTSJSONObject({
          name: "plus",
          size: "14",
          color: "#999"
        })),
        e: common_vendor.p(new UTSJSONObject({
          name: "delete",
          size: "16",
          color: "#ff3b30"
        }))
      }) : new UTSJSONObject({
        f: common_vendor.p(new UTSJSONObject({
          name: "cart-empty",
          size: "80",
          color: "#ccc"
        })),
        g: common_vendor.o(goShopping)
      }), new UTSJSONObject({
        h: cartItems.value.length > 0
      }), cartItems.value.length > 0 ? new UTSJSONObject({
        i: common_vendor.p(new UTSJSONObject({
          name: isAllSelected.value ? "checkbox-checked" : "checkbox",
          size: "20",
          color: isAllSelected.value ? "#007aff" : "#ccc"
        })),
        j: common_vendor.o(toggleSelectAll),
        k: common_vendor.t(totalAmount.value.toFixed(2)),
        l: common_vendor.t(selectedCount.value),
        m: selectedCount.value === 0 ? 1 : "",
        n: common_vendor.o(handleCheckout)
      }) : new UTSJSONObject({}), new UTSJSONObject({
        o: common_vendor.sei(common_vendor.gei(_ctx, ""), "view")
      }));
      return __returned__;
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-70b3dcf8"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/cart/cart.js.map
