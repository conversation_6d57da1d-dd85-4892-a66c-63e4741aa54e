from rest_framework import permissions

class IsAdminUser(permissions.BasePermission):
    """
    仅允许管理员用户访问
    """
    def has_permission(self, request, view):
        return request.user and request.user.is_staff

class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    对象级权限，允许对象的所有者编辑，其他用户只能查看
    """
    def has_object_permission(self, request, view, obj):
        # 读权限允许任何请求
        if request.method in permissions.SAFE_METHODS:
            return True

        # 写权限只允许对象的所有者
        return obj.owner == request.user

class IsSuperUser(permissions.BasePermission):
    """
    仅允许超级用户访问
    """
    def has_permission(self, request, view):
        return request.user and request.user.is_superuser

class HasAPIKey(permissions.BasePermission):
    """
    API密钥验证权限
    """
    def has_permission(self, request, view):
        api_key = request.META.get('HTTP_X_API_KEY')
        if not api_key:
            return False
        # 在实际应用中，这里应该验证API密钥的有效性
        # 例如从环境变量或数据库中获取有效密钥进行比对
        from django.conf import settings
        return api_key == settings.API_SECRET_KEY

class ActionBasedPermission(permissions.BasePermission):
    """
    基于动作的动态权限控制
    视图需定义action_permissions属性，格式为：
    action_permissions = {
        'list': [permissions.AllowAny()],
        'create': [permissions.IsAuthenticated()],
        'retrieve': [permissions.IsAuthenticated()],
        'update': [IsOwnerOrReadOnly()],
        'partial_update': [IsOwnerOrReadOnly()],
        'destroy': [IsOwnerOrReadOnly()|IsAdminUser()],
    }
    """
    def has_permission(self, request, view):
        if not hasattr(view, 'action_permissions'):
            return False
        
        action_permissions = view.action_permissions.get(view.action, [])
        for permission in action_permissions:
            if permission.has_permission(request, view):
                return True
        return False

    def has_object_permission(self, request, view, obj):
        if not hasattr(view, 'action_permissions'):
            return False
        
        action_permissions = view.action_permissions.get(view.action, [])
        for permission in action_permissions:
            if permission.has_object_permission(request, view, obj):
                return True
        return False