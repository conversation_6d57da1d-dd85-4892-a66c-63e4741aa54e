/**
 * 数据验证工具
 * 提供各种数据验证功能
 */

import { REGEX_PATTERNS } from './constants'

// 验证结果接口
export interface ValidationResult {
  valid: boolean
  message?: string
}

/**
 * 验证手机号
 */
export function validatePhone(phone: string): ValidationResult {
  if (!phone) {
    return { valid: false, message: '请输入手机号' }
  }
  
  if (!REGEX_PATTERNS.PHONE.test(phone)) {
    return { valid: false, message: '请输入正确的手机号' }
  }
  
  return { valid: true }
}

/**
 * 验证邮箱
 */
export function validateEmail(email: string): ValidationResult {
  if (!email) {
    return { valid: false, message: '请输入邮箱地址' }
  }
  
  if (!REGEX_PATTERNS.EMAIL.test(email)) {
    return { valid: false, message: '请输入正确的邮箱地址' }
  }
  
  return { valid: true }
}

/**
 * 验证密码
 */
export function validatePassword(password: string): ValidationResult {
  if (!password) {
    return { valid: false, message: '请输入密码' }
  }
  
  if (password.length < 6) {
    return { valid: false, message: '密码长度不能少于6位' }
  }
  
  if (password.length > 20) {
    return { valid: false, message: '密码长度不能超过20位' }
  }
  
  if (!REGEX_PATTERNS.PASSWORD.test(password)) {
    return { valid: false, message: '密码必须包含字母和数字' }
  }
  
  return { valid: true }
}

/**
 * 验证身份证号
 */
export function validateIdCard(idCard: string): ValidationResult {
  if (!idCard) {
    return { valid: false, message: '请输入身份证号' }
  }
  
  if (!REGEX_PATTERNS.ID_CARD.test(idCard)) {
    return { valid: false, message: '请输入正确的身份证号' }
  }
  
  return { valid: true }
}

/**
 * 验证中文姓名
 */
export function validateChineseName(name: string): ValidationResult {
  if (!name) {
    return { valid: false, message: '请输入姓名' }
  }
  
  if (!REGEX_PATTERNS.CHINESE_NAME.test(name)) {
    return { valid: false, message: '请输入正确的中文姓名' }
  }
  
  return { valid: true }
}

/**
 * 验证验证码
 */
export function validateSmsCode(code: string): ValidationResult {
  if (!code) {
    return { valid: false, message: '请输入验证码' }
  }
  
  if (!/^\d{4,6}$/.test(code)) {
    return { valid: false, message: '请输入正确的验证码' }
  }
  
  return { valid: true }
}

/**
 * 验证金额
 */
export function validateAmount(amount: string | number): ValidationResult {
  if (!amount && amount !== 0) {
    return { valid: false, message: '请输入金额' }
  }
  
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  
  if (isNaN(numAmount)) {
    return { valid: false, message: '请输入正确的金额' }
  }
  
  if (numAmount < 0) {
    return { valid: false, message: '金额不能为负数' }
  }
  
  if (numAmount > 999999.99) {
    return { valid: false, message: '金额不能超过999999.99' }
  }
  
  // 检查小数位数
  const decimalPlaces = (numAmount.toString().split('.')[1] || '').length
  if (decimalPlaces > 2) {
    return { valid: false, message: '金额最多保留两位小数' }
  }
  
  return { valid: true }
}

/**
 * 验证数量
 */
export function validateQuantity(quantity: string | number): ValidationResult {
  if (!quantity && quantity !== 0) {
    return { valid: false, message: '请输入数量' }
  }
  
  const numQuantity = typeof quantity === 'string' ? parseInt(quantity) : quantity
  
  if (isNaN(numQuantity)) {
    return { valid: false, message: '请输入正确的数量' }
  }
  
  if (numQuantity <= 0) {
    return { valid: false, message: '数量必须大于0' }
  }
  
  if (numQuantity > 9999) {
    return { valid: false, message: '数量不能超过9999' }
  }
  
  return { valid: true }
}

/**
 * 验证URL
 */
export function validateUrl(url: string): ValidationResult {
  if (!url) {
    return { valid: false, message: '请输入URL' }
  }
  
  if (!REGEX_PATTERNS.URL.test(url)) {
    return { valid: false, message: '请输入正确的URL' }
  }
  
  return { valid: true }
}

/**
 * 验证银行卡号
 */
export function validateBankCard(cardNumber: string): ValidationResult {
  if (!cardNumber) {
    return { valid: false, message: '请输入银行卡号' }
  }
  
  // 移除空格
  const cleanCardNumber = cardNumber.replace(/\s/g, '')
  
  if (!/^\d{16,19}$/.test(cleanCardNumber)) {
    return { valid: false, message: '请输入正确的银行卡号' }
  }
  
  // Luhn算法验证
  let sum = 0
  let isEven = false
  
  for (let i = cleanCardNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cleanCardNumber.charAt(i))
    
    if (isEven) {
      digit *= 2
      if (digit > 9) {
        digit -= 9
      }
    }
    
    sum += digit
    isEven = !isEven
  }
  
  if (sum % 10 !== 0) {
    return { valid: false, message: '请输入正确的银行卡号' }
  }
  
  return { valid: true }
}

/**
 * 验证表单数据
 */
export function validateForm(data: Record<string, any>, rules: Record<string, any>): {
  valid: boolean
  errors: Record<string, string>
} {
  const errors: Record<string, string> = {}
  
  for (const field in rules) {
    const rule = rules[field]
    const value = data[field]
    
    // 必填验证
    if (rule.required && (!value || (typeof value === 'string' && !value.trim()))) {
      errors[field] = rule.message || `${field}不能为空`
      continue
    }
    
    // 如果不是必填且值为空，跳过其他验证
    if (!rule.required && (!value || (typeof value === 'string' && !value.trim()))) {
      continue
    }
    
    // 类型验证
    if (rule.type) {
      let result: ValidationResult
      
      switch (rule.type) {
        case 'phone':
          result = validatePhone(value)
          break
        case 'email':
          result = validateEmail(value)
          break
        case 'password':
          result = validatePassword(value)
          break
        case 'idCard':
          result = validateIdCard(value)
          break
        case 'chineseName':
          result = validateChineseName(value)
          break
        case 'smsCode':
          result = validateSmsCode(value)
          break
        case 'amount':
          result = validateAmount(value)
          break
        case 'quantity':
          result = validateQuantity(value)
          break
        case 'url':
          result = validateUrl(value)
          break
        case 'bankCard':
          result = validateBankCard(value)
          break
        default:
          result = { valid: true }
      }
      
      if (!result.valid) {
        errors[field] = result.message || `${field}格式不正确`
        continue
      }
    }
    
    // 长度验证
    if (rule.minLength && value.length < rule.minLength) {
      errors[field] = `${field}长度不能少于${rule.minLength}位`
      continue
    }
    
    if (rule.maxLength && value.length > rule.maxLength) {
      errors[field] = `${field}长度不能超过${rule.maxLength}位`
      continue
    }
    
    // 数值范围验证
    if (rule.min !== undefined && parseFloat(value) < rule.min) {
      errors[field] = `${field}不能小于${rule.min}`
      continue
    }
    
    if (rule.max !== undefined && parseFloat(value) > rule.max) {
      errors[field] = `${field}不能大于${rule.max}`
      continue
    }
    
    // 自定义验证函数
    if (rule.validator && typeof rule.validator === 'function') {
      const customResult = rule.validator(value, data)
      if (!customResult.valid) {
        errors[field] = customResult.message || `${field}验证失败`
        continue
      }
    }
  }
  
  return {
    valid: Object.keys(errors).length === 0,
    errors
  }
}

/**
 * 验证文件类型
 */
export function validateFileType(fileName: string, allowedTypes: string[]): ValidationResult {
  if (!fileName) {
    return { valid: false, message: '请选择文件' }
  }
  
  const extension = fileName.split('.').pop()?.toLowerCase()
  
  if (!extension || !allowedTypes.includes(extension)) {
    return { 
      valid: false, 
      message: `只支持${allowedTypes.join('、')}格式的文件` 
    }
  }
  
  return { valid: true }
}

/**
 * 验证文件大小
 */
export function validateFileSize(fileSize: number, maxSize: number): ValidationResult {
  if (fileSize > maxSize) {
    const maxSizeMB = (maxSize / 1024 / 1024).toFixed(1)
    return { 
      valid: false, 
      message: `文件大小不能超过${maxSizeMB}MB` 
    }
  }
  
  return { valid: true }
}

export default {
  validatePhone,
  validateEmail,
  validatePassword,
  validateIdCard,
  validateChineseName,
  validateSmsCode,
  validateAmount,
  validateQuantity,
  validateUrl,
  validateBankCard,
  validateForm,
  validateFileType,
  validateFileSize
}
