# Generated by Django 5.2.4 on 2025-07-25 09:19

import django.contrib.auth.models
import django.contrib.auth.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('phone', models.CharField(blank=True, help_text='手机号码', max_length=11, null=True, unique=True)),
                ('avatar', models.ImageField(blank=True, help_text='用户头像', null=True, upload_to='avatars/')),
                ('nickname', models.CharField(blank=True, help_text='用户昵称', max_length=50, null=True)),
                ('gender', models.CharField(blank=True, choices=[('male', '男'), ('female', '女'), ('other', '其他')], help_text='性别', max_length=10, null=True)),
                ('birth_date', models.DateField(blank=True, help_text='出生日期', null=True)),
                ('is_verified', models.BooleanField(default=False, help_text='是否已验证')),
                ('wechat_openid', models.CharField(blank=True, help_text='微信OpenID', max_length=100, null=True, unique=True)),
                ('wechat_unionid', models.CharField(blank=True, help_text='微信UnionID', max_length=100, null=True, unique=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': '用户',
                'verbose_name_plural': '用户',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='UserAddress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('recipient_name', models.CharField(help_text='收件人姓名', max_length=50)),
                ('recipient_phone', models.CharField(help_text='收件人电话', max_length=11)),
                ('province', models.CharField(help_text='省份', max_length=20)),
                ('city', models.CharField(help_text='城市', max_length=20)),
                ('district', models.CharField(help_text='区/县', max_length=20)),
                ('detailed_address', models.CharField(help_text='详细地址', max_length=200)),
                ('postal_code', models.CharField(blank=True, help_text='邮政编码', max_length=6, null=True)),
                ('is_default', models.BooleanField(default=False, help_text='是否默认地址')),
                ('user', models.ForeignKey(help_text='关联用户', on_delete=django.db.models.deletion.CASCADE, related_name='addresses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '用户地址',
                'verbose_name_plural': '用户地址',
            },
        ),
        migrations.CreateModel(
            name='UserLoginLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('ip_address', models.GenericIPAddressField(help_text='登录IP地址')),
                ('login_time', models.DateTimeField(auto_now_add=True, help_text='登录时间')),
                ('logout_time', models.DateTimeField(blank=True, help_text='登出时间', null=True)),
                ('device_info', models.CharField(blank=True, help_text='设备信息', max_length=200, null=True)),
                ('browser_info', models.CharField(blank=True, help_text='浏览器信息', max_length=200, null=True)),
                ('user', models.ForeignKey(help_text='关联用户', on_delete=django.db.models.deletion.CASCADE, related_name='login_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '用户登录日志',
                'verbose_name_plural': '用户登录日志',
            },
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['phone'], name='users_user_phone_9474e8_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['wechat_openid'], name='users_user_wechat__640083_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['wechat_unionid'], name='users_user_wechat__976edb_idx'),
        ),
        migrations.AddIndex(
            model_name='useraddress',
            index=models.Index(fields=['user', 'is_default'], name='users_usera_user_id_b870fc_idx'),
        ),
        migrations.AddIndex(
            model_name='userloginlog',
            index=models.Index(fields=['user', '-login_time'], name='users_userl_user_id_d9a1a3_idx'),
        ),
        migrations.AddIndex(
            model_name='userloginlog',
            index=models.Index(fields=['ip_address'], name='users_userl_ip_addr_5ba078_idx'),
        ),
    ]
