from rest_framework import serializers
from .models import Banner


class BannerSerializer(serializers.ModelSerializer):
    """轮播图序列化器"""
    
    class Meta:
        model = Banner
        fields = [
            'id', 'title', 'image', 'link_type', 'link_value', 
            'position', 'sort_order', 'is_active', 'start_time', 
            'end_time', 'click_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'click_count', 'created_at', 'updated_at']
    
    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)
        
        # 添加图片完整URL
        if instance.image:
            request = self.context.get('request')
            if request:
                data['image'] = request.build_absolute_uri(instance.image.url)
        
        # 添加时间状态
        data['time_status'] = instance.is_valid_time()
        
        return data


class BannerListSerializer(serializers.ModelSerializer):
    """轮播图列表序列化器（简化版）"""
    
    class Meta:
        model = Banner
        fields = [
            'id', 'title', 'image', 'link_type', 'link_value', 'sort_order'
        ]
    
    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)
        
        # 添加图片完整URL
        if instance.image:
            request = self.context.get('request')
            if request:
                data['image'] = request.build_absolute_uri(instance.image.url)
        
        return data
