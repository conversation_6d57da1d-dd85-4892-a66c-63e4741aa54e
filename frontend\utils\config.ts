/**
 * 环境配置管理
 * 统一管理不同环境的域名和配置
 */

// 环境配置接口
export interface EnvConfig {
  API_BASE_URL: string      // API基础地址
  CDN_BASE_URL: string      // CDN基础地址
  WS_BASE_URL?: string      // WebSocket地址(如需要)
  APP_NAME: string          // 应用名称
  VERSION: string           // 版本号
}

// 域名配置
const MAIN_DOMAIN = 'zmkj.nat100.top'  // 主域名
const CDN_DOMAIN = 'zmkj.nat100.top'   // 图片域名

// 统一配置
const UNIFIED_CONFIG: EnvConfig = {
  API_BASE_URL: `http://${MAIN_DOMAIN}/api/v1`,
  CDN_BASE_URL: `http://${CDN_DOMAIN}`,
  WS_BASE_URL: `ws://${MAIN_DOMAIN}/ws`,
  APP_NAME: '智梦科技系统',
  VERSION: '1.0.0'
}

// 开发环境配置
const DEV_CONFIG: EnvConfig = UNIFIED_CONFIG

// 生产环境配置
const PROD_CONFIG: EnvConfig = UNIFIED_CONFIG

// 测试环境配置
const TEST_CONFIG: EnvConfig = UNIFIED_CONFIG

/**
 * 获取当前环境
 */
export function getEnv(): 'development' | 'production' | 'test' {
  // #ifdef MP-WEIXIN
  // 微信小程序环境判断
  const accountInfo = uni.getAccountInfoSync()
  const envVersion = accountInfo.miniProgram.envVersion
  
  if (envVersion === 'release') {
    return 'production'
  } else if (envVersion === 'trial') {
    return 'test'
  } else {
    return 'development'
  }
  // #endif
  
  // #ifdef H5
  // H5环境判断
  const hostname = window.location.hostname
  if (hostname.includes('zmkj.live')) {
    return 'production'
  } else if (hostname.includes('test')) {
    return 'test'
  } else {
    return 'development'
  }
  // #endif
  
  // #ifdef APP-PLUS
  // App环境判断
  // 可以通过打包时的环境变量或其他方式判断
  return process.env.NODE_ENV === 'production' ? 'production' : 'development'
  // #endif
  
  // 默认开发环境
  return 'development'
}

/**
 * 获取当前环境配置
 */
export function getConfig(): EnvConfig {
  const env = getEnv()
  
  switch (env) {
    case 'production':
      return PROD_CONFIG
    case 'test':
      return TEST_CONFIG
    case 'development':
    default:
      return DEV_CONFIG
  }
}

// 导出当前环境配置
export const CONFIG = getConfig()

/**
 * 获取完整的API地址
 * @param path API路径
 * @returns 完整的API地址
 */
export function getApiUrl(path: string): string {
  // 确保path以/开头
  if (!path.startsWith('/')) {
    path = '/' + path
  }
  
  return CONFIG.API_BASE_URL + path
}

/**
 * 获取完整的CDN地址
 * @param path 资源路径
 * @returns 完整的CDN地址
 */
export function getCdnUrl(path: string): string {
  if (!path) return ''
  
  // 如果已经是完整URL，直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path
  }
  
  // 确保path以/开头
  if (!path.startsWith('/')) {
    path = '/' + path
  }
  
  return CONFIG.CDN_BASE_URL + path
}

/**
 * 获取WebSocket地址
 * @param path WebSocket路径
 * @returns 完整的WebSocket地址
 */
export function getWsUrl(path: string = ''): string {
  if (!CONFIG.WS_BASE_URL) {
    console.warn('WebSocket URL not configured')
    return ''
  }
  
  // 确保path以/开头
  if (path && !path.startsWith('/')) {
    path = '/' + path
  }
  
  return CONFIG.WS_BASE_URL + path
}

/**
 * 是否为开发环境
 */
export function isDev(): boolean {
  return getEnv() === 'development'
}

/**
 * 是否为生产环境
 */
export function isProd(): boolean {
  return getEnv() === 'production'
}

/**
 * 是否为测试环境
 */
export function isTest(): boolean {
  return getEnv() === 'test'
}

/**
 * 打印当前环境信息
 */
export function printEnvInfo(): void {
  const env = getEnv()
  const config = getConfig()
  
  console.log('=== 环境信息 ===')
  console.log('当前环境:', env)
  console.log('应用名称:', config.APP_NAME)
  console.log('版本号:', config.VERSION)
  console.log('API地址:', config.API_BASE_URL)
  console.log('CDN地址:', config.CDN_BASE_URL)
  console.log('WebSocket地址:', config.WS_BASE_URL)
  console.log('===============')
}

// 开发环境下打印环境信息
if (isDev()) {
  printEnvInfo()
}

export default {
  CONFIG,
  getConfig,
  getApiUrl,
  getCdnUrl,
  getWsUrl,
  isDev,
  isProd,
  isTest,
  printEnvInfo
}
