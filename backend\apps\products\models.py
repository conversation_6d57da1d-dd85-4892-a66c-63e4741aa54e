from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal
from apps.core.models import BaseModel
from apps.users.models import User


class ProductCategory(BaseModel):
    """产品分类模型"""
    name = models.CharField(verbose_name='分类名称', max_length=100, help_text='产品分类名称')
    description = models.TextField(verbose_name='分类描述', blank=True, help_text='分类详细描述')
    parent = models.ForeignKey(
        'self', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True, 
        related_name='children',
        verbose_name='父分类',
        help_text='上级分类'
    )
    sort_order = models.IntegerField(verbose_name='排序', default=0, help_text='数字越小越靠前')
    is_active = models.BooleanField(verbose_name='是否启用', default=True, help_text='是否在前台显示')
    icon = models.CharField(verbose_name='图标', max_length=50, blank=True, help_text='分类图标(emoji)')
    
    class Meta:
        verbose_name = '产品分类'
        verbose_name_plural = verbose_name
        ordering = ['sort_order', 'id']
        indexes = [
            models.Index(fields=['parent', 'is_active']),
            models.Index(fields=['sort_order']),
        ]
    
    def __str__(self):
        return self.name
    
    def get_full_name(self):
        """获取完整分类路径"""
        if self.parent:
            return f"{self.parent.get_full_name()} > {self.name}"
        return self.name


class Product(BaseModel):
    """产品模型"""
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('active', '上架'),
        ('inactive', '下架'),
        ('soldout', '售罄'),
    ]
    
    name = models.CharField(verbose_name='产品名称', max_length=200, help_text='产品标题')
    subtitle = models.CharField(verbose_name='副标题', max_length=255, blank=True, help_text='产品副标题')
    description = models.TextField(verbose_name='产品描述', help_text='产品详细描述')
    category = models.ForeignKey(
        ProductCategory, 
        on_delete=models.PROTECT, 
        related_name='products',
        verbose_name='产品分类',
        help_text='所属分类'
    )
    sku = models.CharField(verbose_name='商品编码', max_length=100, unique=True, help_text='商品唯一编码')
    price = models.DecimalField(
        verbose_name='价格', 
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text='销售价格'
    )
    original_price = models.DecimalField(
        verbose_name='原价', 
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        blank=True,
        null=True,
        help_text='划线价格'
    )
    cost_price = models.DecimalField(
        verbose_name='成本价', 
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        blank=True,
        null=True,
        help_text='采购成本'
    )
    stock = models.IntegerField(
        verbose_name='库存数量', 
        default=0,
        validators=[MinValueValidator(0)],
        help_text='当前库存'
    )
    min_stock = models.IntegerField(
        verbose_name='最低库存', 
        default=0,
        validators=[MinValueValidator(0)],
        help_text='库存预警线'
    )
    weight = models.DecimalField(
        verbose_name='重量(kg)', 
        max_digits=8, 
        decimal_places=3,
        blank=True,
        null=True,
        help_text='商品重量'
    )
    status = models.CharField(
        verbose_name='商品状态', 
        max_length=20, 
        choices=STATUS_CHOICES, 
        default='draft',
        help_text='商品当前状态'
    )
    is_featured = models.BooleanField(verbose_name='是否推荐', default=False, help_text='首页推荐商品')
    sort_order = models.IntegerField(verbose_name='排序', default=0, help_text='数字越小越靠前')
    view_count = models.IntegerField(verbose_name='浏览次数', default=0, help_text='商品浏览统计')
    sale_count = models.IntegerField(verbose_name='销售数量', default=0, help_text='累计销售数量')
    tags = models.CharField(verbose_name='标签', max_length=500, blank=True, help_text='商品标签，逗号分隔')
    seo_title = models.CharField(verbose_name='SEO标题', max_length=255, blank=True, help_text='搜索引擎标题')
    seo_keywords = models.CharField(verbose_name='SEO关键词', max_length=255, blank=True, help_text='搜索关键词')
    seo_description = models.TextField(verbose_name='SEO描述', blank=True, help_text='搜索引擎描述')
    
    class Meta:
        verbose_name = '产品'
        verbose_name_plural = verbose_name
        ordering = ['-is_featured', 'sort_order', '-created_at']
        indexes = [
            models.Index(fields=['category', 'status']),
            models.Index(fields=['status', 'is_featured']),
            models.Index(fields=['sku']),
            models.Index(fields=['sort_order']),
        ]
    
    def __str__(self):
        return self.name
    
    def is_in_stock(self):
        """检查是否有库存"""
        return self.stock > 0
    
    def is_low_stock(self):
        """检查是否库存不足"""
        return self.stock <= self.min_stock
    
    def get_discount_rate(self):
        """计算折扣率"""
        if self.original_price and self.original_price > self.price:
            return int((1 - self.price / self.original_price) * 100)
        return 0


class ProductImage(BaseModel):
    """产品图片模型"""
    product = models.ForeignKey(
        Product, 
        on_delete=models.CASCADE, 
        related_name='images',
        verbose_name='关联产品',
        help_text='所属产品'
    )
    image = models.ImageField(
        verbose_name='产品图片', 
        upload_to='products/%Y/%m/%d/',
        help_text='产品展示图片'
    )
    alt_text = models.CharField(verbose_name='图片描述', max_length=200, blank=True, help_text='图片替代文字')
    is_primary = models.BooleanField(verbose_name='是否主图', default=False, help_text='产品主要展示图片')
    sort_order = models.IntegerField(verbose_name='排序', default=0, help_text='显示顺序')
    
    class Meta:
        verbose_name = '产品图片'
        verbose_name_plural = verbose_name
        ordering = ['-is_primary', 'sort_order', 'id']
        indexes = [
            models.Index(fields=['product', 'is_primary']),
        ]
    
    def __str__(self):
        return f"{self.product.name} - 图片{self.id}"


class ProductAttribute(BaseModel):
    """产品属性模型"""
    ATTRIBUTE_TYPES = [
        ('text', '文本'),
        ('number', '数字'),
        ('boolean', '布尔值'),
        ('choice', '选择'),
    ]
    
    name = models.CharField(verbose_name='属性名称', max_length=100, help_text='属性标识名')
    display_name = models.CharField(verbose_name='显示名称', max_length=100, help_text='前台显示名称')
    attribute_type = models.CharField(
        verbose_name='属性类型', 
        max_length=20, 
        choices=ATTRIBUTE_TYPES,
        default='text',
        help_text='属性值类型'
    )
    choices = models.TextField(
        verbose_name='选择项', 
        blank=True, 
        help_text='选择类型的可选项，一行一个'
    )
    is_required = models.BooleanField(verbose_name='是否必填', default=False, help_text='前台是否必须填写')
    sort_order = models.IntegerField(verbose_name='排序', default=0, help_text='显示顺序')
    
    class Meta:
        verbose_name = '产品属性'
        verbose_name_plural = verbose_name
        ordering = ['sort_order', 'id']
    
    def __str__(self):
        return self.display_name


class ProductAttributeValue(BaseModel):
    """产品属性值模型"""
    product = models.ForeignKey(
        Product, 
        on_delete=models.CASCADE, 
        related_name='attribute_values',
        verbose_name='关联产品'
    )
    attribute = models.ForeignKey(
        ProductAttribute, 
        on_delete=models.CASCADE,
        verbose_name='产品属性'
    )
    value = models.TextField(verbose_name='属性值', help_text='具体的属性值')
    
    class Meta:
        verbose_name = '产品属性值'
        verbose_name_plural = verbose_name
        unique_together = ['product', 'attribute']
        indexes = [
            models.Index(fields=['product', 'attribute']),
        ]
    
    def __str__(self):
        return f"{self.product.name} - {self.attribute.display_name}: {self.value}"


class ProductReview(BaseModel):
    """产品评价模型"""
    RATING_CHOICES = [
        (1, '⭐'),
        (2, '⭐⭐'),
        (3, '⭐⭐⭐'),
        (4, '⭐⭐⭐⭐'),
        (5, '⭐⭐⭐⭐⭐'),
    ]
    
    product = models.ForeignKey(
        Product, 
        on_delete=models.CASCADE, 
        related_name='reviews',
        verbose_name='关联产品'
    )
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        verbose_name='评价用户'
    )
    rating = models.IntegerField(
        verbose_name='评分', 
        choices=RATING_CHOICES,
        help_text='1-5星评分'
    )
    title = models.CharField(verbose_name='评价标题', max_length=200, blank=True, help_text='评价标题')
    content = models.TextField(verbose_name='评价内容', help_text='详细评价内容')
    is_verified_purchase = models.BooleanField(
        verbose_name='已验证购买', 
        default=False, 
        help_text='是否为实际购买用户的评价'
    )
    is_approved = models.BooleanField(verbose_name='已审核', default=True, help_text='是否通过审核')
    helpful_count = models.IntegerField(verbose_name='有用数', default=0, help_text='认为有用的用户数')
    
    class Meta:
        verbose_name = '产品评价'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        unique_together = ['product', 'user']
        indexes = [
            models.Index(fields=['product', 'is_approved']),
            models.Index(fields=['user']),
        ]
    
    def __str__(self):
        return f"{self.user.username}对{self.product.name}的评价"
