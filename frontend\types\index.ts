/**
 * 全局类型定义文件
 * 定义API接口、用户、产品、订单和分销相关的TypeScript类型
 */

// ==================== 基础类型 ====================

/** 通用响应结构 */
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
  timestamp: number
}

/** 分页参数 */
export interface PaginationParams {
  page: number
  page_size: number
  search?: string
  ordering?: string
}

/** 分页响应 */
export interface PaginationResponse<T> {
  count: number
  next: string | null
  previous: string | null
  results: T[]
}

// ==================== 用户相关类型 ====================

/** 用户信息 */
export interface UserInfo {
  id: number
  username: string
  nickname?: string
  avatar?: string
  phone?: string
  email?: string
  gender: 'M' | 'F' | 'U'
  birthday?: string
  is_active: boolean
  is_distributor: boolean
  distributor_level: number
  created_at: string
  updated_at: string
}

/** 微信用户信息 */
export interface WechatUserInfo {
  openid: string
  unionid?: string
  nickname: string
  avatar: string
  gender: number
  country: string
  province: string
  city: string
}

/** 登录请求 */
export interface LoginRequest {
  code?: string // 微信登录code
  phone?: string // 手机号登录
  password?: string // 密码登录
  captcha?: string // 验证码
}

/** 登录响应 */
export interface LoginResponse {
  access_token: string
  refresh_token: string
  expires_in: number
  user: UserInfo
}

// ==================== 产品相关类型 ====================

/** 产品分类 */
export interface ProductCategory {
  id: number
  name: string
  icon: string
  image: string
  sort_order: number
  is_active: boolean
  parent_id?: number
  children?: ProductCategory[]
}

/** 产品规格 */
export interface ProductSpec {
  id: number
  name: string
  values: ProductSpecValue[]
}

export interface ProductSpecValue {
  id: number
  value: string
  image?: string
}

/** 产品SKU */
export interface ProductSku {
  id: number
  product_id: number
  sku_code: string
  price: number
  original_price: number
  stock: number
  specs: { [key: string]: string }
  image?: string
  is_active: boolean
}

/** 产品信息 */
export interface Product {
  id: number
  name: string
  description: string
  content: string
  category_id: number
  category: ProductCategory
  images: string[]
  price: number
  original_price: number
  stock: number
  sales: number
  rating: number
  rating_count: number
  is_hot: boolean
  is_new: boolean
  is_recommend: boolean
  is_active: boolean
  specs: ProductSpec[]
  skus: ProductSku[]
  tags: string[]
  created_at: string
  updated_at: string
}

/** 产品搜索参数 */
export interface ProductSearchParams extends PaginationParams {
  category_id?: number
  min_price?: number
  max_price?: number
  is_hot?: boolean
  is_new?: boolean
  is_recommend?: boolean
  tags?: string[]
}

// ==================== 购物车相关类型 ====================

/** 购物车商品 */
export interface CartItem {
  id: number
  product_id: number
  product: Product
  sku_id?: number
  sku?: ProductSku
  quantity: number
  selected: boolean
  spec?: string
  created_at: string
  updated_at: string
}

/** 添加到购物车请求 */
export interface AddToCartRequest {
  product_id: number
  sku_id?: number
  quantity: number
  spec?: string
}

// ==================== 订单相关类型 ====================

/** 订单状态 */
export type OrderStatus = 'pending' | 'paid' | 'shipped' | 'delivered' | 'completed' | 'cancelled' | 'refunded'

/** 支付状态 */
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded'

/** 订单商品 */
export interface OrderItem {
  id: number
  product_id: number
  product: Product
  sku_id?: number
  sku?: ProductSku
  quantity: number
  price: number
  total_price: number
  spec?: string
}

/** 收货地址 */
export interface Address {
  id: number
  name: string
  phone: string
  province: string
  city: string
  district: string
  detail: string
  postal_code?: string
  is_default: boolean
  created_at: string
  updated_at: string
}

/** 订单信息 */
export interface Order {
  id: number
  order_no: string
  user_id: number
  status: OrderStatus
  payment_status: PaymentStatus
  items: OrderItem[]
  address: Address
  total_amount: number
  shipping_fee: number
  discount_amount: number
  final_amount: number
  payment_method?: string
  payment_time?: string
  shipping_time?: string
  delivery_time?: string
  completion_time?: string
  cancellation_reason?: string
  remark?: string
  created_at: string
  updated_at: string
}

/** 创建订单请求 */
export interface CreateOrderRequest {
  items: Array<{
    product_id: number
    sku_id?: number
    quantity: number
    spec?: string
  }>
  address_id: number
  payment_method: string
  remark?: string
  coupon_id?: number
}

// ==================== 分销相关类型 ====================

/** 分销商等级 */
export type DistributorLevel = 1 | 2 | 3

/** 佣金状态 */
export type CommissionStatus = 'pending' | 'confirmed' | 'paid' | 'cancelled'

/** 分销商信息 */
export interface DistributorInfo {
  id: number
  user_id: number
  user: UserInfo
  level: DistributorLevel
  parent_id?: number
  parent?: DistributorInfo
  total_commission: number
  available_commission: number
  withdrawn_commission: number
  team_count: number
  direct_count: number
  total_sales: number
  this_month_sales: number
  status: 'active' | 'inactive' | 'suspended'
  created_at: string
  updated_at: string
}

/** 佣金记录 */
export interface CommissionRecord {
  id: number
  distributor_id: number
  distributor: DistributorInfo
  order_id: number
  order: Order
  type: 'direct' | 'indirect'
  level: number
  amount: number
  rate: number
  status: CommissionStatus
  confirmed_at?: string
  paid_at?: string
  created_at: string
  updated_at: string
}

/** 提现记录 */
export interface WithdrawRecord {
  id: number
  distributor_id: number
  distributor: DistributorInfo
  amount: number
  fee: number
  actual_amount: number
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  bank_info?: {
    bank_name: string
    account_name: string
    account_number: string
  }
  alipay_info?: {
    account: string
    name: string
  }
  wechat_info?: {
    openid: string
    name: string
  }
  remark?: string
  processed_at?: string
  created_at: string
  updated_at: string
}

/** 团队成员 */
export interface TeamMember {
  id: number
  user: UserInfo
  level: number
  direct_parent_id: number
  commission_total: number
  sales_total: number
  team_count: number
  joined_at: string
}

// ==================== 其他类型 ====================

/** 轮播图 */
export interface Banner {
  id: number
  title: string
  image: string
  link_type: 'none' | 'product' | 'category' | 'url' | 'page'
  link_value?: string
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
}

/** 优惠券 */
export interface Coupon {
  id: number
  name: string
  type: 'fixed' | 'percent'
  value: number
  min_amount: number
  max_discount?: number
  start_time: string
  end_time: string
  total_count: number
  used_count: number
  per_user_limit: number
  is_active: boolean
  created_at: string
  updated_at: string
}

/** 用户优惠券 */
export interface UserCoupon {
  id: number
  user_id: number
  coupon_id: number
  coupon: Coupon
  status: 'unused' | 'used' | 'expired'
  used_at?: string
  order_id?: number
  created_at: string
  updated_at: string
}

/** 系统配置 */
export interface SystemConfig {
  app_name: string
  app_logo: string
  app_version: string
  api_version: string
  customer_service_phone: string
  customer_service_wechat: string
  about_us: string
  privacy_policy: string
  user_agreement: string
}

// ==================== 表单类型 ====================

/** 表单验证规则 */
export interface FormRule {
  required?: boolean
  pattern?: RegExp
  min?: number
  max?: number
  message: string
  validator?: (value: any) => boolean | Promise<boolean>
}

/** 表单字段 */
export interface FormField {
  name: string
  label: string
  type: 'text' | 'number' | 'email' | 'phone' | 'password' | 'textarea' | 'select' | 'radio' | 'checkbox'
  value: any
  placeholder?: string
  rules?: FormRule[]
  options?: Array<{ label: string; value: any }>
  disabled?: boolean
  readonly?: boolean
}

// ==================== 导出所有类型 ====================
export * from './api'
export * from './components'
