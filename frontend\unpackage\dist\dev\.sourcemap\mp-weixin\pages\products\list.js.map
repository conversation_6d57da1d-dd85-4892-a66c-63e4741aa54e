{"version": 3, "file": "list.js", "sources": ["pages/products/list.uvue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJvZHVjdHMvbGlzdC51dnVl"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 搜索栏 -->\n\t\t<view class=\"search-header\">\n\t\t\t<view class=\"search-bar\" @click=\"goToSearch\">\n\t\t\t\t<Icon name=\"search\" size=\"16\" color=\"#999\" />\n\t\t\t\t<text class=\"search-placeholder\">搜索商品</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 筛选栏 -->\n\t\t<view class=\"filter-bar\">\n\t\t\t<view class=\"filter-item\" :class=\"{ active: currentFilter === 'all' }\" @click=\"handleFilter('all')\">\n\t\t\t\t<text class=\"filter-text\">全部</text>\n\t\t\t</view>\n\t\t\t<view class=\"filter-item\" :class=\"{ active: currentFilter === 'hot' }\" @click=\"handleFilter('hot')\">\n\t\t\t\t<text class=\"filter-text\">热销</text>\n\t\t\t</view>\n\t\t\t<view class=\"filter-item\" :class=\"{ active: currentFilter === 'new' }\" @click=\"handleFilter('new')\">\n\t\t\t\t<text class=\"filter-text\">新品</text>\n\t\t\t</view>\n\t\t\t<view class=\"filter-item\" :class=\"{ active: currentFilter === 'price' }\" @click=\"handleFilter('price')\">\n\t\t\t\t<text class=\"filter-text\">价格</text>\n\t\t\t\t<Icon name=\"arrow-down\" size=\"12\" color=\"#999\" />\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 商品列表 -->\n\t\t<view class=\"product-list\">\n\t\t\t<view class=\"product-item\" v-for=\"(product, index) in products\" :key=\"index\" @click=\"handleProductClick(product)\">\n\t\t\t\t<image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\" />\n\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t<text class=\"product-name\">{{ product.name }}</text>\n\t\t\t\t\t<view class=\"product-price\">\n\t\t\t\t\t\t<text class=\"price-current\">￥{{ product.price }}</text>\n\t\t\t\t\t\t<text class=\"price-original\" v-if=\"product.originalPrice\">￥{{ product.originalPrice }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"product-tags\">\n\t\t\t\t\t\t<text class=\"tag hot\" v-if=\"product.isHot\">热销</text>\n\t\t\t\t\t\t<text class=\"tag new\" v-if=\"product.isNew\">新品</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"product-sales\">\n\t\t\t\t\t\t<text class=\"sales-text\">已售{{ product.sales }}件</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 加载状态 -->\n\t\t<view class=\"load-more\" v-if=\"hasMore\">\n\t\t\t<text class=\"load-text\" v-if=\"!loading\">上拉加载更多</text>\n\t\t\t<text class=\"load-text\" v-else>加载中...</text>\n\t\t</view>\n\n\t\t<!-- 空状态 -->\n\t\t<view class=\"empty-state\" v-if=\"products.length === 0 && !loading\">\n\t\t\t<Icon name=\"empty\" size=\"80\" color=\"#ccc\" />\n\t\t\t<text class=\"empty-text\">暂无商品</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup lang=\"uts\">\nimport { ref, onMounted } from 'vue'\nimport Icon from '@/components/common/Icon.uvue'\n\n// 响应式数据\nconst loading = ref(false)\nconst hasMore = ref(true)\nconst currentFilter = ref('all')\n\n// 模拟商品数据\nconst products = ref([\n\t{\n\t\tid: 1,\n\t\tname: '夏季清爽T恤 纯棉透气 多色可选',\n\t\tprice: 89.00,\n\t\toriginalPrice: 129.00,\n\t\timage: 'https://imggw.zmkj.live/products/tshirt1.jpg',\n\t\tisHot: true,\n\t\tisNew: false,\n\t\tsales: 1234\n\t},\n\t{\n\t\tid: 2,\n\t\tname: '无线蓝牙耳机 降噪立体声 长续航',\n\t\tprice: 299.00,\n\t\toriginalPrice: null,\n\t\timage: 'https://imggw.zmkj.live/products/earphone1.jpg',\n\t\tisHot: false,\n\t\tisNew: true,\n\t\tsales: 567\n\t},\n\t{\n\t\tid: 3,\n\t\tname: '简约家居摆件 北欧风格 装饰品',\n\t\tprice: 59.00,\n\t\toriginalPrice: 89.00,\n\t\timage: 'https://imggw.zmkj.live/products/decoration1.jpg',\n\t\tisHot: true,\n\t\tisNew: false,\n\t\tsales: 890\n\t},\n\t{\n\t\tid: 4,\n\t\tname: '天然护肤套装 补水保湿 温和无刺激',\n\t\tprice: 199.00,\n\t\toriginalPrice: 299.00,\n\t\timage: 'https://imggw.zmkj.live/products/skincare1.jpg',\n\t\tisHot: false,\n\t\tisNew: true,\n\t\tsales: 345\n\t},\n\t{\n\t\tid: 5,\n\t\tname: '运动休闲鞋 透气舒适 防滑耐磨',\n\t\tprice: 159.00,\n\t\toriginalPrice: 199.00,\n\t\timage: 'https://imggw.zmkj.live/products/shoes1.jpg',\n\t\tisHot: true,\n\t\tisNew: false,\n\t\tsales: 678\n\t},\n\t{\n\t\tid: 6,\n\t\tname: '智能手环 健康监测 运动记录',\n\t\tprice: 399.00,\n\t\toriginalPrice: null,\n\t\timage: 'https://imggw.zmkj.live/products/watch1.jpg',\n\t\tisHot: false,\n\t\tisNew: true,\n\t\tsales: 234\n\t}\n])\n\n// 生命周期\nonMounted(() => {\n\tinitPage()\n})\n\n// 方法\nconst initPage = async () => {\n\tuni.__f__('log','at pages/products/list.uvue:143','初始化商品列表页')\n\t// 这里可以添加数据初始化逻辑\n}\n\nconst goToSearch = () => {\n\tuni.navigateTo({\n\t\turl: '/pages/products/search'\n\t})\n}\n\nconst handleFilter = (filter: string) => {\n\tcurrentFilter.value = filter\n\t\n\t// 这里可以根据筛选条件重新获取数据\n\tswitch (filter) {\n\t\tcase 'all':\n\t\t\t// 获取全部商品\n\t\t\tbreak\n\t\tcase 'hot':\n\t\t\t// 获取热销商品\n\t\t\tbreak\n\t\tcase 'new':\n\t\t\t// 获取新品\n\t\t\tbreak\n\t\tcase 'price':\n\t\t\t// 按价格排序\n\t\t\tbreak\n\t}\n}\n\nconst handleProductClick = (product: any) => {\n\tuni.navigateTo({\n\t\turl: `/pages/products/detail?id=${product.id}`\n\t})\n}\n\n// 下拉刷新\nconst onPullDownRefresh = async () => {\n\tawait initPage()\n\tuni.stopPullDownRefresh()\n}\n\n// 上拉加载更多\nconst onReachBottom = async () => {\n\tif (!hasMore.value || loading.value) return\n\t\n\tloading.value = true\n\t// 模拟加载更多数据\n\tsetTimeout(() => {\n\t\tloading.value = false\n\t\t// 这里可以添加更多商品数据\n\t}, 1000)\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n}\n\n// 搜索栏\n.search-header {\n\tpadding: 10px 15px;\n\tbackground-color: #fff;\n\tborder-bottom: 1px solid #eee;\n\t\n\t.search-bar {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\theight: 36px;\n\t\tpadding: 0 12px;\n\t\tbackground-color: #f5f5f5;\n\t\tborder-radius: 18px;\n\t\t\n\t\t.search-placeholder {\n\t\t\tmargin-left: 8px;\n\t\t\tfont-size: 14px;\n\t\t\tcolor: #999;\n\t\t}\n\t}\n}\n\n// 筛选栏\n.filter-bar {\n\tdisplay: flex;\n\tbackground-color: #fff;\n\tborder-bottom: 1px solid #eee;\n\t\n\t.filter-item {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\theight: 44px;\n\t\tposition: relative;\n\t\t\n\t\t&.active {\n\t\t\t.filter-text {\n\t\t\t\tcolor: #007aff;\n\t\t\t}\n\t\t\t\n\t\t\t&::after {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 50%;\n\t\t\t\ttransform: translateX(-50%);\n\t\t\t\twidth: 20px;\n\t\t\t\theight: 2px;\n\t\t\t\tbackground-color: #007aff;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.filter-text {\n\t\t\tfont-size: 14px;\n\t\t\tcolor: #333;\n\t\t\tmargin-right: 4px;\n\t\t}\n\t}\n}\n\n// 商品列表\n.product-list {\n\tpadding: 10px 15px;\n\t\n\t.product-item {\n\t\tdisplay: flex;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 8px;\n\t\tpadding: 12px;\n\t\tmargin-bottom: 10px;\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\t\t\n\t\t.product-image {\n\t\t\twidth: 100px;\n\t\t\theight: 100px;\n\t\t\tborder-radius: 6px;\n\t\t\tmargin-right: 12px;\n\t\t}\n\t\t\n\t\t.product-info {\n\t\t\tflex: 1;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\tjustify-content: space-between;\n\t\t\t\n\t\t\t.product-name {\n\t\t\t\tfont-size: 14px;\n\t\t\t\tcolor: #333;\n\t\t\t\tline-height: 1.4;\n\t\t\t\tmargin-bottom: 8px;\n\t\t\t\tdisplay: -webkit-box;\n\t\t\t\t-webkit-box-orient: vertical;\n\t\t\t\t-webkit-line-clamp: 2;\n\t\t\t\toverflow: hidden;\n\t\t\t}\n\t\t\t\n\t\t\t.product-price {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tmargin-bottom: 8px;\n\t\t\t\t\n\t\t\t\t.price-current {\n\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: #ff3b30;\n\t\t\t\t\tmargin-right: 8px;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.price-original {\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\tcolor: #999;\n\t\t\t\t\ttext-decoration: line-through;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.product-tags {\n\t\t\t\tdisplay: flex;\n\t\t\t\tmargin-bottom: 8px;\n\t\t\t\t\n\t\t\t\t.tag {\n\t\t\t\t\tfont-size: 10px;\n\t\t\t\t\tpadding: 2px 6px;\n\t\t\t\t\tborder-radius: 2px;\n\t\t\t\t\tmargin-right: 4px;\n\t\t\t\t\t\n\t\t\t\t\t&.hot {\n\t\t\t\t\t\tbackground-color: #ff3b30;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&.new {\n\t\t\t\t\t\tbackground-color: #34c759;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.product-sales {\n\t\t\t\t.sales-text {\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\tcolor: #999;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 加载更多\n.load-more {\n\tpadding: 20px;\n\ttext-align: center;\n\t\n\t.load-text {\n\t\tfont-size: 14px;\n\t\tcolor: #999;\n\t}\n}\n\n// 空状态\n.empty-state {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 60px 20px;\n\t\n\t.empty-text {\n\t\tfont-size: 14px;\n\t\tcolor: #999;\n\t\tmargin-top: 16px;\n\t}\n}\n</style>\n", "import MiniProgramPage from 'F:/zmkj-system/frontend/pages/products/list.uvue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "__awaiter", "uni"], "mappings": ";;;;;AAgEA,MAAO,OAAU,MAAA;;;;AAGjB,UAAM,UAAUA,kBAAI,KAAK;AACzB,UAAM,UAAUA,kBAAI,IAAI;AACxB,UAAM,gBAAgBA,kBAAI,KAAK;AAG/B,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACpB,IAAA,cAAA;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,MACP,CAAA;AAAA,MACD,IAAA,cAAA;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,MACP,CAAA;AAAA,MACD,IAAA,cAAA;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,MACP,CAAA;AAAA,MACD,IAAA,cAAA;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,MACP,CAAA;AAAA,MACD,IAAA,cAAA;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,MACP,CAAA;AAAA,MACD,IAAA,cAAA;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,MACP,CAAA;AAAA,IACD,CAAA;AAGDC,kBAAAA,UAAU,MAAA;AACT;IACD,CAAC;AAGD,UAAM,WAAW,MAAA;AAAA,aAAAC,cAAA,UAAA,MAAA,QAAA,QAAA,aAAA;AAChBC,sBAAAA,MAAI,MAAM,OAAM,mCAAkC,UAAU;AAAA,MAE5D,CAAA;AAAA;AAED,UAAM,aAAa,MAAA;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACF;AAEA,UAAM,eAAe,CAAC,WAAc;AACnC,oBAAc,QAAQ;AAAA,IAiBvB;AAEA,UAAM,qBAAqB,CAAC,UAAY,SAAA;AACvCA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,6BAA6B,QAAQ,EAAE;AAAA,MAC5C,CAAA;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/KA,GAAG,WAAW,eAAe;"}