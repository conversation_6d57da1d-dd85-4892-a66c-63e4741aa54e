# Generated by Django 5.2.4 on 2025-07-29 10:03

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Banner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('title', models.CharField(help_text='轮播图标题', max_length=200, verbose_name='标题')),
                ('image', models.ImageField(help_text='建议尺寸：750x300px', upload_to='banners/%Y/%m/%d/', verbose_name='轮播图片')),
                ('link_type', models.CharField(choices=[('none', '无链接'), ('product', '商品详情'), ('category', '商品分类'), ('url', '外部链接'), ('page', '内部页面')], default='none', help_text='点击轮播图的跳转类型', max_length=20, verbose_name='链接类型')),
                ('link_value', models.CharField(blank=True, help_text='根据链接类型填写：商品ID、分类ID、URL地址或页面路径', max_length=500, verbose_name='链接值')),
                ('position', models.CharField(choices=[('home', '首页轮播'), ('category', '分类页轮播'), ('product', '商品页轮播')], default='home', help_text='轮播图显示的位置', max_length=20, verbose_name='显示位置')),
                ('sort_order', models.IntegerField(default=0, help_text='数字越小越靠前', verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, help_text='是否在前台显示', verbose_name='是否启用')),
                ('start_time', models.DateTimeField(blank=True, help_text='轮播图开始显示时间，留空表示立即生效', null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, help_text='轮播图结束显示时间，留空表示永久有效', null=True, verbose_name='结束时间')),
                ('click_count', models.PositiveIntegerField(default=0, help_text='轮播图点击统计', verbose_name='点击次数')),
            ],
            options={
                'verbose_name': '轮播图',
                'verbose_name_plural': '轮播图',
                'ordering': ['position', 'sort_order', '-created_at'],
                'indexes': [models.Index(fields=['position', 'is_active'], name='core_banner_positio_a91839_idx'), models.Index(fields=['sort_order'], name='core_banner_sort_or_9c24af_idx'), models.Index(fields=['start_time', 'end_time'], name='core_banner_start_t_2de665_idx')],
            },
        ),
    ]
