from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import DistributionRelation, DistributionLevel, CommissionRecord

User = get_user_model()


# 注释掉自动创建分销关系的信号处理器
# 用户需要主动加入分销系统
# @receiver(post_save, sender=User)
# def create_distribution_relation(sender, instance, created, **kwargs):
#     """用户创建时自动创建分销关系"""
#     if created:
#         # 获取默认分销等级（最低等级）
#         try:
#             default_level = DistributionLevel.objects.filter(is_active=True).order_by('level').first()
#             if default_level:
#                 DistributionRelation.objects.create(
#                     user=instance,
#                     level=default_level,
#                     is_active=True,
#                     activated_at=timezone.now()
#                 )
#         except DistributionLevel.DoesNotExist:
#             # 如果没有分销等级，先不创建分销关系
#             pass


@receiver(post_save, sender=CommissionRecord)
def update_distributor_commission(sender, instance, created, **kwargs):
    """佣金记录创建或更新时，更新分销商的佣金统计"""
    if created:
        # 新增佣金记录时，更新分销商的总佣金
        distributor = instance.distributor
        distributor.total_commission += instance.commission_amount
        
        # 如果佣金状态是已确认，则加入可提现佣金
        if instance.status == 'confirmed':
            distributor.available_commission += instance.commission_amount
        
        distributor.save()
    else:
        # 佣金记录状态变更时的处理
        if instance.status == 'confirmed' and instance.pk:
            # 获取之前的状态
            old_instance = CommissionRecord.objects.get(pk=instance.pk)
            if old_instance.status != 'confirmed':
                # 从其他状态变为已确认，增加可提现佣金
                instance.distributor.available_commission += instance.commission_amount
                instance.distributor.save()
        elif instance.status == 'cancelled' and instance.pk:
            # 佣金被取消，需要从总佣金中扣除
            old_instance = CommissionRecord.objects.get(pk=instance.pk)
            if old_instance.status != 'cancelled':
                distributor = instance.distributor
                distributor.total_commission -= instance.commission_amount
                if old_instance.status == 'confirmed':
                    distributor.available_commission -= instance.commission_amount
                distributor.save()


@receiver(pre_save, sender=DistributionRelation)
def auto_check_level_upgrade(sender, instance, **kwargs):
    """自动检查分销商等级是否需要升级"""
    if instance.pk:  # 只对已存在的记录进行检查
        # 获取所有可用的分销等级，按等级从低到高排序
        levels = DistributionLevel.objects.filter(is_active=True).order_by('level')

        # 找到最高的符合条件的等级
        target_level = None
        for level in levels:
            # 检查是否满足升级条件
            if (instance.total_sales >= level.min_sales and
                instance.referral_count >= level.min_referrals and
                level.level > instance.level.level):
                target_level = level

        # 如果找到了更高的等级，进行升级
        if target_level:
            instance.level = target_level


def calculate_commission(order, distributor_relation):
    """计算订单佣金"""
    from decimal import Decimal
    
    commission_records = []
    current_distributor = distributor_relation
    level = 1
    
    # 最多计算3级分销
    while current_distributor and level <= 3:
        # 根据等级确定佣金比例
        if level == 1:  # 直推
            commission_rate = current_distributor.level.commission_rate
            commission_type = 'direct'
        elif level == 2:  # 间推
            commission_rate = current_distributor.level.commission_rate * Decimal('0.5')
            commission_type = 'indirect'
        else:  # 团队
            commission_rate = current_distributor.level.commission_rate * Decimal('0.2')
            commission_type = 'team'
        
        # 计算佣金金额
        commission_amount = order.final_amount * commission_rate
        
        if commission_amount > 0:
            # 创建佣金记录
            commission_record = CommissionRecord(
                distributor=current_distributor,
                order=order,
                commission_type=commission_type,
                commission_rate=commission_rate,
                order_amount=order.final_amount,
                commission_amount=commission_amount,
                status='pending'
            )
            commission_records.append(commission_record)
        
        # 移动到上级分销商
        current_distributor = current_distributor.parent
        level += 1
    
    return commission_records


def process_order_commission(order):
    """处理订单佣金分配"""
    # 检查订单是否有推荐人
    if hasattr(order, 'distributor_id') and order.distributor_id:
        try:
            distributor_relation = DistributionRelation.objects.get(
                user_id=order.distributor_id,
                is_active=True
            )
            
            # 计算佣金
            commission_records = calculate_commission(order, distributor_relation)
            
            # 批量创建佣金记录
            if commission_records:
                CommissionRecord.objects.bulk_create(commission_records)
                
                # 更新推荐人的销售统计
                distributor_relation.total_sales += order.final_amount
                distributor_relation.save()
                
        except DistributionRelation.DoesNotExist:
            pass
