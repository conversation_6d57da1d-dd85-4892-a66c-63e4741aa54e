# 图标组件使用说明

## 概述

本项目使用纯CSS + Unicode字符实现图标系统，无需引入图片文件，具有轻量级、可定制、高清适配等优势。

## 基础使用

### 导入组件
```vue
<script setup>
import Icon from '@/components/common/Icon.uvue'
</script>
```

### 基础用法
```vue
<template>
  <!-- 基础图标 -->
  <Icon name="home" />
  
  <!-- 自定义大小和颜色 -->
  <Icon name="cart" :size="40" color="#007AFF" />
  
  <!-- 使用预设样式类 -->
  <Icon name="user" customClass="icon-lg icon-primary" />
  
  <!-- 带点击事件 -->
  <Icon name="search" @click="handleSearch" />
</template>
```

## 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| name | string | - | 图标名称（必需） |
| size | number | 32 | 图标大小（rpx） |
| color | string | #333333 | 图标颜色 |
| customClass | string | '' | 自定义样式类 |

## 事件说明

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| click | 点击图标时触发 | - |

## 可用图标列表

### 基础图标
- `home` - 首页 🏠
- `product` - 产品 📦
- `cart` - 购物车 🛒
- `order` - 订单 📋
- `user` - 用户 👤
- `search` - 搜索 🔍
- `share` - 分享 📤
- `star` - 收藏 ⭐
- `location` - 位置 📍
- `phone` - 电话 📞
- `wechat` - 微信 💬
- `money` - 金钱 💰
- `team` - 团队 👥
- `setting` - 设置 ⚙️

### 方向图标
- `arrow-right` - 右箭头 ➡️
- `arrow-left` - 左箭头 ⬅️
- `up` - 向上 ⬆️
- `down` - 向下 ⬇️
- `left` - 向左 ⬅️
- `right` - 向右 ➡️

### 操作图标
- `close` - 关闭 ❌
- `check` - 选中 ✅
- `plus` - 加号 ➕
- `minus` - 减号 ➖
- `edit` - 编辑 ✏️
- `delete` - 删除 🗑️
- `refresh` - 刷新 🔄

### 媒体图标
- `camera` - 相机 📷
- `image` - 图片 🖼️
- `video` - 视频 🎥
- `audio` - 音频 🎵
- `file` - 文件 📄
- `folder` - 文件夹 📁

### 状态图标
- `success` - 成功 ✅
- `error` - 错误 ❌
- `warning` - 警告 ⚠️
- `info` - 信息 ℹ️
- `loading` - 加载 ⏳

### 业务图标
- `distribution` - 分销 🤝
- `commission` - 佣金 💵
- `promotion` - 推广 📢
- `invite` - 邀请 👋
- `qrcode` - 二维码 📱
- `gift` - 礼品 🎁
- `coupon` - 优惠券 🎫
- `vip` - VIP 👑

## 预设样式类

### 大小预设
- `icon-xs` - 20rpx
- `icon-sm` - 24rpx
- `icon-md` - 32rpx (默认)
- `icon-lg` - 40rpx
- `icon-xl` - 48rpx

### 颜色预设
- `icon-primary` - 主色调 (#007AFF)
- `icon-success` - 成功色 (#34C759)
- `icon-warning` - 警告色 (#FF9500)
- `icon-danger` - 危险色 (#FF3B30)
- `icon-info` - 信息色 (#5AC8FA)
- `icon-secondary` - 次要色 (#8E8E93)
- `icon-light` - 浅色 (#F2F2F7)
- `icon-dark` - 深色 (#1C1C1E)

## 使用示例

### 底部导航栏
```vue
<template>
  <view class="tabbar">
    <view class="tab-item" @click="goHome">
      <Icon name="home" :size="28" :color="activeTab === 'home' ? '#007AFF' : '#999'" />
      <text>首页</text>
    </view>
    <view class="tab-item" @click="goProducts">
      <Icon name="product" :size="28" :color="activeTab === 'products' ? '#007AFF' : '#999'" />
      <text>产品</text>
    </view>
    <view class="tab-item" @click="goCart">
      <Icon name="cart" :size="28" :color="activeTab === 'cart' ? '#007AFF' : '#999'" />
      <text>购物车</text>
    </view>
    <view class="tab-item" @click="goUser">
      <Icon name="user" :size="28" :color="activeTab === 'user' ? '#007AFF' : '#999'" />
      <text>我的</text>
    </view>
  </view>
</template>
```

### 按钮中使用
```vue
<template>
  <button class="btn-primary" @click="handleShare">
    <Icon name="share" :size="24" color="#fff" />
    <text>分享</text>
  </button>
  
  <button class="btn-success" @click="handleSubmit">
    <Icon name="check" :size="24" color="#fff" />
    <text>确认</text>
  </button>
</template>
```

### 列表项中使用
```vue
<template>
  <view class="list-item" @click="goToDetail">
    <Icon name="order" :size="32" customClass="icon-primary" />
    <view class="item-content">
      <text class="title">我的订单</text>
      <text class="desc">查看订单详情</text>
    </view>
    <Icon name="arrow-right" :size="24" customClass="icon-secondary" />
  </view>
</template>
```

### 加载状态
```vue
<template>
  <view class="loading-container">
    <Icon name="loading" :size="40" customClass="icon-primary" />
    <text>加载中...</text>
  </view>
</template>
```

## 自定义图标

如需添加新图标，请在 `Icon.uvue` 文件中添加对应的CSS规则：

```scss
&.icon-custom::before { content: '\e069'; }  // 自定义图标
```

## 注意事项

1. **Unicode字符**：使用标准Unicode字符，确保跨平台兼容性
2. **性能优化**：图标使用CSS实现，无网络请求，加载速度快
3. **样式隔离**：使用scoped样式，避免样式冲突
4. **可访问性**：图标具有语义化的class名称，便于维护
5. **扩展性**：可轻松添加新图标和样式预设

## 最佳实践

1. **统一使用**：项目中统一使用Icon组件，避免混用其他图标方案
2. **语义化命名**：图标名称应具有语义化，便于理解和维护
3. **合理大小**：根据使用场景选择合适的图标大小
4. **颜色搭配**：图标颜色应与整体设计风格保持一致
5. **性能考虑**：避免在循环中频繁渲染大量图标
