{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
/* 折叠菜单样式 */
.module {
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.module h2 {
    background: #79aec8;
    color: white;
    margin: 0;
    padding: 10px 15px;
    cursor: pointer;
    position: relative;
    user-select: none;
    transition: background-color 0.3s ease;
}

.module h2:hover {
    background: #6ba6cd;
}

.module h2::after {
    content: '▼';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
}

.module.collapsed h2::after {
    transform: translateY(-50%) rotate(-90deg);
}

.module-content {
    max-height: 1000px;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: white;
}

.module.collapsed .module-content {
    max-height: 0;
}

.module table {
    width: 100%;
    margin: 0;
    border-collapse: collapse;
}

.module table th,
.module table td {
    padding: 8px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.module table th {
    background: #f8f9fa;
    font-weight: bold;
    color: #333;
}

.module table tr:hover {
    background: #f5f5f5;
}

.module table a {
    color: #447e9b;
    text-decoration: none;
}

.module table a:hover {
    color: #2e5266;
    text-decoration: underline;
}

/* 添加/修改按钮样式 */
.addlink, .changelink {
    display: inline-block;
    padding: 2px 8px;
    margin-left: 10px;
    background: #79aec8;
    color: white !important;
    text-decoration: none;
    border-radius: 3px;
    font-size: 11px;
}

.addlink:hover, .changelink:hover {
    background: #6ba6cd;
    text-decoration: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .module h2 {
        padding: 8px 12px;
        font-size: 14px;
    }
    
    .module table th,
    .module table td {
        padding: 6px 12px;
        font-size: 13px;
    }
}

/* AI功能模块特殊样式 */
.module.ai-module h2 {
    background: #28a745;
}

.module.ai-module h2:hover {
    background: #218838;
}

/* 微信模块特殊样式 */
.module.wechat-module h2 {
    background: #07c160;
}

.module.wechat-module h2:hover {
    background: #06ad56;
}

/* 分销模块特殊样式 */
.module.distribution-module h2 {
    background: #6f42c1;
}

.module.distribution-module h2:hover {
    background: #5a32a3;
}
</style>
{% endblock %}

{% block content %}
<h1>{% trans 'Site administration' %}</h1>

<div id="content-main">
    {% if app_list %}
        {% for app in app_list %}
            <div class="module {% if app.app_label == 'ai' %}ai-module{% elif app.app_label == 'wechat' %}wechat-module{% elif app.app_label == 'distribution' %}distribution-module{% endif %} collapsed" data-app="{{ app.app_label }}">
                <h2>
                    <a href="#" class="section-title">{{ app.name }}</a>
                </h2>
                <div class="module-content">
                    {% if app.models %}
                        <table>
                            <thead>
                                <tr>
                                    <th>{% trans 'Model' %}</th>
                                    <th>{% trans 'Actions' %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for model in app.models %}
                                    <tr class="model-{{ model.object_name|lower }}">
                                        <td>
                                            <strong>{{ model.name }}</strong>
                                            {% if model.admin_url %}
                                                <a href="{{ model.admin_url }}" class="changelink">{% trans 'Change' %}</a>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if model.add_url %}
                                                <a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>
                                            {% endif %}
                                            {% if model.admin_url %}
                                                <a href="{{ model.admin_url }}" class="changelink">{% trans 'Change' %}</a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    {% else %}
        <p>{% trans "You don't have permission to edit anything." %}</p>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有模块标题
    const moduleHeaders = document.querySelectorAll('.module h2');
    
    // 为每个标题添加点击事件
    moduleHeaders.forEach(function(header) {
        header.addEventListener('click', function(e) {
            e.preventDefault();
            const module = this.closest('.module');
            
            // 切换折叠状态
            module.classList.toggle('collapsed');
            
            // 保存状态到localStorage
            const appLabel = module.dataset.app;
            const isCollapsed = module.classList.contains('collapsed');
            localStorage.setItem('admin_module_' + appLabel, isCollapsed ? 'collapsed' : 'expanded');
        });
    });
    
    // 从localStorage恢复状态
    const modules = document.querySelectorAll('.module[data-app]');
    modules.forEach(function(module) {
        const appLabel = module.dataset.app;
        const savedState = localStorage.getItem('admin_module_' + appLabel);
        
        if (savedState === 'expanded') {
            module.classList.remove('collapsed');
        } else if (savedState === 'collapsed') {
            module.classList.add('collapsed');
        }
        // 如果没有保存的状态，保持默认的collapsed状态
    });
    
    // 添加键盘快捷键支持
    document.addEventListener('keydown', function(e) {
        // Ctrl+E 展开所有模块
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            modules.forEach(function(module) {
                module.classList.remove('collapsed');
                const appLabel = module.dataset.app;
                localStorage.setItem('admin_module_' + appLabel, 'expanded');
            });
        }
        
        // Ctrl+C 折叠所有模块
        if (e.ctrlKey && e.key === 'c') {
            e.preventDefault();
            modules.forEach(function(module) {
                module.classList.add('collapsed');
                const appLabel = module.dataset.app;
                localStorage.setItem('admin_module_' + appLabel, 'collapsed');
            });
        }
    });
    
    // 添加搜索功能
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = '搜索模型...';
    searchInput.style.cssText = `
        width: 300px;
        padding: 8px 12px;
        margin-bottom: 20px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    `;
    
    const contentMain = document.getElementById('content-main');
    const firstModule = contentMain.querySelector('.module');
    if (firstModule) {
        contentMain.insertBefore(searchInput, firstModule);
    }
    
    // 搜索功能实现
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        modules.forEach(function(module) {
            const models = module.querySelectorAll('tbody tr');
            let hasVisibleModel = false;
            
            models.forEach(function(row) {
                const modelName = row.querySelector('strong').textContent.toLowerCase();
                if (modelName.includes(searchTerm)) {
                    row.style.display = '';
                    hasVisibleModel = true;
                } else {
                    row.style.display = 'none';
                }
            });
            
            // 如果有匹配的模型，展开模块并显示
            if (hasVisibleModel && searchTerm) {
                module.style.display = '';
                module.classList.remove('collapsed');
            } else if (searchTerm) {
                module.style.display = 'none';
            } else {
                module.style.display = '';
                // 恢复原始折叠状态
                const appLabel = module.dataset.app;
                const savedState = localStorage.getItem('admin_module_' + appLabel);
                if (savedState === 'expanded') {
                    module.classList.remove('collapsed');
                } else {
                    module.classList.add('collapsed');
                }
            }
        });
    });
});
</script>

<!-- 添加快捷键提示 -->
<div style="position: fixed; bottom: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 10px; border-radius: 4px; font-size: 12px; z-index: 1000;">
    <div>快捷键：</div>
    <div>Ctrl+E - 展开所有</div>
    <div>Ctrl+C - 折叠所有</div>
</div>
{% endblock %}
