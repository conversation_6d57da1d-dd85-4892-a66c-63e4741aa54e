from django.db import models
from django.utils import timezone


class BaseModel(models.Model):
    """
    基础抽象模型类
    包含所有模型共有的字段和方法
    """
    created_at = models.DateTimeField(verbose_name='创建时间', default=timezone.now)
    updated_at = models.DateTimeField(verbose_name='更新时间', auto_now=True)
    is_deleted = models.BooleanField(verbose_name='是否删除', default=False)

    class Meta:
        abstract = True
        ordering = ['-created_at']

    def logical_delete(self):
        """逻辑删除"""
        self.is_deleted = True
        self.save()

    def restore(self):
        """恢复逻辑删除"""
        self.is_deleted = False
        self.save()


class Banner(BaseModel):
    """轮播图模型"""
    LINK_TYPE_CHOICES = [
        ('none', '无链接'),
        ('product', '商品详情'),
        ('category', '商品分类'),
        ('url', '外部链接'),
        ('page', '内部页面'),
    ]

    POSITION_CHOICES = [
        ('home', '首页轮播'),
        ('category', '分类页轮播'),
        ('product', '商品页轮播'),
    ]

    title = models.CharField(verbose_name='标题', max_length=200, help_text='轮播图标题')
    image = models.ImageField(
        verbose_name='轮播图片',
        upload_to='banners/%Y/%m/%d/',
        help_text='建议尺寸：750x300px'
    )
    link_type = models.CharField(
        verbose_name='链接类型',
        max_length=20,
        choices=LINK_TYPE_CHOICES,
        default='none',
        help_text='点击轮播图的跳转类型'
    )
    link_value = models.CharField(
        verbose_name='链接值',
        max_length=500,
        blank=True,
        help_text='根据链接类型填写：商品ID、分类ID、URL地址或页面路径'
    )
    position = models.CharField(
        verbose_name='显示位置',
        max_length=20,
        choices=POSITION_CHOICES,
        default='home',
        help_text='轮播图显示的位置'
    )
    sort_order = models.IntegerField(verbose_name='排序', default=0, help_text='数字越小越靠前')
    is_active = models.BooleanField(verbose_name='是否启用', default=True, help_text='是否在前台显示')
    start_time = models.DateTimeField(
        verbose_name='开始时间',
        null=True,
        blank=True,
        help_text='轮播图开始显示时间，留空表示立即生效'
    )
    end_time = models.DateTimeField(
        verbose_name='结束时间',
        null=True,
        blank=True,
        help_text='轮播图结束显示时间，留空表示永久有效'
    )
    click_count = models.PositiveIntegerField(verbose_name='点击次数', default=0, help_text='轮播图点击统计')

    class Meta:
        verbose_name = '轮播图'
        verbose_name_plural = verbose_name
        ordering = ['position', 'sort_order', '-created_at']
        indexes = [
            models.Index(fields=['position', 'is_active']),
            models.Index(fields=['sort_order']),
            models.Index(fields=['start_time', 'end_time']),
        ]

    def __str__(self):
        return f"{self.get_position_display()} - {self.title}"

    def is_valid_time(self):
        """检查轮播图是否在有效时间内"""
        now = timezone.now()
        if self.start_time and now < self.start_time:
            return False
        if self.end_time and now > self.end_time:
            return False
        return True


