#!/usr/bin/env python
"""
测试运行脚本
用于运行Django项目的测试用例
"""

import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 配置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zmkj.settings.development')
django.setup()

def run_tests():
    """运行测试用例"""
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests([
        'apps.core',
        'apps.users',
        'apps.products',
        'apps.orders'
    ])
    sys.exit(bool(failures))

if __name__ == '__main__':
    run_tests()
