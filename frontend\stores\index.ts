/**
 * Pinia状态管理入口文件
 * 配置和导出所有store
 */

import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'

// 创建pinia实例
const pinia = createPinia()

// 配置持久化插件
pinia.use(
  createPersistedState({
    // 默认使用uni-app的存储
    storage: {
      getItem: (key: string) => {
        return uni.getStorageSync(key)
      },
      setItem: (key: string, value: string) => {
        uni.setStorageSync(key, value)
      },
      removeItem: (key: string) => {
        uni.removeStorageSync(key)
      }
    },
    // 默认序列化配置
    serializer: {
      serialize: JSON.stringify,
      deserialize: JSON.parse
    }
  })
)

export default pinia

// 导出所有store
export { useUserStore } from './user'
export { useProductStore } from './product'
export { useCartStore } from './cart'
export { useOrderStore } from './order'
export { useDistributionStore } from './distribution'
