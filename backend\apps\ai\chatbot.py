# -*- coding: utf-8 -*-
"""
智梦科技系统 - 智能客服模块

智能客服系统的核心功能，包括：
- 对话管理
- 意图识别
- 自动回复
- 人工转接

创建时间：2025年7月30日
维护人员：智梦科技开发团队
"""

import logging
import uuid
from typing import Dict, List, Any, Optional, Tuple
from django.utils import timezone
from django.contrib.auth import get_user_model
from .models import ChatbotConversation, ChatbotMessage, AIServiceConfig
from .services import ai_service_manager

User = get_user_model()
logger = logging.getLogger(__name__)


class ChatbotManager:
    """智能客服管理器"""
    
    def __init__(self):
        self.intent_patterns = self._load_intent_patterns()
        self.auto_replies = self._load_auto_replies()
    
    def _load_intent_patterns(self) -> Dict[str, List[str]]:
        """加载意图识别模式"""
        return {
            'greeting': ['你好', '您好', 'hello', 'hi', '在吗'],
            'product_inquiry': ['产品', '价格', '多少钱', '怎么买', '购买'],
            'order_inquiry': ['订单', '物流', '发货', '快递', '配送'],
            'complaint': ['投诉', '问题', '不满意', '退款', '退货'],
            'help': ['帮助', '怎么办', '如何', '怎样', '教程'],
            'goodbye': ['再见', '拜拜', 'bye', '结束', '谢谢']
        }
    
    def _load_auto_replies(self) -> Dict[str, List[str]]:
        """加载自动回复模板"""
        return {
            'greeting': [
                '您好！欢迎来到智梦科技，我是您的专属客服助手，有什么可以帮助您的吗？',
                '您好！很高兴为您服务，请问有什么问题需要咨询吗？'
            ],
            'product_inquiry': [
                '我们有多种优质产品，您可以浏览我们的产品页面查看详情。如需了解具体产品信息，请告诉我您感兴趣的产品名称。',
                '关于产品信息，建议您查看产品详情页面，或者告诉我您想了解哪款产品，我来为您详细介绍。'
            ],
            'order_inquiry': [
                '关于订单问题，您可以在个人中心查看订单状态。如需具体帮助，请提供您的订单号。',
                '订单相关问题我很乐意帮您解决，请提供订单号，我来为您查询详细信息。'
            ],
            'complaint': [
                '非常抱歉给您带来不便，我会立即为您转接人工客服，请稍等片刻。',
                '对于您遇到的问题，我深表歉意。让我为您转接专业客服人员来处理。'
            ],
            'help': [
                '我很乐意为您提供帮助！请详细描述您遇到的问题，我会尽力为您解答。',
                '请告诉我您需要什么帮助，我会为您提供详细的指导。'
            ],
            'goodbye': [
                '感谢您的咨询，祝您生活愉快！如有其他问题，随时欢迎回来。',
                '再见！希望我的服务对您有帮助，期待下次为您服务。'
            ],
            'default': [
                '抱歉，我没有完全理解您的问题。您可以换个方式描述，或者我为您转接人工客服。',
                '我正在学习中，对于这个问题可能理解不够准确。建议您联系人工客服获得更好的帮助。'
            ]
        }
    
    def create_conversation(self, user_id: Optional[int] = None) -> ChatbotConversation:
        """
        创建新的对话会话
        
        Args:
            user_id: 用户ID
            
        Returns:
            ChatbotConversation: 对话对象
        """
        session_id = str(uuid.uuid4())
        conversation = ChatbotConversation.objects.create(
            user_id=user_id,
            session_id=session_id,
            status='active'
        )
        
        # 发送欢迎消息
        welcome_message = self._get_auto_reply('greeting')
        self.add_message(conversation, 'bot', welcome_message)
        
        logger.info(f"创建新对话会话: {session_id}")
        return conversation
    
    def get_conversation(self, session_id: str) -> Optional[ChatbotConversation]:
        """
        获取对话会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            ChatbotConversation: 对话对象
        """
        try:
            return ChatbotConversation.objects.get(session_id=session_id)
        except ChatbotConversation.DoesNotExist:
            return None
    
    def add_message(self, conversation: ChatbotConversation, message_type: str, 
                   content: str, metadata: Dict[str, Any] = None) -> ChatbotMessage:
        """
        添加消息到对话
        
        Args:
            conversation: 对话对象
            message_type: 消息类型
            content: 消息内容
            metadata: 元数据
            
        Returns:
            ChatbotMessage: 消息对象
        """
        message = ChatbotMessage.objects.create(
            conversation=conversation,
            message_type=message_type,
            content=content,
            metadata=metadata or {}
        )
        return message
    
    def process_user_message(self, session_id: str, user_message: str) -> Dict[str, Any]:
        """
        处理用户消息
        
        Args:
            session_id: 会话ID
            user_message: 用户消息
            
        Returns:
            dict: 处理结果
        """
        conversation = self.get_conversation(session_id)
        if not conversation:
            return {
                'success': False,
                'error': '会话不存在'
            }
        
        if conversation.status != 'active':
            return {
                'success': False,
                'error': '会话已结束'
            }
        
        # 记录用户消息
        self.add_message(conversation, 'user', user_message)
        
        # 意图识别和自动回复
        intent = self._recognize_intent(user_message)
        bot_reply = self._generate_reply(intent, user_message)
        
        # 记录机器人回复
        bot_message = self.add_message(conversation, 'bot', bot_reply, {
            'intent': intent,
            'confidence': 0.8  # 模拟置信度
        })
        
        # 检查是否需要转人工
        need_transfer = self._should_transfer_to_human(intent, user_message)
        if need_transfer:
            self._transfer_to_human(conversation)
        
        return {
            'success': True,
            'bot_reply': bot_reply,
            'intent': intent,
            'need_transfer': need_transfer,
            'message_id': bot_message.id
        }
    
    def _recognize_intent(self, message: str) -> str:
        """
        识别用户意图
        
        Args:
            message: 用户消息
            
        Returns:
            str: 意图类型
        """
        message_lower = message.lower()
        
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if pattern in message_lower:
                    return intent
        
        return 'unknown'
    
    def _generate_reply(self, intent: str, user_message: str) -> str:
        """
        生成回复消息
        
        Args:
            intent: 意图类型
            user_message: 用户消息
            
        Returns:
            str: 回复消息
        """
        import random
        
        if intent in self.auto_replies:
            replies = self.auto_replies[intent]
        else:
            replies = self.auto_replies['default']
        
        return random.choice(replies)
    
    def _get_auto_reply(self, intent: str) -> str:
        """
        获取自动回复
        
        Args:
            intent: 意图类型
            
        Returns:
            str: 回复消息
        """
        import random
        
        if intent in self.auto_replies:
            return random.choice(self.auto_replies[intent])
        return random.choice(self.auto_replies['default'])
    
    def _should_transfer_to_human(self, intent: str, message: str) -> bool:
        """
        判断是否需要转人工
        
        Args:
            intent: 意图类型
            message: 用户消息
            
        Returns:
            bool: 是否需要转人工
        """
        # 投诉类问题直接转人工
        if intent == 'complaint':
            return True
        
        # 包含特定关键词转人工
        transfer_keywords = ['人工', '客服', '转接', '投诉', '退款']
        message_lower = message.lower()
        
        for keyword in transfer_keywords:
            if keyword in message_lower:
                return True
        
        return False
    
    def _transfer_to_human(self, conversation: ChatbotConversation):
        """
        转接人工客服
        
        Args:
            conversation: 对话对象
        """
        conversation.status = 'transferred'
        conversation.save()
        
        # 发送转接提示消息
        transfer_message = "正在为您转接人工客服，请稍等片刻..."
        self.add_message(conversation, 'system', transfer_message)
        
        logger.info(f"对话 {conversation.session_id} 已转接人工客服")
    
    def close_conversation(self, session_id: str) -> bool:
        """
        关闭对话会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 是否成功关闭
        """
        try:
            conversation = self.get_conversation(session_id)
            if conversation:
                conversation.status = 'closed'
                conversation.end_time = timezone.now()
                conversation.save()
                
                # 发送结束消息
                goodbye_message = self._get_auto_reply('goodbye')
                self.add_message(conversation, 'bot', goodbye_message)
                
                logger.info(f"关闭对话会话: {session_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"关闭对话会话失败: {e}")
            return False
    
    def get_conversation_history(self, session_id: str) -> List[Dict[str, Any]]:
        """
        获取对话历史
        
        Args:
            session_id: 会话ID
            
        Returns:
            list: 对话历史
        """
        conversation = self.get_conversation(session_id)
        if not conversation:
            return []
        
        messages = conversation.messages.all()
        history = []
        
        for message in messages:
            history.append({
                'id': message.id,
                'type': message.message_type,
                'content': message.content,
                'metadata': message.metadata,
                'created_at': message.created_at.isoformat()
            })
        
        return history


# 全局智能客服管理器实例
chatbot_manager = ChatbotManager()
