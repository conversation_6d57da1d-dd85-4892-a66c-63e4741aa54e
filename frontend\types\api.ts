/**
 * API接口类型定义
 * 定义所有API请求和响应的类型
 */

import type {
  UserInfo,
  Product,
  ProductCategory,
  CartItem,
  Order,
  DistributorInfo,
  CommissionRecord,
  WithdrawRecord,
  TeamMember,
  Banner,
  Coupon,
  UserCoupon,
  Address,
  PaginationParams,
  PaginationResponse,
  LoginRequest,
  LoginResponse,
  CreateOrderRequest,
  AddToCartRequest
} from './index'

// ==================== 用户API ====================

export namespace UserAPI {
  /** 微信登录 */
  export interface WxLoginRequest {
    code: string
    encrypted_data?: string
    iv?: string
  }

  /** 手机号登录 */
  export interface PhoneLoginRequest {
    phone: string
    code: string
  }

  /** 更新用户信息 */
  export interface UpdateUserRequest {
    nickname?: string
    avatar?: string
    gender?: 'M' | 'F' | 'U'
    birthday?: string
  }

  /** 绑定手机号 */
  export interface BindPhoneRequest {
    phone: string
    code: string
  }
}

// ==================== 产品API ====================

export namespace ProductAPI {
  /** 获取产品列表 */
  export interface GetProductsRequest extends PaginationParams {
    category_id?: number
    is_hot?: boolean
    is_new?: boolean
    is_recommend?: boolean
    min_price?: number
    max_price?: number
    tags?: string[]
  }

  export interface GetProductsResponse extends PaginationResponse<Product> {}

  /** 获取产品详情 */
  export interface GetProductDetailRequest {
    id: number
  }

  export interface GetProductDetailResponse {
    product: Product
    related_products: Product[]
  }

  /** 获取产品分类 */
  export interface GetCategoriesResponse {
    categories: ProductCategory[]
  }

  /** 搜索产品 */
  export interface SearchProductsRequest extends PaginationParams {
    keyword: string
    category_id?: number
    min_price?: number
    max_price?: number
  }

  export interface SearchProductsResponse extends PaginationResponse<Product> {
    hot_keywords: string[]
    related_keywords: string[]
  }
}

// ==================== 购物车API ====================

export namespace CartAPI {
  /** 获取购物车列表 */
  export interface GetCartItemsResponse {
    items: CartItem[]
    total_count: number
    selected_count: number
    total_amount: number
  }

  /** 更新购物车商品 */
  export interface UpdateCartItemRequest {
    id: number
    quantity?: number
    selected?: boolean
  }

  /** 批量更新购物车 */
  export interface BatchUpdateCartRequest {
    items: Array<{
      id: number
      quantity?: number
      selected?: boolean
    }>
  }

  /** 删除购物车商品 */
  export interface RemoveCartItemRequest {
    ids: number[]
  }
}

// ==================== 订单API ====================

export namespace OrderAPI {
  /** 获取订单列表 */
  export interface GetOrdersRequest extends PaginationParams {
    status?: string
  }

  export interface GetOrdersResponse extends PaginationResponse<Order> {}

  /** 获取订单详情 */
  export interface GetOrderDetailRequest {
    id: number
  }

  export interface GetOrderDetailResponse {
    order: Order
  }

  /** 取消订单 */
  export interface CancelOrderRequest {
    id: number
    reason: string
  }

  /** 确认收货 */
  export interface ConfirmOrderRequest {
    id: number
  }

  /** 申请退款 */
  export interface RefundOrderRequest {
    id: number
    reason: string
    amount: number
    images?: string[]
  }

  /** 订单支付 */
  export interface PayOrderRequest {
    order_id: number
    payment_method: 'wechat' | 'alipay'
  }

  export interface PayOrderResponse {
    payment_params: any
    order_no: string
  }
}

// ==================== 地址API ====================

export namespace AddressAPI {
  /** 获取地址列表 */
  export interface GetAddressesResponse {
    addresses: Address[]
  }

  /** 创建地址 */
  export interface CreateAddressRequest {
    name: string
    phone: string
    province: string
    city: string
    district: string
    detail: string
    postal_code?: string
    is_default?: boolean
  }

  /** 更新地址 */
  export interface UpdateAddressRequest extends CreateAddressRequest {
    id: number
  }

  /** 删除地址 */
  export interface DeleteAddressRequest {
    id: number
  }

  /** 设置默认地址 */
  export interface SetDefaultAddressRequest {
    id: number
  }
}

// ==================== 分销API ====================

export namespace DistributionAPI {
  /** 获取分销商信息 */
  export interface GetDistributorInfoResponse {
    distributor: DistributorInfo
    statistics: {
      today_commission: number
      this_month_commission: number
      total_commission: number
      team_count: number
      direct_count: number
      total_sales: number
    }
  }

  /** 申请成为分销商 */
  export interface ApplyDistributorRequest {
    real_name: string
    id_card: string
    phone: string
    reason?: string
  }

  /** 获取佣金记录 */
  export interface GetCommissionRecordsRequest extends PaginationParams {
    status?: CommissionStatus
    type?: 'direct' | 'indirect'
  }

  export interface GetCommissionRecordsResponse extends PaginationResponse<CommissionRecord> {}

  /** 获取提现记录 */
  export interface GetWithdrawRecordsRequest extends PaginationParams {
    status?: string
  }

  export interface GetWithdrawRecordsResponse extends PaginationResponse<WithdrawRecord> {}

  /** 申请提现 */
  export interface ApplyWithdrawRequest {
    amount: number
    type: 'bank' | 'alipay' | 'wechat'
    bank_info?: {
      bank_name: string
      account_name: string
      account_number: string
    }
    alipay_info?: {
      account: string
      name: string
    }
    wechat_info?: {
      openid: string
      name: string
    }
  }

  /** 获取团队成员 */
  export interface GetTeamMembersRequest extends PaginationParams {
    level?: number
  }

  export interface GetTeamMembersResponse extends PaginationResponse<TeamMember> {}

  /** 生成推广二维码 */
  export interface GenerateQrCodeRequest {
    type: 'app' | 'product' | 'page'
    target_id?: number
    page?: string
  }

  export interface GenerateQrCodeResponse {
    qr_code: string
    share_url: string
  }

  /** 获取推广链接 */
  export interface GetShareLinkRequest {
    type: 'app' | 'product' | 'page'
    target_id?: number
    page?: string
  }

  export interface GetShareLinkResponse {
    share_url: string
    share_title: string
    share_desc: string
    share_image: string
  }
}

// ==================== 优惠券API ====================

export namespace CouponAPI {
  /** 获取可用优惠券 */
  export interface GetAvailableCouponsRequest {
    amount?: number
  }

  export interface GetAvailableCouponsResponse {
    coupons: Coupon[]
  }

  /** 获取用户优惠券 */
  export interface GetUserCouponsRequest extends PaginationParams {
    status?: 'unused' | 'used' | 'expired'
  }

  export interface GetUserCouponsResponse extends PaginationResponse<UserCoupon> {}

  /** 领取优惠券 */
  export interface ReceiveCouponRequest {
    coupon_id: number
  }

  /** 使用优惠券 */
  export interface UseCouponRequest {
    coupon_id: number
    order_id: number
  }
}

// ==================== 其他API ====================

export namespace CommonAPI {
  /** 获取轮播图 */
  export interface GetBannersRequest {
    position?: string
  }

  export interface GetBannersResponse {
    banners: Banner[]
  }

  /** 上传文件 */
  export interface UploadFileRequest {
    file: File
    type: 'image' | 'video' | 'document'
  }

  export interface UploadFileResponse {
    url: string
    filename: string
    size: number
    type: string
  }

  /** 发送短信验证码 */
  export interface SendSmsRequest {
    phone: string
    type: 'login' | 'register' | 'bind' | 'reset'
  }

  /** 获取系统配置 */
  export interface GetConfigResponse {
    config: {
      app_name: string
      app_logo: string
      customer_service_phone: string
      customer_service_wechat: string
      about_us: string
      privacy_policy: string
      user_agreement: string
    }
  }

  /** 意见反馈 */
  export interface FeedbackRequest {
    type: 'bug' | 'suggestion' | 'complaint' | 'other'
    content: string
    contact?: string
    images?: string[]
  }
}

// ==================== 统计API ====================

export namespace StatisticsAPI {
  /** 获取首页统计数据 */
  export interface GetHomeStatsResponse {
    user_count: number
    product_count: number
    order_count: number
    sales_amount: number
    hot_products: Product[]
    new_products: Product[]
    recommend_products: Product[]
  }

  /** 获取用户统计 */
  export interface GetUserStatsResponse {
    order_count: number
    total_amount: number
    coupon_count: number
    favorite_count: number
    recent_orders: Order[]
  }

  /** 获取分销统计 */
  export interface GetDistributionStatsResponse {
    total_commission: number
    this_month_commission: number
    team_count: number
    direct_count: number
    recent_commissions: CommissionRecord[]
  }
}
