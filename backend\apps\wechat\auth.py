"""
微信登录认证模块
"""
import hashlib
import time
import requests
from django.conf import settings
from django.contrib.auth import get_user_model
from .models import WechatUser, WechatConfig

User = get_user_model()


class WechatAuth:
    """微信登录认证类"""
    
    def __init__(self, config_name='default'):
        # 从数据库获取配置
        config = WechatConfig.get_config('official', config_name)
        self.app_id = config.get('app_id', '')
        self.app_secret = config.get('app_secret', '')
        self.api_base = 'https://api.weixin.qq.com'
    
    def get_access_token(self, code):
        """通过code获取access_token"""
        url = f"{self.api_base}/sns/oauth2/access_token"
        params = {
            'appid': self.app_id,
            'secret': self.app_secret,
            'code': code,
            'grant_type': 'authorization_code'
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if 'errcode' in data:
            raise Exception(f"获取access_token失败: {data.get('errmsg', '未知错误')}")
        
        return data
    
    def get_user_info(self, access_token, openid):
        """获取用户信息"""
        url = f"{self.api_base}/sns/userinfo"
        params = {
            'access_token': access_token,
            'openid': openid,
            'lang': 'zh_CN'
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if 'errcode' in data:
            raise Exception(f"获取用户信息失败: {data.get('errmsg', '未知错误')}")
        
        return data
    
    def login_or_create_user(self, code):
        """微信登录或创建用户"""
        # 获取access_token
        token_data = self.get_access_token(code)
        access_token = token_data['access_token']
        openid = token_data['openid']
        
        # 获取用户信息
        user_info = self.get_user_info(access_token, openid)
        
        # 查找或创建微信用户
        wechat_user, created = WechatUser.objects.get_or_create(
            openid=openid,
            defaults={
                'unionid': user_info.get('unionid'),
                'nickname': user_info.get('nickname'),
                'avatar_url': user_info.get('headimgurl'),
                'gender': user_info.get('sex', 0),
                'city': user_info.get('city'),
                'province': user_info.get('province'),
                'country': user_info.get('country'),
                'language': user_info.get('language', 'zh_CN'),
            }
        )
        
        # 如果是新用户，创建系统用户
        if created or not wechat_user.user:
            # 生成用户名
            username = f"wx_{openid[:10]}"
            counter = 1
            original_username = username
            while User.objects.filter(username=username).exists():
                username = f"{original_username}_{counter}"
                counter += 1
            
            # 创建系统用户
            user = User.objects.create_user(
                username=username,
                nickname=user_info.get('nickname'),
                wechat_openid=openid,
                wechat_unionid=user_info.get('unionid'),
            )
            
            # 关联微信用户
            wechat_user.user = user
            wechat_user.save()
        else:
            # 更新用户信息
            user = wechat_user.user
            if not user.nickname:
                user.nickname = user_info.get('nickname')
            if not user.wechat_openid:
                user.wechat_openid = openid
            if not user.wechat_unionid and user_info.get('unionid'):
                user.wechat_unionid = user_info.get('unionid')
            user.save()
            
            # 更新微信用户信息
            wechat_user.nickname = user_info.get('nickname')
            wechat_user.avatar_url = user_info.get('headimgurl')
            wechat_user.gender = user_info.get('sex', 0)
            wechat_user.city = user_info.get('city')
            wechat_user.province = user_info.get('province')
            wechat_user.country = user_info.get('country')
            wechat_user.save()
        
        return user, wechat_user


class WechatSignature:
    """微信签名验证类"""
    
    @staticmethod
    def check_signature(signature, timestamp, nonce, token):
        """验证微信服务器签名"""
        tmp_arr = [token, timestamp, nonce]
        tmp_arr.sort()
        tmp_str = ''.join(tmp_arr)
        tmp_str = hashlib.sha1(tmp_str.encode('utf-8')).hexdigest()
        return tmp_str == signature
    
    @staticmethod
    def generate_js_signature(jsapi_ticket, url):
        """生成JS-SDK签名"""
        noncestr = hashlib.md5(str(time.time()).encode()).hexdigest()[:16]
        timestamp = int(time.time())
        
        string1 = f"jsapi_ticket={jsapi_ticket}&noncestr={noncestr}&timestamp={timestamp}&url={url}"
        signature = hashlib.sha1(string1.encode('utf-8')).hexdigest()
        
        return {
            'nonceStr': noncestr,
            'timestamp': timestamp,
            'signature': signature
        }
