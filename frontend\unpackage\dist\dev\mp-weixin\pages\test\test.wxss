@import "../../uvue.wxss";
:host{display:flex;flex-direction:column}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 图标相关变量 */
/* 图标颜色预设 */
/* 全局图标基础样式 */
.icon-base.data-v-f37d094f {
  font-family: "iconfont";
  font-style: normal;
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
}

/* 图标动画 */
@keyframes icon-rotate-f37d094f {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
.icon-spin.data-v-f37d094f {
  animation: icon-rotate-f37d094f 1s linear infinite;
}
.test-page.data-v-f37d094f {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-f37d094f {
  text-align: center;
  margin-bottom: 30px;
}
.header .title.data-v-f37d094f {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}
.test-section.data-v-f37d094f {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.test-section .section-title.data-v-f37d094f {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  display: block;
}
.test-btn.data-v-f37d094f {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 16px;
  margin-bottom: 15px;
}
.loading.data-v-f37d094f {
  text-align: center;
  color: #666;
  padding: 20px;
}
.error.data-v-f37d094f {
  background-color: #ffebee;
  color: #c62828;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}
.result .result-title.data-v-f37d094f {
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  display: block;
}
.banner-item.data-v-f37d094f {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
}
.banner-item .banner-title.data-v-f37d094f {
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5px;
}
.banner-item .banner-url.data-v-f37d094f {
  color: #666;
  font-size: 12px;
  word-break: break-all;
  display: block;
}
.config-info text.data-v-f37d094f {
  display: block;
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
}