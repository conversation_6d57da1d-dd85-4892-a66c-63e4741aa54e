from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'distribution'

# DRF路由器
router = DefaultRouter()
router.register(r'relations', views.DistributionRelationViewSet, basename='relation')
router.register(r'commissions', views.CommissionRecordViewSet, basename='commission')
router.register(r'withdrawals', views.WithdrawalRecordViewSet, basename='withdrawal')
router.register(r'promotion-codes', views.PromotionCodeViewSet, basename='promotion-code')
router.register(r'activities', views.MarketingActivityViewSet, basename='activity')
router.register(r'levels', views.DistributionLevelViewSet, basename='level')

urlpatterns = [
    # API接口
    path('api/', include(router.urls)),
    
    # 分销商相关
    path('join/', views.JoinDistributionView.as_view(), name='join'),
    path('bind-parent/', views.BindParentView.as_view(), name='bind-parent'),
    path('my-team/', views.MyTeamView.as_view(), name='my-team'),
    path('my-commission/', views.MyCommissionView.as_view(), name='my-commission'),
    path('apply-withdrawal/', views.ApplyWithdrawalView.as_view(), name='apply-withdrawal'),
    
    # 推广码相关
    path('generate-code/', views.GeneratePromotionCodeView.as_view(), name='generate-code'),
    path('validate-code/<str:code>/', views.ValidatePromotionCodeView.as_view(), name='validate-code'),
    
    # 统计相关
    path('stats/overview/', views.DistributionStatsView.as_view(), name='stats-overview'),
    path('stats/commission/', views.CommissionStatsView.as_view(), name='stats-commission'),
    path('stats/team/', views.TeamStatsView.as_view(), name='stats-team'),
    
    # 管理后台相关
    path('admin/dashboard/', views.AdminDashboardView.as_view(), name='admin-dashboard'),
    path('admin/approve-withdrawal/<int:pk>/', views.ApproveWithdrawalView.as_view(), name='approve-withdrawal'),
    path('admin/reject-withdrawal/<int:pk>/', views.RejectWithdrawalView.as_view(), name='reject-withdrawal'),
]
