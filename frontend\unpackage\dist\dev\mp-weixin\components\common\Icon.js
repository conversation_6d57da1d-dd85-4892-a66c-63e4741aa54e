"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "Icon",
  props: {
    name: {},
    size: { default: 32 },
    color: { default: "#333333" },
    customClass: { default: "" }
  },
  emits: ["click"],
  setup(__props, _a) {
    var __emit = _a.emit;
    const props = __props;
    const emit = __emit;
    const iconStyle = common_vendor.computed(() => {
      return new UTSJSONObject({
        fontSize: props.size + "rpx",
        color: props.color
      });
    });
    const handleClick = () => {
      emit("click");
    };
    return (_ctx = null, _cache = null) => {
      const __returned__ = {
        a: common_vendor.sei(common_vendor.gei(_ctx, ""), "text"),
        b: common_vendor.n(`icon-${_ctx.name}`),
        c: common_vendor.n(_ctx.customClass),
        d: common_vendor.s(common_vendor.unref(iconStyle)),
        e: common_vendor.o(handleClick)
      };
      return __returned__;
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d330f826"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/common/Icon.js.map
