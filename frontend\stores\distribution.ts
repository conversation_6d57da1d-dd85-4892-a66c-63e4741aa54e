/**
 * 分销状态管理
 * 管理分销商信息、团队、佣金等
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import http from '@/utils/request'
import { getCdnUrl } from '@/utils/config'
import { 
  DISTRIBUTOR_LEVELS, 
  DISTRIBUTOR_LEVEL_TEXT, 
  COMMISSION_STATUS, 
  COMMISSION_STATUS_TEXT 
} from '@/utils/constants'

// 分销商信息接口
export interface DistributorInfo {
  id: number
  user_id: number
  level: number
  parent_id?: number
  real_name: string
  phone: string
  id_card: string
  bank_account?: string
  total_commission: number
  available_commission: number
  frozen_commission: number
  withdrawn_commission: number
  total_sales: number
  team_count: number
  direct_count: number
  status: string
  created_at: string
}

// 团队成员接口
export interface TeamMember {
  id: number
  user_id: number
  nickname: string
  avatar: string
  phone: string
  level: number
  parent_id: number
  total_commission: number
  total_sales: number
  team_count: number
  join_time: string
}

// 佣金记录接口
export interface CommissionRecord {
  id: number
  order_id: number
  order_no: string
  product_name: string
  product_image: string
  commission_amount: number
  commission_rate: number
  status: string
  settle_time?: string
  created_at: string
}

// 提现记录接口
export interface WithdrawRecord {
  id: number
  amount: number
  fee: number
  actual_amount: number
  bank_account: string
  status: string
  apply_time: string
  process_time?: string
  remark?: string
}

export const useDistributionStore = defineStore('distribution', () => {
  // 状态
  const distributorInfo = ref<DistributorInfo | null>(null)
  const teamMembers = ref<TeamMember[]>([])
  const commissionRecords = ref<CommissionRecord[]>([])
  const withdrawRecords = ref<WithdrawRecord[]>([])
  const isLoading = ref(false)
  const hasMore = ref(true)
  const currentPage = ref(1)
  const pageSize = ref(20)

  // 计算属性
  const isDistributor = computed(() => {
    return !!distributorInfo.value
  })

  const distributorLevel = computed(() => {
    return distributorInfo.value?.level || 0
  })

  const distributorLevelText = computed(() => {
    return DISTRIBUTOR_LEVEL_TEXT[distributorLevel.value] || '普通用户'
  })

  const totalCommission = computed(() => {
    return distributorInfo.value?.total_commission || 0
  })

  const availableCommission = computed(() => {
    return distributorInfo.value?.available_commission || 0
  })

  const teamStats = computed(() => {
    const stats = {
      total: teamMembers.value.length,
      level1: 0,
      level2: 0,
      level3: 0
    }
    
    teamMembers.value.forEach(member => {
      if (member.level === 1) stats.level1++
      else if (member.level === 2) stats.level2++
      else if (member.level === 3) stats.level3++
    })
    
    return stats
  })

  // 方法
  /**
   * 获取分销商信息
   */
  const getDistributorInfo = async (): Promise<boolean> => {
    try {
      const response = await http.get('/distribution/info')
      
      if (response.success) {
        distributorInfo.value = response.data
        return true
      }
      
      return false
    } catch (error) {
      console.error('获取分销商信息失败:', error)
      return false
    }
  }

  /**
   * 申请成为分销商
   */
  const applyDistributor = async (data: {
    real_name: string
    id_card: string
    phone: string
    bank_account?: string
  }): Promise<boolean> => {
    try {
      const response = await http.post('/distribution/apply', data)
      
      if (response.success) {
        // 重新获取分销商信息
        await getDistributorInfo()
        return true
      }
      
      return false
    } catch (error) {
      console.error('申请分销商失败:', error)
      return false
    }
  }

  /**
   * 获取团队成员列表
   */
  const getTeamMembers = async (params: {
    page?: number
    page_size?: number
    level?: number
  } = {}): Promise<boolean> => {
    try {
      isLoading.value = true
      
      const queryParams = {
        page: params.page || currentPage.value,
        page_size: params.page_size || pageSize.value,
        ...params
      }
      
      const response = await http.get('/distribution/team', queryParams)
      
      if (response.success) {
        const { data, pagination } = response.data
        
        // 处理头像URL
        const processedMembers = data.map((member: any) => ({
          ...member,
          avatar: getCdnUrl(member.avatar)
        }))
        
        if (queryParams.page === 1) {
          teamMembers.value = processedMembers
        } else {
          teamMembers.value.push(...processedMembers)
        }
        
        currentPage.value = pagination.current_page
        hasMore.value = pagination.current_page < pagination.total_pages
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('获取团队成员失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 获取佣金记录
   */
  const getCommissionRecords = async (params: {
    page?: number
    page_size?: number
    status?: string
    start_date?: string
    end_date?: string
  } = {}): Promise<boolean> => {
    try {
      isLoading.value = true
      
      const queryParams = {
        page: params.page || currentPage.value,
        page_size: params.page_size || pageSize.value,
        ...params
      }
      
      const response = await http.get('/distribution/commission', queryParams)
      
      if (response.success) {
        const { data, pagination } = response.data
        
        // 处理产品图片URL
        const processedRecords = data.map((record: any) => ({
          ...record,
          product_image: getCdnUrl(record.product_image)
        }))
        
        if (queryParams.page === 1) {
          commissionRecords.value = processedRecords
        } else {
          commissionRecords.value.push(...processedRecords)
        }
        
        currentPage.value = pagination.current_page
        hasMore.value = pagination.current_page < pagination.total_pages
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('获取佣金记录失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 获取提现记录
   */
  const getWithdrawRecords = async (params: {
    page?: number
    page_size?: number
  } = {}): Promise<boolean> => {
    try {
      const queryParams = {
        page: params.page || currentPage.value,
        page_size: params.page_size || pageSize.value,
        ...params
      }
      
      const response = await http.get('/distribution/withdraw', queryParams)
      
      if (response.success) {
        const { data, pagination } = response.data
        
        if (queryParams.page === 1) {
          withdrawRecords.value = data
        } else {
          withdrawRecords.value.push(...data)
        }
        
        currentPage.value = pagination.current_page
        hasMore.value = pagination.current_page < pagination.total_pages
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('获取提现记录失败:', error)
      return false
    }
  }

  /**
   * 申请提现
   */
  const applyWithdraw = async (amount: number, bankAccount?: string): Promise<boolean> => {
    try {
      const response = await http.post('/distribution/withdraw/apply', {
        amount,
        bank_account: bankAccount
      })
      
      if (response.success) {
        // 更新分销商信息
        if (distributorInfo.value) {
          distributorInfo.value.available_commission -= amount
          distributorInfo.value.frozen_commission += amount
        }
        
        // 重新获取提现记录
        await getWithdrawRecords({ page: 1 })
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('申请提现失败:', error)
      return false
    }
  }

  /**
   * 生成推广二维码
   */
  const generateQrCode = async (): Promise<string | null> => {
    try {
      const response = await http.post('/distribution/qrcode')
      
      if (response.success) {
        return getCdnUrl(response.data.qrcode_url)
      }
      
      return null
    } catch (error) {
      console.error('生成推广二维码失败:', error)
      return null
    }
  }

  /**
   * 获取推广链接
   */
  const getPromotionLink = async (productId?: number): Promise<string | null> => {
    try {
      const params = productId ? { product_id: productId } : {}
      const response = await http.get('/distribution/promotion-link', params)
      
      if (response.success) {
        return response.data.link
      }
      
      return null
    } catch (error) {
      console.error('获取推广链接失败:', error)
      return null
    }
  }

  /**
   * 获取分销统计数据
   */
  const getDistributionStats = async (period: string = 'month'): Promise<any> => {
    try {
      const response = await http.get('/distribution/stats', { period })
      
      if (response.success) {
        return response.data
      }
      
      return null
    } catch (error) {
      console.error('获取分销统计失败:', error)
      return null
    }
  }

  /**
   * 重置分销数据
   */
  const resetDistribution = (): void => {
    teamMembers.value = []
    commissionRecords.value = []
    withdrawRecords.value = []
    currentPage.value = 1
    hasMore.value = true
  }

  /**
   * 加载更多数据
   */
  const loadMore = async (type: 'team' | 'commission' | 'withdraw', params: any = {}): Promise<boolean> => {
    if (!hasMore.value || isLoading.value) {
      return false
    }
    
    const nextPage = currentPage.value + 1
    
    switch (type) {
      case 'team':
        return getTeamMembers({ ...params, page: nextPage })
      case 'commission':
        return getCommissionRecords({ ...params, page: nextPage })
      case 'withdraw':
        return getWithdrawRecords({ ...params, page: nextPage })
      default:
        return false
    }
  }

  return {
    // 状态
    distributorInfo,
    teamMembers,
    commissionRecords,
    withdrawRecords,
    isLoading,
    hasMore,
    currentPage,
    pageSize,
    
    // 计算属性
    isDistributor,
    distributorLevel,
    distributorLevelText,
    totalCommission,
    availableCommission,
    teamStats,
    
    // 方法
    getDistributorInfo,
    applyDistributor,
    getTeamMembers,
    getCommissionRecords,
    getWithdrawRecords,
    applyWithdraw,
    generateQrCode,
    getPromotionLink,
    getDistributionStats,
    resetDistribution,
    loadMore
  }
})
