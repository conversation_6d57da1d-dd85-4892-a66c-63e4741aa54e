<template>
	<view class="loading-container" v-if="visible">
		<view class="loading-mask" v-if="mask" @click="handleMaskClick"></view>
		<view class="loading-content" :class="{ 'loading-inline': !mask }">
			<!-- 加载动画 -->
			<view class="loading-spinner" :class="spinnerClass">
				<view class="spinner-dot" v-for="n in 12" :key="n" :style="getDotStyle(n)"></view>
			</view>
			
			<!-- 加载文本 -->
			<text class="loading-text" v-if="text">{{ text }}</text>
		</view>
	</view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Props定义
interface Props {
	visible?: boolean
	text?: string
	mask?: boolean
	maskClosable?: boolean
	size?: 'small' | 'medium' | 'large'
	color?: string
}

const props = withDefaults(defineProps<Props>(), {
	visible: false,
	text: '加载中...',
	mask: true,
	maskClosable: false,
	size: 'medium',
	color: '#007AFF'
})

// Emits定义
const emit = defineEmits<{
	maskClick: []
}>()

// 计算属性
const spinnerClass = computed(() => {
	return `loading-spinner-${props.size}`
})

// 方法
const handleMaskClick = () => {
	if (props.maskClosable) {
		emit('maskClick')
	}
}

const getDotStyle = (index: number) => {
	const delay = (index - 1) * 0.1
	return {
		'animation-delay': `${delay}s`,
		'background-color': props.color
	}
}
</script>

<style lang="scss" scoped>
.loading-container {
	position: relative;
	
	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 9998;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.loading-content {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 9999;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: rgba(0, 0, 0, 0.8);
		border-radius: 8px;
		padding: 20px;
		min-width: 100px;
		
		&.loading-inline {
			position: relative;
			top: auto;
			left: auto;
			transform: none;
			background-color: transparent;
			padding: 10px;
		}
	}
	
	.loading-spinner {
		position: relative;
		display: inline-block;
		
		&.loading-spinner-small {
			width: 20px;
			height: 20px;
		}
		
		&.loading-spinner-medium {
			width: 30px;
			height: 30px;
		}
		
		&.loading-spinner-large {
			width: 40px;
			height: 40px;
		}
		
		.spinner-dot {
			position: absolute;
			width: 2px;
			height: 25%;
			background-color: #007AFF;
			border-radius: 1px;
			animation: loading-spin 1.2s linear infinite;
			transform-origin: center 200%;
			
			&:nth-child(1) { transform: rotate(0deg); }
			&:nth-child(2) { transform: rotate(30deg); }
			&:nth-child(3) { transform: rotate(60deg); }
			&:nth-child(4) { transform: rotate(90deg); }
			&:nth-child(5) { transform: rotate(120deg); }
			&:nth-child(6) { transform: rotate(150deg); }
			&:nth-child(7) { transform: rotate(180deg); }
			&:nth-child(8) { transform: rotate(210deg); }
			&:nth-child(9) { transform: rotate(240deg); }
			&:nth-child(10) { transform: rotate(270deg); }
			&:nth-child(11) { transform: rotate(300deg); }
			&:nth-child(12) { transform: rotate(330deg); }
		}
	}
	
	.loading-text {
		color: #fff;
		font-size: 14px;
		margin-top: 12px;
		text-align: center;
		
		.loading-inline & {
			color: #666;
		}
	}
}

@keyframes loading-spin {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0.2;
	}
}
</style>
