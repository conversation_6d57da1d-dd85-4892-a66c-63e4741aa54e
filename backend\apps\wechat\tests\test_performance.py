"""
微信模块性能测试
"""
import time
from django.test import TestCase, TransactionTestCase
from django.test.utils import override_settings
from django.core.cache import cache
from django.contrib.auth import get_user_model
from unittest.mock import patch

from apps.wechat.models import WechatConfig, WechatUser, WechatMessage
from apps.wechat.auth import WechatAuth

User = get_user_model()


class WechatConfigCacheTest(TestCase):
    """微信配置缓存性能测试"""
    
    def setUp(self):
        self.config = WechatConfig.objects.create(
            platform='official',
            name='default',
            app_id='test_app_id',
            app_secret='test_app_secret',
            token='test_token'
        )
    
    def test_config_cache_performance(self):
        """测试配置缓存性能"""
        # 清除缓存
        cache.clear()
        
        # 第一次获取配置（从数据库）
        start_time = time.time()
        config1 = WechatConfig.get_config('official', 'default')
        first_query_time = time.time() - start_time
        
        # 第二次获取配置（从缓存）
        start_time = time.time()
        config2 = WechatConfig.get_config('official', 'default')
        second_query_time = time.time() - start_time
        
        # 验证结果一致
        self.assertEqual(config1, config2)
        
        # 缓存查询应该更快
        self.assertLess(second_query_time, first_query_time)
        
        # 缓存查询应该在1ms内完成
        self.assertLess(second_query_time, 0.001)
    
    def test_config_cache_invalidation(self):
        """测试配置缓存失效"""
        # 获取配置并缓存
        config1 = WechatConfig.get_config('official', 'default')
        self.assertEqual(config1['app_id'], 'test_app_id')
        
        # 更新配置
        self.config.app_id = 'updated_app_id'
        self.config.save()
        
        # 获取更新后的配置
        config2 = WechatConfig.get_config('official', 'default')
        self.assertEqual(config2['app_id'], 'updated_app_id')
    
    def test_platform_configs_cache(self):
        """测试平台配置缓存"""
        # 创建多个配置
        WechatConfig.objects.create(
            platform='official',
            name='test1',
            app_id='test_app_id_1'
        )
        WechatConfig.objects.create(
            platform='official',
            name='test2',
            app_id='test_app_id_2'
        )
        
        # 清除缓存
        cache.clear()
        
        # 第一次获取平台配置
        start_time = time.time()
        configs1 = WechatConfig.get_platform_configs('official')
        first_query_time = time.time() - start_time
        
        # 第二次获取平台配置
        start_time = time.time()
        configs2 = WechatConfig.get_platform_configs('official')
        second_query_time = time.time() - start_time
        
        # 验证结果一致
        self.assertEqual(configs1, configs2)
        self.assertEqual(len(configs1), 3)  # 包括setUp中创建的配置
        
        # 缓存查询应该更快
        self.assertLess(second_query_time, first_query_time)


class WechatUserQueryPerformanceTest(TransactionTestCase):
    """微信用户查询性能测试"""
    
    def setUp(self):
        # 创建大量测试用户
        self.users = []
        for i in range(100):
            django_user = User.objects.create_user(
                username=f'testuser{i}',
                email=f'test{i}@example.com'
            )
            wechat_user = WechatUser.objects.create(
                user=django_user,
                openid=f'test_openid_{i}',
                nickname=f'测试用户{i}',
                subscribe=i % 2 == 0  # 一半用户关注
            )
            self.users.append(wechat_user)
    
    def test_user_query_performance(self):
        """测试用户查询性能"""
        # 测试按openid查询
        start_time = time.time()
        user = WechatUser.objects.get(openid='test_openid_50')
        query_time = time.time() - start_time
        
        self.assertEqual(user.nickname, '测试用户50')
        # 单个用户查询应该在10ms内完成
        self.assertLess(query_time, 0.01)
    
    def test_user_list_query_performance(self):
        """测试用户列表查询性能"""
        # 测试关注用户查询
        start_time = time.time()
        subscribed_users = list(WechatUser.objects.filter(subscribe=True))
        query_time = time.time() - start_time
        
        self.assertEqual(len(subscribed_users), 50)
        # 列表查询应该在50ms内完成
        self.assertLess(query_time, 0.05)
    
    def test_user_with_related_query_performance(self):
        """测试关联查询性能"""
        # 测试select_related优化
        start_time = time.time()
        users_with_django_user = list(
            WechatUser.objects.select_related('user').all()[:10]
        )
        optimized_query_time = time.time() - start_time
        
        # 测试未优化查询
        start_time = time.time()
        users_without_optimization = list(WechatUser.objects.all()[:10])
        # 访问关联对象触发额外查询
        for user in users_without_optimization:
            _ = user.user.username
        unoptimized_query_time = time.time() - start_time
        
        # 优化后的查询应该更快
        self.assertLess(optimized_query_time, unoptimized_query_time)


class WechatMessagePerformanceTest(TransactionTestCase):
    """微信消息性能测试"""
    
    def setUp(self):
        # 创建测试用户
        django_user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>'
        )
        self.wechat_user = WechatUser.objects.create(
            user=django_user,
            openid='test_openid',
            nickname='测试用户'
        )
        
        # 创建大量消息
        self.messages = []
        for i in range(1000):
            message = WechatMessage.objects.create(
                wechat_user=self.wechat_user,
                msg_type='text',
                content=f'测试消息{i}',
                msg_id=f'msg_{i}'
            )
            self.messages.append(message)
    
    def test_message_pagination_performance(self):
        """测试消息分页性能"""
        # 测试分页查询
        start_time = time.time()
        page1 = list(WechatMessage.objects.order_by('-created_at')[:20])
        query_time = time.time() - start_time
        
        self.assertEqual(len(page1), 20)
        # 分页查询应该在20ms内完成
        self.assertLess(query_time, 0.02)
    
    def test_message_search_performance(self):
        """测试消息搜索性能"""
        # 测试内容搜索
        start_time = time.time()
        search_results = list(
            WechatMessage.objects.filter(content__icontains='测试消息1')[:10]
        )
        search_time = time.time() - start_time
        
        self.assertGreater(len(search_results), 0)
        # 搜索应该在50ms内完成
        self.assertLess(search_time, 0.05)
    
    def test_message_aggregation_performance(self):
        """测试消息聚合性能"""
        from django.db.models import Count
        
        # 测试按类型聚合
        start_time = time.time()
        type_counts = WechatMessage.objects.values('msg_type').annotate(
            count=Count('id')
        )
        list(type_counts)  # 执行查询
        aggregation_time = time.time() - start_time
        
        # 聚合查询应该在30ms内完成
        self.assertLess(aggregation_time, 0.03)


class WechatAuthPerformanceTest(TestCase):
    """微信认证性能测试"""
    
    def setUp(self):
        WechatConfig.objects.create(
            platform='official',
            name='default',
            app_id='test_app_id',
            app_secret='test_app_secret'
        )
        self.wechat_auth = WechatAuth()
    
    @patch('apps.wechat.auth.requests.get')
    def test_access_token_cache_performance(self, mock_get):
        """测试access_token缓存性能"""
        mock_response = type('MockResponse', (), {
            'json': lambda: {
                'access_token': 'test_access_token',
                'expires_in': 7200
            }
        })()
        mock_get.return_value = mock_response
        
        # 第一次获取token
        start_time = time.time()
        token1 = self.wechat_auth.get_access_token()
        first_request_time = time.time() - start_time
        
        # 第二次获取token（应该从缓存获取）
        start_time = time.time()
        token2 = self.wechat_auth.get_access_token()
        second_request_time = time.time() - start_time
        
        # 验证token一致
        self.assertEqual(token1, token2)
        
        # 缓存获取应该更快
        self.assertLess(second_request_time, first_request_time)
        
        # 只应该调用一次API
        self.assertEqual(mock_get.call_count, 1)
    
    def test_user_creation_performance(self):
        """测试用户创建性能"""
        user_info = {
            'openid': 'test_openid',
            'nickname': '测试用户',
            'sex': 1,
            'city': '北京',
            'province': '北京',
            'country': '中国',
            'headimgurl': 'http://example.com/avatar.jpg',
            'subscribe': 1
        }
        
        # 测试用户创建时间
        start_time = time.time()
        wechat_user = self.wechat_auth.create_or_update_user(user_info)
        creation_time = time.time() - start_time
        
        self.assertEqual(wechat_user.openid, 'test_openid')
        # 用户创建应该在100ms内完成
        self.assertLess(creation_time, 0.1)
        
        # 测试用户更新时间
        user_info['nickname'] = '更新用户'
        start_time = time.time()
        updated_user = self.wechat_auth.create_or_update_user(user_info)
        update_time = time.time() - start_time
        
        self.assertEqual(updated_user.nickname, '更新用户')
        # 用户更新应该在50ms内完成
        self.assertLess(update_time, 0.05)
