{"version": 3, "file": "profile.js", "sources": ["pages/user/profile.uvue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9wcm9maWxlLnV2dWU"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 用户信息区域 -->\n\t\t<view class=\"user-header\">\n\t\t\t<view class=\"user-info\" @click=\"handleLogin\" v-if=\"!isLogin\">\n\t\t\t\t<image class=\"avatar\" src=\"/static/default-avatar.png\" mode=\"aspectFill\" />\n\t\t\t\t<view class=\"user-details\">\n\t\t\t\t\t<text class=\"username\">点击登录</text>\n\t\t\t\t\t<text class=\"user-desc\">登录后享受更多服务</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"user-info\" v-else>\n\t\t\t\t<image class=\"avatar\" :src=\"userInfo.avatar || '/static/default-avatar.png'\" mode=\"aspectFill\" />\n\t\t\t\t<view class=\"user-details\">\n\t\t\t\t\t<text class=\"username\">{{ userInfo.nickname || userInfo.username }}</text>\n\t\t\t\t\t<text class=\"user-desc\" v-if=\"userInfo.is_distributor\">分销商 - 等级{{ userInfo.distributor_level }}</text>\n\t\t\t\t\t<text class=\"user-desc\" v-else>普通用户</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"user-actions\">\n\t\t\t\t<Icon name=\"arrow-right\" size=\"16\" color=\"#999\" />\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 订单统计 -->\n\t\t<view class=\"order-stats\" v-if=\"isLogin\">\n\t\t\t<view class=\"stats-title\">\n\t\t\t\t<text class=\"title-text\">我的订单</text>\n\t\t\t\t<view class=\"view-all\" @click=\"goToOrders\">\n\t\t\t\t\t<text class=\"view-text\">查看全部</text>\n\t\t\t\t\t<Icon name=\"arrow-right\" size=\"12\" color=\"#999\" />\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"stats-grid\">\n\t\t\t\t<view class=\"stats-item\" @click=\"goToOrders('pending')\">\n\t\t\t\t\t<Icon name=\"clock\" size=\"24\" color=\"#ff9500\" />\n\t\t\t\t\t<text class=\"stats-text\">待付款</text>\n\t\t\t\t\t<text class=\"stats-count\">{{ orderStats.pending }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stats-item\" @click=\"goToOrders('paid')\">\n\t\t\t\t\t<Icon name=\"truck\" size=\"24\" color=\"#007aff\" />\n\t\t\t\t\t<text class=\"stats-text\">待发货</text>\n\t\t\t\t\t<text class=\"stats-count\">{{ orderStats.paid }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stats-item\" @click=\"goToOrders('shipped')\">\n\t\t\t\t\t<Icon name=\"package\" size=\"24\" color=\"#34c759\" />\n\t\t\t\t\t<text class=\"stats-text\">待收货</text>\n\t\t\t\t\t<text class=\"stats-count\">{{ orderStats.shipped }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stats-item\" @click=\"goToOrders('completed')\">\n\t\t\t\t\t<Icon name=\"star\" size=\"24\" color=\"#ff3b30\" />\n\t\t\t\t\t<text class=\"stats-text\">待评价</text>\n\t\t\t\t\t<text class=\"stats-count\">{{ orderStats.completed }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 功能菜单 -->\n\t\t<view class=\"menu-section\">\n\t\t\t<view class=\"menu-group\">\n\t\t\t\t<view class=\"menu-item\" @click=\"goToDistribution\" v-if=\"isLogin\">\n\t\t\t\t\t<view class=\"menu-icon\">\n\t\t\t\t\t\t<Icon name=\"team\" size=\"20\" color=\"#007aff\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"menu-text\">分销中心</text>\n\t\t\t\t\t<view class=\"menu-extra\">\n\t\t\t\t\t\t<text class=\"extra-text\" v-if=\"userInfo.is_distributor\">已开通</text>\n\t\t\t\t\t\t<Icon name=\"arrow-right\" size=\"12\" color=\"#999\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"menu-item\" @click=\"goToCoupons\">\n\t\t\t\t\t<view class=\"menu-icon\">\n\t\t\t\t\t\t<Icon name=\"gift\" size=\"20\" color=\"#ff9500\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"menu-text\">优惠券</text>\n\t\t\t\t\t<view class=\"menu-extra\">\n\t\t\t\t\t\t<Icon name=\"arrow-right\" size=\"12\" color=\"#999\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"menu-item\" @click=\"goToAddress\" v-if=\"isLogin\">\n\t\t\t\t\t<view class=\"menu-icon\">\n\t\t\t\t\t\t<Icon name=\"location\" size=\"20\" color=\"#34c759\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"menu-text\">收货地址</text>\n\t\t\t\t\t<view class=\"menu-extra\">\n\t\t\t\t\t\t<Icon name=\"arrow-right\" size=\"12\" color=\"#999\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"menu-group\">\n\t\t\t\t<view class=\"menu-item\" @click=\"goToService\">\n\t\t\t\t\t<view class=\"menu-icon\">\n\t\t\t\t\t\t<Icon name=\"service\" size=\"20\" color=\"#5856d6\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"menu-text\">客服中心</text>\n\t\t\t\t\t<view class=\"menu-extra\">\n\t\t\t\t\t\t<Icon name=\"arrow-right\" size=\"12\" color=\"#999\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"menu-item\" @click=\"goToFeedback\">\n\t\t\t\t\t<view class=\"menu-icon\">\n\t\t\t\t\t\t<Icon name=\"feedback\" size=\"20\" color=\"#af52de\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"menu-text\">意见反馈</text>\n\t\t\t\t\t<view class=\"menu-extra\">\n\t\t\t\t\t\t<Icon name=\"arrow-right\" size=\"12\" color=\"#999\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"menu-item\" @click=\"goToAbout\">\n\t\t\t\t\t<view class=\"menu-icon\">\n\t\t\t\t\t\t<Icon name=\"info\" size=\"20\" color=\"#8e8e93\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"menu-text\">关于我们</text>\n\t\t\t\t\t<view class=\"menu-extra\">\n\t\t\t\t\t\t<Icon name=\"arrow-right\" size=\"12\" color=\"#999\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 退出登录 -->\n\t\t<view class=\"logout-section\" v-if=\"isLogin\">\n\t\t\t<view class=\"logout-btn\" @click=\"handleLogout\">\n\t\t\t\t<text class=\"logout-text\">退出登录</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup lang=\"uts\">\nimport { ref, computed, onMounted } from 'vue'\nimport Icon from '@/components/common/Icon.uvue'\n\n// 模拟登录状态\nconst isLogin = ref(false)\nconst userInfo = ref({\n\tnickname: '测试用户',\n\tusername: 'testuser',\n\tavatar: '',\n\tis_distributor: false,\n\tdistributor_level: 0\n})\n\n// 订单统计数据\nconst orderStats = ref({\n\tpending: 2,\n\tpaid: 1,\n\tshipped: 3,\n\tcompleted: 5\n})\n\n// 生命周期\nonMounted(() => {\n\tinitPage()\n})\n\n// 方法\nconst initPage = async () => {\n\tuni.__f__('log','at pages/user/profile.uvue:160','初始化用户中心')\n\t// 这里可以添加数据初始化逻辑\n}\n\nconst handleLogin = async () => {\n\t// 模拟登录\n\tisLogin.value = true\n\tuni.showToast({\n\t\ttitle: '登录成功',\n\t\ticon: 'success'\n\t})\n}\n\nconst handleLogout = () => {\n\tuni.showModal({\n\t\ttitle: '提示',\n\t\tcontent: '确定要退出登录吗？',\n\t\tsuccess: (res) => {\n\t\t\tif (res.confirm) {\n\t\t\t\tisLogin.value = false\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '已退出登录',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t})\n}\n\nconst goToOrders = (status?: string) => {\n\tconst url = status ? `/pages/order/list?status=${status}` : '/pages/order/list'\n\tuni.navigateTo({ url })\n}\n\nconst goToDistribution = () => {\n\tuni.navigateTo({\n\t\turl: '/pages/distribution/index'\n\t})\n}\n\nconst goToCoupons = () => {\n\tuni.navigateTo({\n\t\turl: '/pages/coupons/list'\n\t})\n}\n\nconst goToAddress = () => {\n\tuni.navigateTo({\n\t\turl: '/pages/address/list'\n\t})\n}\n\nconst goToService = () => {\n\tuni.navigateTo({\n\t\turl: '/pages/service/index'\n\t})\n}\n\nconst goToFeedback = () => {\n\tuni.navigateTo({\n\t\turl: '/pages/feedback/index'\n\t})\n}\n\nconst goToAbout = () => {\n\tuni.navigateTo({\n\t\turl: '/pages/about/index'\n\t})\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n}\n\n// 用户信息区域\n.user-header {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20px 15px;\n\tbackground: linear-gradient(135deg, #007aff 0%, #5856d6 100%);\n\t\n\t.user-info {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\t\n\t\t.avatar {\n\t\t\twidth: 60px;\n\t\t\theight: 60px;\n\t\t\tborder-radius: 30px;\n\t\t\tmargin-right: 15px;\n\t\t\tborder: 2px solid rgba(255, 255, 255, 0.3);\n\t\t}\n\t\t\n\t\t.user-details {\n\t\t\t.username {\n\t\t\t\tfont-size: 18px;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tcolor: #fff;\n\t\t\t\tmargin-bottom: 4px;\n\t\t\t}\n\t\t\t\n\t\t\t.user-desc {\n\t\t\t\tfont-size: 12px;\n\t\t\t\tcolor: rgba(255, 255, 255, 0.8);\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.user-actions {\n\t\tpadding: 10px;\n\t}\n}\n\n// 订单统计\n.order-stats {\n\tmargin: 10px 15px;\n\tbackground-color: #fff;\n\tborder-radius: 8px;\n\tpadding: 15px;\n\t\n\t.stats-title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: 15px;\n\t\t\n\t\t.title-text {\n\t\t\tfont-size: 16px;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #333;\n\t\t}\n\t\t\n\t\t.view-all {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\t\n\t\t\t.view-text {\n\t\t\t\tfont-size: 12px;\n\t\t\t\tcolor: #999;\n\t\t\t\tmargin-right: 4px;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.stats-grid {\n\t\tdisplay: flex;\n\t\t\n\t\t.stats-item {\n\t\t\tflex: 1;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tposition: relative;\n\t\t\t\n\t\t\t&:not(:last-child)::after {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\tright: 0;\n\t\t\t\ttop: 10px;\n\t\t\t\tbottom: 10px;\n\t\t\t\twidth: 1px;\n\t\t\t\tbackground-color: #eee;\n\t\t\t}\n\t\t\t\n\t\t\t.stats-text {\n\t\t\t\tfont-size: 12px;\n\t\t\t\tcolor: #666;\n\t\t\t\tmargin: 8px 0 4px;\n\t\t\t}\n\t\t\t\n\t\t\t.stats-count {\n\t\t\t\tfont-size: 16px;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tcolor: #333;\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 功能菜单\n.menu-section {\n\tmargin: 10px 15px;\n\t\n\t.menu-group {\n\t\tbackground-color: #fff;\n\t\tborder-radius: 8px;\n\t\tmargin-bottom: 10px;\n\t\toverflow: hidden;\n\t\t\n\t\t.menu-item {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tpadding: 15px;\n\t\t\tborder-bottom: 1px solid #f5f5f5;\n\t\t\t\n\t\t\t&:last-child {\n\t\t\t\tborder-bottom: none;\n\t\t\t}\n\t\t\t\n\t\t\t.menu-icon {\n\t\t\t\twidth: 40px;\n\t\t\t\theight: 40px;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tbackground-color: #f8f8f8;\n\t\t\t\tborder-radius: 20px;\n\t\t\t\tmargin-right: 12px;\n\t\t\t}\n\t\t\t\n\t\t\t.menu-text {\n\t\t\t\tflex: 1;\n\t\t\t\tfont-size: 14px;\n\t\t\t\tcolor: #333;\n\t\t\t}\n\t\t\t\n\t\t\t.menu-extra {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\t\n\t\t\t\t.extra-text {\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\tcolor: #999;\n\t\t\t\t\tmargin-right: 8px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 退出登录\n.logout-section {\n\tmargin: 20px 15px;\n\t\n\t.logout-btn {\n\t\theight: 44px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 8px;\n\t\t\n\t\t.logout-text {\n\t\t\tfont-size: 16px;\n\t\t\tcolor: #ff3b30;\n\t\t}\n\t}\n}\n</style>\n", "import MiniProgramPage from 'F:/zmkj-system/frontend/pages/user/profile.uvue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "__awaiter", "uni"], "mappings": ";;;;;;AAoIA,MAAO,OAAU,MAAA;;;;AAGjB,UAAM,UAAUA,kBAAI,KAAK;AACzB,UAAM,WAAWA,kBAAI,IAAA,cAAA;AAAA,MACpB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,IACnB,CAAA,CAAA;AAGD,UAAM,aAAaA,kBAAI,IAAA,cAAA;AAAA,MACtB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,IACX,CAAA,CAAA;AAGDC,kBAAAA,UAAU,MAAA;AACT;IACD,CAAC;AAGD,UAAM,WAAW,MAAA;AAAA,aAAAC,cAAA,UAAA,MAAA,QAAA,QAAA,aAAA;AAChBC,sBAAAA,MAAI,MAAM,OAAM,kCAAiC,SAAS;AAAA,MAE1D,CAAA;AAAA;AAED,UAAM,cAAc,MAAA;AAAA,aAAAD,cAAA,UAAA,MAAA,QAAA,QAAA,aAAA;AAEnB,gBAAQ,QAAQ;AAChBC,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACN,CAAA;AAAA,MACD,CAAA;AAAA;AAED,UAAM,eAAe,MAAA;AACpBA,0BAAI,UAAU,IAAA,cAAA;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAG;AACZ,cAAI,IAAI,SAAS;AAChB,oBAAQ,QAAQ;AAChBA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACN,CAAA;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAA,CAAA;AAAA,IACF;AAEA,UAAM,aAAa,CAAC,SAAe,SAAA;AAClC,YAAM,MAAM,SAAS,4BAA4B,MAAM,KAAK;AAC5DA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACvB;AAEA,UAAM,mBAAmB,MAAA;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACF;AAEA,UAAM,cAAc,MAAA;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACF;AAEA,UAAM,cAAc,MAAA;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACF;AAEA,UAAM,cAAc,MAAA;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACF;AAEA,UAAM,eAAe,MAAA;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACF;AAEA,UAAM,YAAY,MAAA;AACjBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClOA,GAAG,WAAW,eAAe;"}