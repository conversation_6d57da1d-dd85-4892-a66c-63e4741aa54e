<template>
	<view class="container">
		<!-- 用户信息区域 -->
		<view class="user-header">
			<view class="user-info" @click="handleLogin" v-if="!isLogin">
				<image class="avatar" src="/static/default-avatar.png" mode="aspectFill" />
				<view class="user-details">
					<text class="username">点击登录</text>
					<text class="user-desc">登录后享受更多服务</text>
				</view>
			</view>
			<view class="user-info" v-else>
				<image class="avatar" :src="userInfo.avatar || '/static/default-avatar.png'" mode="aspectFill" />
				<view class="user-details">
					<text class="username">{{ userInfo.nickname || userInfo.username }}</text>
					<text class="user-desc" v-if="userInfo.is_distributor">分销商 - 等级{{ userInfo.distributor_level }}</text>
					<text class="user-desc" v-else>普通用户</text>
				</view>
			</view>
			<view class="user-actions">
				<Icon name="arrow-right" size="16" color="#999" />
			</view>
		</view>

		<!-- 订单统计 -->
		<view class="order-stats" v-if="isLogin">
			<view class="stats-title">
				<text class="title-text">我的订单</text>
				<view class="view-all" @click="goToOrders">
					<text class="view-text">查看全部</text>
					<Icon name="arrow-right" size="12" color="#999" />
				</view>
			</view>
			<view class="stats-grid">
				<view class="stats-item" @click="goToOrders('pending')">
					<Icon name="clock" size="24" color="#ff9500" />
					<text class="stats-text">待付款</text>
					<text class="stats-count">{{ orderStats.pending }}</text>
				</view>
				<view class="stats-item" @click="goToOrders('paid')">
					<Icon name="truck" size="24" color="#007aff" />
					<text class="stats-text">待发货</text>
					<text class="stats-count">{{ orderStats.paid }}</text>
				</view>
				<view class="stats-item" @click="goToOrders('shipped')">
					<Icon name="package" size="24" color="#34c759" />
					<text class="stats-text">待收货</text>
					<text class="stats-count">{{ orderStats.shipped }}</text>
				</view>
				<view class="stats-item" @click="goToOrders('completed')">
					<Icon name="star" size="24" color="#ff3b30" />
					<text class="stats-text">待评价</text>
					<text class="stats-count">{{ orderStats.completed }}</text>
				</view>
			</view>
		</view>

		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="menu-group">
				<view class="menu-item" @click="goToDistribution" v-if="isLogin">
					<view class="menu-icon">
						<Icon name="team" size="20" color="#007aff" />
					</view>
					<text class="menu-text">分销中心</text>
					<view class="menu-extra">
						<text class="extra-text" v-if="userInfo.is_distributor">已开通</text>
						<Icon name="arrow-right" size="12" color="#999" />
					</view>
				</view>
				<view class="menu-item" @click="goToCoupons">
					<view class="menu-icon">
						<Icon name="gift" size="20" color="#ff9500" />
					</view>
					<text class="menu-text">优惠券</text>
					<view class="menu-extra">
						<Icon name="arrow-right" size="12" color="#999" />
					</view>
				</view>
				<view class="menu-item" @click="goToAddress" v-if="isLogin">
					<view class="menu-icon">
						<Icon name="location" size="20" color="#34c759" />
					</view>
					<text class="menu-text">收货地址</text>
					<view class="menu-extra">
						<Icon name="arrow-right" size="12" color="#999" />
					</view>
				</view>
			</view>

			<view class="menu-group">
				<view class="menu-item" @click="goToService">
					<view class="menu-icon">
						<Icon name="service" size="20" color="#5856d6" />
					</view>
					<text class="menu-text">客服中心</text>
					<view class="menu-extra">
						<Icon name="arrow-right" size="12" color="#999" />
					</view>
				</view>
				<view class="menu-item" @click="goToFeedback">
					<view class="menu-icon">
						<Icon name="feedback" size="20" color="#af52de" />
					</view>
					<text class="menu-text">意见反馈</text>
					<view class="menu-extra">
						<Icon name="arrow-right" size="12" color="#999" />
					</view>
				</view>
				<view class="menu-item" @click="goToAbout">
					<view class="menu-icon">
						<Icon name="info" size="20" color="#8e8e93" />
					</view>
					<text class="menu-text">关于我们</text>
					<view class="menu-extra">
						<Icon name="arrow-right" size="12" color="#999" />
					</view>
				</view>
			</view>
		</view>

		<!-- 退出登录 -->
		<view class="logout-section" v-if="isLogin">
			<view class="logout-btn" @click="handleLogout">
				<text class="logout-text">退出登录</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import Icon from '@/components/common/Icon.uvue'

// 模拟登录状态
const isLogin = ref(false)
const userInfo = ref({
	nickname: '测试用户',
	username: 'testuser',
	avatar: '',
	is_distributor: false,
	distributor_level: 0
})

// 订单统计数据
const orderStats = ref({
	pending: 2,
	paid: 1,
	shipped: 3,
	completed: 5
})

// 生命周期
onMounted(() => {
	initPage()
})

// 方法
const initPage = async () => {
	console.log('初始化用户中心')
	// 这里可以添加数据初始化逻辑
}

const handleLogin = async () => {
	// 模拟登录
	isLogin.value = true
	uni.showToast({
		title: '登录成功',
		icon: 'success'
	})
}

const handleLogout = () => {
	uni.showModal({
		title: '提示',
		content: '确定要退出登录吗？',
		success: (res) => {
			if (res.confirm) {
				isLogin.value = false
				uni.showToast({
					title: '已退出登录',
					icon: 'success'
				})
			}
		}
	})
}

const goToOrders = (status?: string) => {
	const url = status ? `/pages/order/list?status=${status}` : '/pages/order/list'
	uni.navigateTo({ url })
}

const goToDistribution = () => {
	uni.navigateTo({
		url: '/pages/distribution/index'
	})
}

const goToCoupons = () => {
	uni.navigateTo({
		url: '/pages/coupons/list'
	})
}

const goToAddress = () => {
	uni.navigateTo({
		url: '/pages/address/list'
	})
}

const goToService = () => {
	uni.navigateTo({
		url: '/pages/service/index'
	})
}

const goToFeedback = () => {
	uni.navigateTo({
		url: '/pages/feedback/index'
	})
}

const goToAbout = () => {
	uni.navigateTo({
		url: '/pages/about/index'
	})
}
</script>

<style lang="scss" scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

// 用户信息区域
.user-header {
	display: flex;
	align-items: center;
	padding: 20px 15px;
	background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
	
	.user-info {
		flex: 1;
		display: flex;
		align-items: center;
		
		.avatar {
			width: 60px;
			height: 60px;
			border-radius: 30px;
			margin-right: 15px;
			border: 2px solid rgba(255, 255, 255, 0.3);
		}
		
		.user-details {
			.username {
				font-size: 18px;
				font-weight: 600;
				color: #fff;
				margin-bottom: 4px;
			}
			
			.user-desc {
				font-size: 12px;
				color: rgba(255, 255, 255, 0.8);
			}
		}
	}
	
	.user-actions {
		padding: 10px;
	}
}

// 订单统计
.order-stats {
	margin: 10px 15px;
	background-color: #fff;
	border-radius: 8px;
	padding: 15px;
	
	.stats-title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 15px;
		
		.title-text {
			font-size: 16px;
			font-weight: 600;
			color: #333;
		}
		
		.view-all {
			display: flex;
			align-items: center;
			
			.view-text {
				font-size: 12px;
				color: #999;
				margin-right: 4px;
			}
		}
	}
	
	.stats-grid {
		display: flex;
		
		.stats-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			position: relative;
			
			&:not(:last-child)::after {
				content: '';
				position: absolute;
				right: 0;
				top: 10px;
				bottom: 10px;
				width: 1px;
				background-color: #eee;
			}
			
			.stats-text {
				font-size: 12px;
				color: #666;
				margin: 8px 0 4px;
			}
			
			.stats-count {
				font-size: 16px;
				font-weight: 600;
				color: #333;
			}
		}
	}
}

// 功能菜单
.menu-section {
	margin: 10px 15px;
	
	.menu-group {
		background-color: #fff;
		border-radius: 8px;
		margin-bottom: 10px;
		overflow: hidden;
		
		.menu-item {
			display: flex;
			align-items: center;
			padding: 15px;
			border-bottom: 1px solid #f5f5f5;
			
			&:last-child {
				border-bottom: none;
			}
			
			.menu-icon {
				width: 40px;
				height: 40px;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: #f8f8f8;
				border-radius: 20px;
				margin-right: 12px;
			}
			
			.menu-text {
				flex: 1;
				font-size: 14px;
				color: #333;
			}
			
			.menu-extra {
				display: flex;
				align-items: center;
				
				.extra-text {
					font-size: 12px;
					color: #999;
					margin-right: 8px;
				}
			}
		}
	}
}

// 退出登录
.logout-section {
	margin: 20px 15px;
	
	.logout-btn {
		height: 44px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #fff;
		border-radius: 8px;
		
		.logout-text {
			font-size: 16px;
			color: #ff3b30;
		}
	}
}
</style>
