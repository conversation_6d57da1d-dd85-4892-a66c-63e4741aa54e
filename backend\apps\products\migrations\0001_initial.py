# Generated by Django 5.2.4 on 2025-07-25 09:19

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductAttribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('name', models.CharField(help_text='属性标识名', max_length=100, verbose_name='属性名称')),
                ('display_name', models.CharField(help_text='前台显示名称', max_length=100, verbose_name='显示名称')),
                ('attribute_type', models.CharField(choices=[('text', '文本'), ('number', '数字'), ('boolean', '布尔值'), ('choice', '选择')], default='text', help_text='属性值类型', max_length=20, verbose_name='属性类型')),
                ('choices', models.TextField(blank=True, help_text='选择类型的可选项，一行一个', verbose_name='选择项')),
                ('is_required', models.BooleanField(default=False, help_text='前台是否必须填写', verbose_name='是否必填')),
                ('sort_order', models.IntegerField(default=0, help_text='显示顺序', verbose_name='排序')),
            ],
            options={
                'verbose_name': '产品属性',
                'verbose_name_plural': '产品属性',
                'ordering': ['sort_order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('name', models.CharField(help_text='产品分类名称', max_length=100, verbose_name='分类名称')),
                ('description', models.TextField(blank=True, help_text='分类详细描述', verbose_name='分类描述')),
                ('sort_order', models.IntegerField(default=0, help_text='数字越小越靠前', verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, help_text='是否在前台显示', verbose_name='是否启用')),
                ('icon', models.CharField(blank=True, help_text='分类图标(emoji)', max_length=50, verbose_name='图标')),
                ('parent', models.ForeignKey(blank=True, help_text='上级分类', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='products.productcategory', verbose_name='父分类')),
            ],
            options={
                'verbose_name': '产品分类',
                'verbose_name_plural': '产品分类',
                'ordering': ['sort_order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('name', models.CharField(help_text='产品标题', max_length=200, verbose_name='产品名称')),
                ('subtitle', models.CharField(blank=True, help_text='产品副标题', max_length=255, verbose_name='副标题')),
                ('description', models.TextField(help_text='产品详细描述', verbose_name='产品描述')),
                ('sku', models.CharField(help_text='商品唯一编码', max_length=100, unique=True, verbose_name='商品编码')),
                ('price', models.DecimalField(decimal_places=2, help_text='销售价格', max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='价格')),
                ('original_price', models.DecimalField(blank=True, decimal_places=2, help_text='划线价格', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0)], verbose_name='原价')),
                ('cost_price', models.DecimalField(blank=True, decimal_places=2, help_text='采购成本', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0)], verbose_name='成本价')),
                ('stock', models.IntegerField(default=0, help_text='当前库存', validators=[django.core.validators.MinValueValidator(0)], verbose_name='库存数量')),
                ('min_stock', models.IntegerField(default=0, help_text='库存预警线', validators=[django.core.validators.MinValueValidator(0)], verbose_name='最低库存')),
                ('weight', models.DecimalField(blank=True, decimal_places=3, help_text='商品重量', max_digits=8, null=True, verbose_name='重量(kg)')),
                ('status', models.CharField(choices=[('draft', '草稿'), ('active', '上架'), ('inactive', '下架'), ('soldout', '售罄')], default='draft', help_text='商品当前状态', max_length=20, verbose_name='商品状态')),
                ('is_featured', models.BooleanField(default=False, help_text='首页推荐商品', verbose_name='是否推荐')),
                ('sort_order', models.IntegerField(default=0, help_text='数字越小越靠前', verbose_name='排序')),
                ('view_count', models.IntegerField(default=0, help_text='商品浏览统计', verbose_name='浏览次数')),
                ('sale_count', models.IntegerField(default=0, help_text='累计销售数量', verbose_name='销售数量')),
                ('tags', models.CharField(blank=True, help_text='商品标签，逗号分隔', max_length=500, verbose_name='标签')),
                ('seo_title', models.CharField(blank=True, help_text='搜索引擎标题', max_length=255, verbose_name='SEO标题')),
                ('seo_keywords', models.CharField(blank=True, help_text='搜索关键词', max_length=255, verbose_name='SEO关键词')),
                ('seo_description', models.TextField(blank=True, help_text='搜索引擎描述', verbose_name='SEO描述')),
                ('category', models.ForeignKey(help_text='所属分类', on_delete=django.db.models.deletion.PROTECT, related_name='products', to='products.productcategory', verbose_name='产品分类')),
            ],
            options={
                'verbose_name': '产品',
                'verbose_name_plural': '产品',
                'ordering': ['-is_featured', 'sort_order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('image', models.ImageField(help_text='产品展示图片', upload_to='products/%Y/%m/%d/', verbose_name='产品图片')),
                ('alt_text', models.CharField(blank=True, help_text='图片替代文字', max_length=200, verbose_name='图片描述')),
                ('is_primary', models.BooleanField(default=False, help_text='产品主要展示图片', verbose_name='是否主图')),
                ('sort_order', models.IntegerField(default=0, help_text='显示顺序', verbose_name='排序')),
                ('product', models.ForeignKey(help_text='所属产品', on_delete=django.db.models.deletion.CASCADE, related_name='images', to='products.product', verbose_name='关联产品')),
            ],
            options={
                'verbose_name': '产品图片',
                'verbose_name_plural': '产品图片',
                'ordering': ['-is_primary', 'sort_order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='ProductReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('rating', models.IntegerField(choices=[(1, '⭐'), (2, '⭐⭐'), (3, '⭐⭐⭐'), (4, '⭐⭐⭐⭐'), (5, '⭐⭐⭐⭐⭐')], help_text='1-5星评分', verbose_name='评分')),
                ('title', models.CharField(blank=True, help_text='评价标题', max_length=200, verbose_name='评价标题')),
                ('content', models.TextField(help_text='详细评价内容', verbose_name='评价内容')),
                ('is_verified_purchase', models.BooleanField(default=False, help_text='是否为实际购买用户的评价', verbose_name='已验证购买')),
                ('is_approved', models.BooleanField(default=True, help_text='是否通过审核', verbose_name='已审核')),
                ('helpful_count', models.IntegerField(default=0, help_text='认为有用的用户数', verbose_name='有用数')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='products.product', verbose_name='关联产品')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='评价用户')),
            ],
            options={
                'verbose_name': '产品评价',
                'verbose_name_plural': '产品评价',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductAttributeValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('value', models.TextField(help_text='具体的属性值', verbose_name='属性值')),
                ('attribute', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.productattribute', verbose_name='产品属性')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attribute_values', to='products.product', verbose_name='关联产品')),
            ],
            options={
                'verbose_name': '产品属性值',
                'verbose_name_plural': '产品属性值',
                'indexes': [models.Index(fields=['product', 'attribute'], name='products_pr_product_c00cf1_idx')],
                'unique_together': {('product', 'attribute')},
            },
        ),
        migrations.AddIndex(
            model_name='productcategory',
            index=models.Index(fields=['parent', 'is_active'], name='products_pr_parent__d76081_idx'),
        ),
        migrations.AddIndex(
            model_name='productcategory',
            index=models.Index(fields=['sort_order'], name='products_pr_sort_or_34ffcf_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['category', 'status'], name='products_pr_categor_75eeb5_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['status', 'is_featured'], name='products_pr_status_28f1d7_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['sku'], name='products_pr_sku_ca0cdc_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['sort_order'], name='products_pr_sort_or_f6f8f4_idx'),
        ),
        migrations.AddIndex(
            model_name='productimage',
            index=models.Index(fields=['product', 'is_primary'], name='products_pr_product_1b7905_idx'),
        ),
        migrations.AddIndex(
            model_name='productreview',
            index=models.Index(fields=['product', 'is_approved'], name='products_pr_product_160d92_idx'),
        ),
        migrations.AddIndex(
            model_name='productreview',
            index=models.Index(fields=['user'], name='products_pr_user_id_c2c9ad_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='productreview',
            unique_together={('product', 'user')},
        ),
    ]
