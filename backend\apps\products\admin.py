from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from apps.core.admin import admin_site
from .models import (
    ProductCategory, 
    Product, 
    ProductImage, 
    ProductAttribute, 
    ProductAttributeValue,
    ProductReview
)


class ProductCategoryAdmin(admin.ModelAdmin):
    """产品分类管理"""
    list_display = ('name', 'get_full_path', 'icon_display', 'parent', 'sort_order', 'is_active', 'product_count', 'created_at')
    list_filter = ('is_active', 'parent', 'created_at')
    search_fields = ('name', 'description')
    list_editable = ('sort_order', 'is_active')
    ordering = ('sort_order', 'id')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'parent', 'icon')
        }),
        ('显示设置', {
            'fields': ('sort_order', 'is_active')
        }),
    )
    
    def get_full_path(self, obj):
        """显示完整分类路径"""
        return obj.get_full_name()
    get_full_path.short_description = '分类路径'
    
    def icon_display(self, obj):
        """显示图标"""
        if obj.icon:
            return format_html('<span style="font-size:20px;">{}</span>', obj.icon)
        return '-'
    icon_display.short_description = '图标'
    
    def product_count(self, obj):
        """显示产品数量"""
        count = obj.products.count()
        if count > 0:
            url = reverse('admin:products_product_changelist') + f'?category__id__exact={obj.id}'
            return format_html('<a href="{}">{} 个产品</a>', url, count)
        return '0 个产品'
    product_count.short_description = '产品数量'


class ProductImageInline(admin.TabularInline):
    """产品图片内联管理"""
    model = ProductImage
    extra = 3
    fields = ('image', 'alt_text', 'is_primary', 'sort_order')
    readonly_fields = ('preview',)
    
    def preview(self, obj):
        """图片预览"""
        if obj.image:
            return format_html('<img src="{}" style="max-width:100px;max-height:100px;" />', obj.image.url)
        return '-'
    preview.short_description = '预览'


class ProductAttributeValueInline(admin.TabularInline):
    """产品属性值内联管理"""
    model = ProductAttributeValue
    extra = 5
    fields = ('attribute', 'value')


class ProductAdmin(admin.ModelAdmin):
    """产品管理"""
    list_display = (
        'name', 'category', 'sku', 'price_display', 'stock_status', 
        'status', 'is_featured', 'view_count', 'sale_count', 'created_at'
    )
    list_filter = ('status', 'category', 'is_featured', 'created_at')
    search_fields = ('name', 'sku', 'description')
    list_editable = ('status', 'is_featured')
    readonly_fields = ('view_count', 'sale_count', 'primary_image_preview')
    ordering = ('-created_at',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'subtitle', 'category', 'sku', 'description')
        }),
        ('价格库存', {
            'fields': ('price', 'original_price', 'cost_price', 'stock', 'min_stock', 'weight')
        }),
        ('状态设置', {
            'fields': ('status', 'is_featured', 'sort_order')
        }),
        ('SEO设置', {
            'fields': ('seo_title', 'seo_keywords', 'seo_description'),
            'classes': ('collapse',)
        }),
        ('其他信息', {
            'fields': ('tags', 'view_count', 'sale_count'),
            'classes': ('collapse',)
        }),
        ('图片预览', {
            'fields': ('primary_image_preview',),
            'classes': ('collapse',)
        }),
    )
    
    inlines = [ProductImageInline, ProductAttributeValueInline]
    
    def price_display(self, obj):
        """价格显示"""
        if obj.original_price and obj.original_price > obj.price:
            discount = obj.get_discount_rate()
            return format_html(
                '¥{} <del style="color:#999;">¥{}</del> <span style="color:#e74c3c;">{}折</span>',
                obj.price, obj.original_price, discount
            )
        return f'¥{obj.price}'
    price_display.short_description = '价格'
    
    def stock_status(self, obj):
        """库存状态"""
        if obj.stock <= 0:
            return format_html('<span style="color:#e74c3c;">缺货</span>')
        elif obj.is_low_stock():
            return format_html('<span style="color:#f39c12;">库存不足 ({})</span>', obj.stock)
        else:
            return format_html('<span style="color:#27ae60;">正常 ({})</span>', obj.stock)
    stock_status.short_description = '库存状态'
    
    def primary_image_preview(self, obj):
        """主图预览"""
        primary_image = obj.images.filter(is_primary=True).first()
        if primary_image and primary_image.image:
            return format_html('<img src="{}" style="max-width:200px;max-height:200px;" />', primary_image.image.url)
        return '暂无主图'
    primary_image_preview.short_description = '主图预览'
    
    actions = ['make_active', 'make_inactive', 'make_featured']
    
    def make_active(self, request, queryset):
        """批量上架"""
        queryset.update(status='active')
        self.message_user(request, f'已上架 {queryset.count()} 个产品')
    make_active.short_description = '批量上架选中的产品'
    
    def make_inactive(self, request, queryset):
        """批量下架"""
        queryset.update(status='inactive')
        self.message_user(request, f'已下架 {queryset.count()} 个产品')
    make_inactive.short_description = '批量下架选中的产品'
    
    def make_featured(self, request, queryset):
        """批量设为推荐"""
        queryset.update(is_featured=True)
        self.message_user(request, f'已设置 {queryset.count()} 个产品为推荐')
    make_featured.short_description = '批量设为推荐产品'


class ProductImageAdmin(admin.ModelAdmin):
    """产品图片管理"""
    list_display = ('product', 'image_preview', 'alt_text', 'is_primary', 'sort_order', 'created_at')
    list_filter = ('is_primary', 'created_at')
    search_fields = ('product__name', 'alt_text')
    list_editable = ('is_primary', 'sort_order')
    ordering = ('product', '-is_primary', 'sort_order')
    
    def image_preview(self, obj):
        """图片预览"""
        if obj.image:
            return format_html('<img src="{}" style="max-width:80px;max-height:80px;" />', obj.image.url)
        return '-'
    image_preview.short_description = '预览'


class ProductAttributeAdmin(admin.ModelAdmin):
    """产品属性管理"""
    list_display = ('display_name', 'name', 'attribute_type', 'is_required', 'sort_order')
    list_filter = ('attribute_type', 'is_required')
    search_fields = ('name', 'display_name')
    list_editable = ('sort_order',)
    ordering = ('sort_order', 'id')


class ProductAttributeValueAdmin(admin.ModelAdmin):
    """产品属性值管理"""
    list_display = ('product', 'attribute', 'value', 'created_at')
    list_filter = ('attribute', 'created_at')
    search_fields = ('product__name', 'attribute__display_name', 'value')
    ordering = ('product', 'attribute')


class ProductReviewAdmin(admin.ModelAdmin):
    """产品评价管理"""
    list_display = ('product', 'user', 'rating_display', 'title', 'is_verified_purchase', 'is_approved', 'helpful_count', 'created_at')
    list_filter = ('rating', 'is_verified_purchase', 'is_approved', 'created_at')
    search_fields = ('product__name', 'user__username', 'title', 'content')
    list_editable = ('is_approved',)
    readonly_fields = ('helpful_count',)
    ordering = ('-created_at',)
    
    fieldsets = (
        ('评价信息', {
            'fields': ('product', 'user', 'rating', 'title', 'content')
        }),
        ('状态设置', {
            'fields': ('is_verified_purchase', 'is_approved', 'helpful_count')
        }),
    )
    
    def rating_display(self, obj):
        """评分显示"""
        stars = '⭐' * obj.rating
        return format_html('<span title="{}分">{}</span>', obj.rating, stars)
    rating_display.short_description = '评分'
    
    actions = ['approve_reviews', 'disapprove_reviews']
    
    def approve_reviews(self, request, queryset):
        """批量审核通过"""
        queryset.update(is_approved=True)
        self.message_user(request, f'已审核通过 {queryset.count()} 条评价')
    approve_reviews.short_description = '批量审核通过选中的评价'
    
    def disapprove_reviews(self, request, queryset):
        """批量审核拒绝"""
        queryset.update(is_approved=False)
        self.message_user(request, f'已拒绝 {queryset.count()} 条评价')
    disapprove_reviews.short_description = '批量拒绝选中的评价'


# 注册到自定义管理站点
admin_site.register(ProductCategory, ProductCategoryAdmin)
admin_site.register(Product, ProductAdmin)
admin_site.register(ProductImage, ProductImageAdmin)
admin_site.register(ProductAttribute, ProductAttributeAdmin)
admin_site.register(ProductAttributeValue, ProductAttributeValueAdmin)
admin_site.register(ProductReview, ProductReviewAdmin)