# -*- coding: utf-8 -*-
"""
智梦科技系统 - AI功能URL配置

AI功能模块的URL路由配置
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'ai'

# DRF路由器
router = DefaultRouter()
router.register(r'services', views.AIServiceConfigViewSet, basename='services')
router.register(r'tasks', views.AITaskViewSet, basename='tasks')
router.register(r'conversations', views.ChatbotConversationViewSet, basename='conversations')
router.register(r'behavior-logs', views.UserBehaviorLogViewSet, basename='behavior-logs')

urlpatterns = [
    # API路由
    path('api/v1/', include(router.urls)),
    
    # 智能客服相关
    path('api/v1/chatbot/start/', views.ChatbotStartView.as_view(), name='chatbot-start'),
    path('api/v1/chatbot/message/', views.ChatbotMessageView.as_view(), name='chatbot-message'),
    path('api/v1/chatbot/close/', views.ChatbotCloseView.as_view(), name='chatbot-close'),
    path('api/v1/chatbot/history/<str:session_id>/', views.ChatbotHistoryView.as_view(), name='chatbot-history'),
    
    # 推荐系统相关
    path('api/v1/recommendations/', views.RecommendationView.as_view(), name='recommendations'),
    path('api/v1/recommendations/config/', views.RecommendationConfigView.as_view(), name='recommendation-config'),
    
    # 用户行为分析
    path('api/v1/behavior/log/', views.BehaviorLogView.as_view(), name='behavior-log'),
    path('api/v1/behavior/stats/', views.BehaviorStatsView.as_view(), name='behavior-stats'),
    path('api/v1/behavior/popular/', views.PopularTargetsView.as_view(), name='popular-targets'),
    
    # AI任务管理
    path('api/v1/tasks/create/', views.CreateTaskView.as_view(), name='create-task'),
    path('api/v1/tasks/<int:task_id>/start/', views.StartTaskView.as_view(), name='start-task'),
    path('api/v1/tasks/<int:task_id>/complete/', views.CompleteTaskView.as_view(), name='complete-task'),
    path('api/v1/tasks/<int:task_id>/fail/', views.FailTaskView.as_view(), name='fail-task'),
    
    # AI服务状态
    path('api/v1/status/', views.AIStatusView.as_view(), name='ai-status'),
    path('api/v1/health/', views.AIHealthCheckView.as_view(), name='ai-health'),
]
