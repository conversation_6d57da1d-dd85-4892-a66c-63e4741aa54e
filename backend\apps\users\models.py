from django.db import models
from django.contrib.auth.models import AbstractUser
from apps.core.models import BaseModel as TimeStampedModel

class User(AbstractUser, TimeStampedModel):
    """扩展用户模型，增加额外的用户信息字段"""
    phone = models.CharField(max_length=11, unique=True, blank=True, null=True, help_text="手机号码")
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True, help_text="用户头像")
    nickname = models.CharField(max_length=50, blank=True, null=True, help_text="用户昵称")
    gender = models.CharField(max_length=10, choices=[('male', '男'), ('female', '女'), ('other', '其他')], blank=True, null=True, help_text="性别")
    birth_date = models.DateField(blank=True, null=True, help_text="出生日期")
    is_verified = models.BooleanField(default=False, help_text="是否已验证")
    wechat_openid = models.CharField(max_length=100, unique=True, blank=True, null=True, help_text="微信OpenID")
    wechat_unionid = models.CharField(max_length=100, unique=True, blank=True, null=True, help_text="微信UnionID")

    class Meta:
        verbose_name = "用户"
        verbose_name_plural = "用户"
        indexes = [
            models.Index(fields=['phone']),
            models.Index(fields=['wechat_openid']),
            models.Index(fields=['wechat_unionid']),
        ]

    def __str__(self):
        return self.username or self.phone or str(self.id)


class UserAddress(TimeStampedModel):
    """用户地址模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='addresses', help_text="关联用户")
    recipient_name = models.CharField(max_length=50, help_text="收件人姓名")
    recipient_phone = models.CharField(max_length=11, help_text="收件人电话")
    province = models.CharField(max_length=20, help_text="省份")
    city = models.CharField(max_length=20, help_text="城市")
    district = models.CharField(max_length=20, help_text="区/县")
    detailed_address = models.CharField(max_length=200, help_text="详细地址")
    postal_code = models.CharField(max_length=6, blank=True, null=True, help_text="邮政编码")
    is_default = models.BooleanField(default=False, help_text="是否默认地址")

    class Meta:
        verbose_name = "用户地址"
        verbose_name_plural = "用户地址"
        indexes = [
            models.Index(fields=['user', 'is_default']),
        ]

    def __str__(self):
        return f"{self.user.username}的地址: {self.province}{self.city}{self.district}{self.detailed_address}"


class UserLoginLog(TimeStampedModel):
    """用户登录日志模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='login_logs', help_text="关联用户")
    ip_address = models.GenericIPAddressField(help_text="登录IP地址")
    login_time = models.DateTimeField(auto_now_add=True, help_text="登录时间")
    logout_time = models.DateTimeField(blank=True, null=True, help_text="登出时间")
    device_info = models.CharField(max_length=200, blank=True, null=True, help_text="设备信息")
    browser_info = models.CharField(max_length=200, blank=True, null=True, help_text="浏览器信息")

    class Meta:
        verbose_name = "用户登录日志"
        verbose_name_plural = "用户登录日志"
        indexes = [
            models.Index(fields=['user', '-login_time']),
            models.Index(fields=['ip_address']),
        ]

    def __str__(self):
        return f"{self.user.username}于{self.login_time}登录，IP: {self.ip_address}"