from rest_framework import serializers
from .models import (
    ProductCategory, 
    Product, 
    ProductImage, 
    ProductAttribute, 
    ProductAttributeValue,
    ProductReview
)


class ProductCategorySerializer(serializers.ModelSerializer):
    """产品分类序列化器"""
    children = serializers.SerializerMethodField()
    product_count = serializers.SerializerMethodField()
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = ProductCategory
        fields = [
            'id', 'name', 'description', 'parent', 'sort_order', 
            'is_active', 'icon', 'children', 'product_count', 'full_name',
            'created_at', 'updated_at'
        ]
    
    def get_children(self, obj):
        """获取子分类"""
        if hasattr(obj, 'children'):
            children = obj.children.filter(is_active=True).order_by('sort_order')
            return ProductCategorySerializer(children, many=True, context=self.context).data
        return []
    
    def get_product_count(self, obj):
        """获取产品数量"""
        return obj.products.filter(status='active').count()
    
    def get_full_name(self, obj):
        """获取完整分类名称"""
        return obj.get_full_name()


class ProductImageSerializer(serializers.ModelSerializer):
    """产品图片序列化器"""
    
    class Meta:
        model = ProductImage
        fields = [
            'id', 'image', 'alt_text', 'is_primary', 'sort_order'
        ]


class ProductAttributeSerializer(serializers.ModelSerializer):
    """产品属性序列化器"""
    
    class Meta:
        model = ProductAttribute
        fields = [
            'id', 'name', 'display_name', 'attribute_type', 
            'choices', 'is_required', 'sort_order'
        ]


class ProductAttributeValueSerializer(serializers.ModelSerializer):
    """产品属性值序列化器"""
    attribute = ProductAttributeSerializer(read_only=True)
    attribute_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = ProductAttributeValue
        fields = [
            'id', 'attribute', 'attribute_id', 'value'
        ]


class ProductReviewSerializer(serializers.ModelSerializer):
    """产品评价序列化器"""
    user_name = serializers.CharField(source='user.username', read_only=True)
    user_avatar = serializers.URLField(source='user.avatar', read_only=True)
    rating_display = serializers.SerializerMethodField()
    
    class Meta:
        model = ProductReview
        fields = [
            'id', 'user', 'user_name', 'user_avatar', 'rating', 
            'rating_display', 'title', 'content', 'is_verified_purchase',
            'helpful_count', 'created_at'
        ]
        read_only_fields = ['user', 'is_verified_purchase', 'helpful_count']
    
    def get_rating_display(self, obj):
        """获取星级显示"""
        return '⭐' * obj.rating


class ProductListSerializer(serializers.ModelSerializer):
    """产品列表序列化器（简化版）"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    primary_image = serializers.SerializerMethodField()
    discount_rate = serializers.SerializerMethodField()
    average_rating = serializers.SerializerMethodField()
    review_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'subtitle', 'price', 'original_price', 
            'category_name', 'primary_image', 'discount_rate',
            'is_featured', 'view_count', 'sale_count',
            'average_rating', 'review_count', 'created_at'
        ]
    
    def get_primary_image(self, obj):
        """获取主图"""
        primary_image = obj.images.filter(is_primary=True).first()
        if primary_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(primary_image.image.url)
            return primary_image.image.url
        return None
    
    def get_discount_rate(self, obj):
        """获取折扣率"""
        return obj.get_discount_rate()
    
    def get_average_rating(self, obj):
        """获取平均评分"""
        reviews = obj.reviews.filter(is_approved=True)
        if reviews:
            return round(sum(r.rating for r in reviews) / len(reviews), 1)
        return 0
    
    def get_review_count(self, obj):
        """获取评价数量"""
        return obj.reviews.filter(is_approved=True).count()


class ProductDetailSerializer(serializers.ModelSerializer):
    """产品详情序列化器"""
    category = ProductCategorySerializer(read_only=True)
    images = ProductImageSerializer(many=True, read_only=True)
    attribute_values = ProductAttributeValueSerializer(many=True, read_only=True)
    reviews = ProductReviewSerializer(many=True, read_only=True)
    discount_rate = serializers.SerializerMethodField()
    is_in_stock = serializers.SerializerMethodField()
    is_low_stock = serializers.SerializerMethodField()
    average_rating = serializers.SerializerMethodField()
    review_count = serializers.SerializerMethodField()
    stock_status = serializers.SerializerMethodField()
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'subtitle', 'description', 'category', 'sku',
            'price', 'original_price', 'stock', 'weight', 'status',
            'is_featured', 'view_count', 'sale_count', 'tags',
            'seo_title', 'seo_keywords', 'seo_description',
            'images', 'attribute_values', 'reviews',
            'discount_rate', 'is_in_stock', 'is_low_stock',
            'average_rating', 'review_count', 'stock_status',
            'created_at', 'updated_at'
        ]
    
    def get_discount_rate(self, obj):
        """获取折扣率"""
        return obj.get_discount_rate()
    
    def get_is_in_stock(self, obj):
        """是否有库存"""
        return obj.is_in_stock()
    
    def get_is_low_stock(self, obj):
        """是否库存不足"""
        return obj.is_low_stock()
    
    def get_average_rating(self, obj):
        """获取平均评分"""
        reviews = obj.reviews.filter(is_approved=True)
        if reviews:
            return round(sum(r.rating for r in reviews) / len(reviews), 1)
        return 0
    
    def get_review_count(self, obj):
        """获取评价数量"""
        return obj.reviews.filter(is_approved=True).count()
    
    def get_stock_status(self, obj):
        """库存状态"""
        if obj.stock <= 0:
            return {'status': 'out_of_stock', 'text': '缺货'}
        elif obj.is_low_stock():
            return {'status': 'low_stock', 'text': f'库存不足 ({obj.stock})'}
        else:
            return {'status': 'in_stock', 'text': f'现货 ({obj.stock})'}


class ProductCreateUpdateSerializer(serializers.ModelSerializer):
    """产品创建/更新序列化器"""
    images = ProductImageSerializer(many=True, read_only=True)
    attribute_values = ProductAttributeValueSerializer(many=True, required=False)
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'subtitle', 'description', 'category', 'sku',
            'price', 'original_price', 'cost_price', 'stock', 'min_stock',
            'weight', 'status', 'is_featured', 'sort_order', 'tags',
            'seo_title', 'seo_keywords', 'seo_description',
            'images', 'attribute_values'
        ]
    
    def create(self, validated_data):
        """创建产品"""
        attribute_values_data = validated_data.pop('attribute_values', [])
        product = Product.objects.create(**validated_data)
        
        # 创建属性值
        for attr_data in attribute_values_data:
            ProductAttributeValue.objects.create(product=product, **attr_data)
        
        return product
    
    def update(self, instance, validated_data):
        """更新产品"""
        attribute_values_data = validated_data.pop('attribute_values', [])
        
        # 更新产品基本信息
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # 更新属性值
        if attribute_values_data:
            # 删除现有属性值
            instance.attribute_values.all().delete()
            # 创建新的属性值
            for attr_data in attribute_values_data:
                ProductAttributeValue.objects.create(product=instance, **attr_data)
        
        return instance


class ProductSearchSerializer(serializers.Serializer):
    """产品搜索序列化器"""
    keyword = serializers.CharField(required=False, help_text="搜索关键词")
    category = serializers.IntegerField(required=False, help_text="分类ID")
    min_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, help_text="最低价格")
    max_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, help_text="最高价格")
    sort_by = serializers.ChoiceField(
        choices=[
            ('created_at', '最新'),
            ('-created_at', '最新'),
            ('price', '价格从低到高'),
            ('-price', '价格从高到低'),
            ('-sale_count', '销量'),
            ('-view_count', '人气'),
        ],
        required=False,
        help_text="排序方式"
    )
    is_featured = serializers.BooleanField(required=False, help_text="是否推荐商品") 