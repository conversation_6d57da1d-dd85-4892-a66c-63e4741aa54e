"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Math) {
  common_vendor.unref(Icon)();
}
const Icon = () => "../../components/common/Icon.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "list",
  setup(__props) {
    const loading = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const currentFilter = common_vendor.ref("all");
    const products = common_vendor.ref([
      new UTSJSONObject({
        id: 1,
        name: "夏季清爽T恤 纯棉透气 多色可选",
        price: 89,
        originalPrice: 129,
        image: "https://imggw.zmkj.live/products/tshirt1.jpg",
        isHot: true,
        isNew: false,
        sales: 1234
      }),
      new UTSJSONObject({
        id: 2,
        name: "无线蓝牙耳机 降噪立体声 长续航",
        price: 299,
        originalPrice: null,
        image: "https://imggw.zmkj.live/products/earphone1.jpg",
        isHot: false,
        isNew: true,
        sales: 567
      }),
      new UTSJSONObject({
        id: 3,
        name: "简约家居摆件 北欧风格 装饰品",
        price: 59,
        originalPrice: 89,
        image: "https://imggw.zmkj.live/products/decoration1.jpg",
        isHot: true,
        isNew: false,
        sales: 890
      }),
      new UTSJSONObject({
        id: 4,
        name: "天然护肤套装 补水保湿 温和无刺激",
        price: 199,
        originalPrice: 299,
        image: "https://imggw.zmkj.live/products/skincare1.jpg",
        isHot: false,
        isNew: true,
        sales: 345
      }),
      new UTSJSONObject({
        id: 5,
        name: "运动休闲鞋 透气舒适 防滑耐磨",
        price: 159,
        originalPrice: 199,
        image: "https://imggw.zmkj.live/products/shoes1.jpg",
        isHot: true,
        isNew: false,
        sales: 678
      }),
      new UTSJSONObject({
        id: 6,
        name: "智能手环 健康监测 运动记录",
        price: 399,
        originalPrice: null,
        image: "https://imggw.zmkj.live/products/watch1.jpg",
        isHot: false,
        isNew: true,
        sales: 234
      })
    ]);
    common_vendor.onMounted(() => {
      initPage();
    });
    const initPage = () => {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        common_vendor.index.__f__("log", "at pages/products/list.uvue:143", "初始化商品列表页");
      });
    };
    const goToSearch = () => {
      common_vendor.index.navigateTo({
        url: "/pages/products/search"
      });
    };
    const handleFilter = (filter) => {
      currentFilter.value = filter;
    };
    const handleProductClick = (product = null) => {
      common_vendor.index.navigateTo({
        url: `/pages/products/detail?id=${product.id}`
      });
    };
    return (_ctx = null, _cache = null) => {
      const __returned__ = common_vendor.e(new UTSJSONObject({
        a: common_vendor.p(new UTSJSONObject({
          name: "search",
          size: "16",
          color: "#999"
        })),
        b: common_vendor.o(goToSearch),
        c: currentFilter.value === "all" ? 1 : "",
        d: common_vendor.o(($event = null) => {
          return handleFilter("all");
        }),
        e: currentFilter.value === "hot" ? 1 : "",
        f: common_vendor.o(($event = null) => {
          return handleFilter("hot");
        }),
        g: currentFilter.value === "new" ? 1 : "",
        h: common_vendor.o(($event = null) => {
          return handleFilter("new");
        }),
        i: common_vendor.p(new UTSJSONObject({
          name: "arrow-down",
          size: "12",
          color: "#999"
        })),
        j: currentFilter.value === "price" ? 1 : "",
        k: common_vendor.o(($event = null) => {
          return handleFilter("price");
        }),
        l: common_vendor.f(products.value, (product = null, index = null, i0 = null) => {
          return common_vendor.e(new UTSJSONObject({
            a: product.image,
            b: common_vendor.t(product.name),
            c: common_vendor.t(product.price),
            d: product.originalPrice
          }), product.originalPrice ? new UTSJSONObject({
            e: common_vendor.t(product.originalPrice)
          }) : new UTSJSONObject({}), new UTSJSONObject({
            f: product.isHot
          }), product.isHot ? new UTSJSONObject({}) : new UTSJSONObject({}), new UTSJSONObject({
            g: product.isNew
          }), product.isNew ? new UTSJSONObject({}) : new UTSJSONObject({}), new UTSJSONObject({
            h: common_vendor.t(product.sales),
            i: index,
            j: common_vendor.o(($event = null) => {
              return handleProductClick(product);
            }, index)
          }));
        }),
        m: hasMore.value
      }), hasMore.value ? common_vendor.e(new UTSJSONObject({
        n: !loading.value
      }), !loading.value ? new UTSJSONObject({}) : new UTSJSONObject({})) : new UTSJSONObject({}), new UTSJSONObject({
        o: products.value.length === 0 && !loading.value
      }), products.value.length === 0 && !loading.value ? new UTSJSONObject({
        p: common_vendor.p(new UTSJSONObject({
          name: "empty",
          size: "80",
          color: "#ccc"
        }))
      }) : new UTSJSONObject({}), new UTSJSONObject({
        q: common_vendor.sei(common_vendor.gei(_ctx, ""), "view")
      }));
      return __returned__;
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7abd9408"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/products/list.js.map
