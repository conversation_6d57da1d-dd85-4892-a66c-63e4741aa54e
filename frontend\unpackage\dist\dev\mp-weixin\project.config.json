{"description": "项目配置文件。", "packOptions": {"ignore": []}, "setting": {"urlCheck": false, "es6": true, "postcss": false, "minified": false, "newFeature": true, "bigPackageSizeSupport": true}, "compileType": "miniprogram", "libVersion": "", "appid": "wx93ee08701f60d728", "projectname": "frontend", "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"current": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}}