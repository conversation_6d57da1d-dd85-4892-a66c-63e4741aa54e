"use strict";
const common_vendor = require("../common/vendor.js");
const MAIN_DOMAIN = "zmkj.nat100.top";
const CDN_DOMAIN = "zmkj.nat100.top";
const UNIFIED_CONFIG = {
  API_BASE_URL: `http://${MAIN_DOMAIN}/api/v1`,
  CDN_BASE_URL: `http://${CDN_DOMAIN}`,
  WS_BASE_URL: `ws://${MAIN_DOMAIN}/ws`,
  APP_NAME: "智梦科技系统",
  VERSION: "1.0.0"
};
const DEV_CONFIG = UNIFIED_CONFIG;
const PROD_CONFIG = UNIFIED_CONFIG;
const TEST_CONFIG = UNIFIED_CONFIG;
function getEnv() {
  const accountInfo = common_vendor.index.getAccountInfoSync();
  const envVersion = accountInfo.miniProgram.envVersion;
  if (envVersion === "release") {
    return "production";
  } else if (envVersion === "trial") {
    return "test";
  } else {
    return "development";
  }
}
function getConfig() {
  const env = getEnv();
  switch (env) {
    case "production":
      return PROD_CONFIG;
    case "test":
      return TEST_CONFIG;
    case "development":
    default:
      return DEV_CONFIG;
  }
}
const CONFIG = getConfig();
function getApiUrl(path) {
  if (!path.startsWith("/")) {
    path = "/" + path;
  }
  return CONFIG.API_BASE_URL + path;
}
function isDev() {
  return getEnv() === "development";
}
function printEnvInfo() {
  const env = getEnv();
  const config = getConfig();
  common_vendor.index.__f__("log", "at utils/config.ts:179", "=== 环境信息 ===");
  common_vendor.index.__f__("log", "at utils/config.ts:180", "当前环境:", env);
  common_vendor.index.__f__("log", "at utils/config.ts:181", "应用名称:", config.APP_NAME);
  common_vendor.index.__f__("log", "at utils/config.ts:182", "版本号:", config.VERSION);
  common_vendor.index.__f__("log", "at utils/config.ts:183", "API地址:", config.API_BASE_URL);
  common_vendor.index.__f__("log", "at utils/config.ts:184", "CDN地址:", config.CDN_BASE_URL);
  common_vendor.index.__f__("log", "at utils/config.ts:185", "WebSocket地址:", config.WS_BASE_URL);
  common_vendor.index.__f__("log", "at utils/config.ts:186", "===============");
}
if (isDev()) {
  printEnvInfo();
}
exports.CONFIG = CONFIG;
exports.getApiUrl = getApiUrl;
exports.getEnv = getEnv;
exports.isDev = isDev;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/config.js.map
