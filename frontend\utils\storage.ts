/**
 * 本地存储工具
 * 封装uni-app的存储API，提供类型安全和错误处理
 */

import { isDev } from './config'

// 存储键名前缀
const STORAGE_PREFIX = 'zmkj_'

/**
 * 获取完整的存储键名
 * @param key 原始键名
 * @returns 带前缀的键名
 */
function getStorageKey(key: string): string {
  return STORAGE_PREFIX + key
}

/**
 * 设置存储数据
 * @param key 存储键名
 * @param value 存储值
 * @returns 是否成功
 */
export function setStorage(key: string, value: any): boolean {
  try {
    const storageKey = getStorageKey(key)
    uni.setStorageSync(storageKey, value)
    
    if (isDev()) {
      console.log(`Storage Set: ${key} =`, value)
    }
    
    return true
  } catch (error) {
    console.error(`设置存储失败 [${key}]:`, error)
    return false
  }
}

/**
 * 获取存储数据
 * @param key 存储键名
 * @param defaultValue 默认值
 * @returns 存储值或默认值
 */
export function getStorage<T = any>(key: string, defaultValue: T | null = null): T | null {
  try {
    const storageKey = getStorageKey(key)
    const value = uni.getStorageSync(storageKey)
    
    if (isDev()) {
      console.log(`Storage Get: ${key} =`, value)
    }
    
    return value !== '' ? value : defaultValue
  } catch (error) {
    console.error(`获取存储失败 [${key}]:`, error)
    return defaultValue
  }
}

/**
 * 移除存储数据
 * @param key 存储键名
 * @returns 是否成功
 */
export function removeStorage(key: string): boolean {
  try {
    const storageKey = getStorageKey(key)
    uni.removeStorageSync(storageKey)
    
    if (isDev()) {
      console.log(`Storage Remove: ${key}`)
    }
    
    return true
  } catch (error) {
    console.error(`移除存储失败 [${key}]:`, error)
    return false
  }
}

/**
 * 检查存储是否存在
 * @param key 存储键名
 * @returns 是否存在
 */
export function hasStorage(key: string): boolean {
  try {
    const storageKey = getStorageKey(key)
    const value = uni.getStorageSync(storageKey)
    return value !== ''
  } catch (error) {
    console.error(`检查存储失败 [${key}]:`, error)
    return false
  }
}

/**
 * 获取所有存储信息
 * @returns 存储信息
 */
export function getStorageInfo(): {
  keys: string[]
  currentSize: number
  limitSize: number
} {
  try {
    const info = uni.getStorageInfoSync()
    return {
      keys: info.keys.filter(key => key.startsWith(STORAGE_PREFIX)),
      currentSize: info.currentSize,
      limitSize: info.limitSize
    }
  } catch (error) {
    console.error('获取存储信息失败:', error)
    return {
      keys: [],
      currentSize: 0,
      limitSize: 0
    }
  }
}

/**
 * 清空所有应用存储
 * @returns 是否成功
 */
export function clearStorage(): boolean {
  try {
    const info = getStorageInfo()
    info.keys.forEach(key => {
      uni.removeStorageSync(key)
    })
    
    if (isDev()) {
      console.log('Storage Cleared')
    }
    
    return true
  } catch (error) {
    console.error('清空存储失败:', error)
    return false
  }
}

/**
 * 异步设置存储数据
 * @param key 存储键名
 * @param value 存储值
 * @returns Promise<boolean>
 */
export function setStorageAsync(key: string, value: any): Promise<boolean> {
  return new Promise((resolve) => {
    try {
      const storageKey = getStorageKey(key)
      uni.setStorage({
        key: storageKey,
        data: value,
        success: () => {
          if (isDev()) {
            console.log(`Storage Set Async: ${key} =`, value)
          }
          resolve(true)
        },
        fail: (error) => {
          console.error(`异步设置存储失败 [${key}]:`, error)
          resolve(false)
        }
      })
    } catch (error) {
      console.error(`异步设置存储失败 [${key}]:`, error)
      resolve(false)
    }
  })
}

/**
 * 异步获取存储数据
 * @param key 存储键名
 * @param defaultValue 默认值
 * @returns Promise<T | null>
 */
export function getStorageAsync<T = any>(key: string, defaultValue: T | null = null): Promise<T | null> {
  return new Promise((resolve) => {
    try {
      const storageKey = getStorageKey(key)
      uni.getStorage({
        key: storageKey,
        success: (res) => {
          if (isDev()) {
            console.log(`Storage Get Async: ${key} =`, res.data)
          }
          resolve(res.data)
        },
        fail: () => {
          resolve(defaultValue)
        }
      })
    } catch (error) {
      console.error(`异步获取存储失败 [${key}]:`, error)
      resolve(defaultValue)
    }
  })
}

/**
 * 异步移除存储数据
 * @param key 存储键名
 * @returns Promise<boolean>
 */
export function removeStorageAsync(key: string): Promise<boolean> {
  return new Promise((resolve) => {
    try {
      const storageKey = getStorageKey(key)
      uni.removeStorage({
        key: storageKey,
        success: () => {
          if (isDev()) {
            console.log(`Storage Remove Async: ${key}`)
          }
          resolve(true)
        },
        fail: (error) => {
          console.error(`异步移除存储失败 [${key}]:`, error)
          resolve(false)
        }
      })
    } catch (error) {
      console.error(`异步移除存储失败 [${key}]:`, error)
      resolve(false)
    }
  })
}

/**
 * 存储管理类
 */
export class StorageManager {
  private prefix: string

  constructor(prefix: string = '') {
    this.prefix = prefix
  }

  private getKey(key: string): string {
    return this.prefix ? `${this.prefix}_${key}` : key
  }

  set(key: string, value: any): boolean {
    return setStorage(this.getKey(key), value)
  }

  get<T = any>(key: string, defaultValue: T | null = null): T | null {
    return getStorage(this.getKey(key), defaultValue)
  }

  remove(key: string): boolean {
    return removeStorage(this.getKey(key))
  }

  has(key: string): boolean {
    return hasStorage(this.getKey(key))
  }

  async setAsync(key: string, value: any): Promise<boolean> {
    return setStorageAsync(this.getKey(key), value)
  }

  async getAsync<T = any>(key: string, defaultValue: T | null = null): Promise<T | null> {
    return getStorageAsync(this.getKey(key), defaultValue)
  }

  async removeAsync(key: string): Promise<boolean> {
    return removeStorageAsync(this.getKey(key))
  }
}

// 创建默认存储管理器实例
export const storage = new StorageManager()

// 创建用户相关存储管理器
export const userStorage = new StorageManager('user')

// 创建应用设置存储管理器
export const appStorage = new StorageManager('app')

// 创建缓存存储管理器
export const cacheStorage = new StorageManager('cache')

/**
 * 缓存数据（带过期时间）
 * @param key 缓存键名
 * @param value 缓存值
 * @param expireTime 过期时间（毫秒）
 */
export function setCache(key: string, value: any, expireTime: number = 30 * 60 * 1000): void {
  const cacheData = {
    value,
    expireTime: Date.now() + expireTime,
    createTime: Date.now()
  }
  cacheStorage.set(key, cacheData)
}

/**
 * 获取缓存数据
 * @param key 缓存键名
 * @param defaultValue 默认值
 * @returns 缓存值或默认值
 */
export function getCache<T = any>(key: string, defaultValue: T | null = null): T | null {
  const cacheData = cacheStorage.get(key)
  
  if (!cacheData || !cacheData.expireTime) {
    return defaultValue
  }
  
  // 检查是否过期
  if (Date.now() > cacheData.expireTime) {
    cacheStorage.remove(key)
    return defaultValue
  }
  
  return cacheData.value
}

/**
 * 移除缓存数据
 * @param key 缓存键名
 */
export function removeCache(key: string): boolean {
  return cacheStorage.remove(key)
}

/**
 * 清空所有缓存
 */
export function clearCache(): void {
  const info = getStorageInfo()
  const cachePrefix = getStorageKey('cache_')
  
  info.keys.forEach(key => {
    if (key.startsWith(cachePrefix)) {
      uni.removeStorageSync(key)
    }
  })
}

export default {
  setStorage,
  getStorage,
  removeStorage,
  hasStorage,
  getStorageInfo,
  clearStorage,
  setStorageAsync,
  getStorageAsync,
  removeStorageAsync,
  StorageManager,
  storage,
  userStorage,
  appStorage,
  cacheStorage,
  setCache,
  getCache,
  removeCache,
  clearCache
}
