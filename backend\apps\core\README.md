# Core 核心模块

## 已完成功能 ✅
- 基础抽象模型 (BaseModel)
  - 创建时间、更新时间字段
  - 逻辑删除功能
- 用户模型扩展 (User)
  - 扩展Django内置用户模型
  - 添加昵称、头像、性别、手机号等字段
- 用户管理API
  - 用户CRUD操作
  - 当前用户信息获取与更新

## 待开发功能 📝
- 权限系统
  - 角色管理
  - 权限分配
- 认证系统
  - JWT认证
  - 第三方登录集成
- 系统设置
  - 全局配置管理
  - 主题设置

## 技术栈
- Django 5.2.4
- Django REST Framework 3.15.1
- PostgreSQL/MySQL (可切换)

## 模型结构
- BaseModel: 所有模型的抽象基类
- User: 扩展Django内置用户模型

## API端点
- GET/POST /api/v1/core/users/ - 用户列表与创建
- GET/PUT/DELETE /api/v1/core/users/{id}/ - 用户详情、更新与删除
- GET /api/v1/core/users/me/ - 获取当前用户信息
- PUT /api/v1/core/users/update_me/ - 更新当前用户信息