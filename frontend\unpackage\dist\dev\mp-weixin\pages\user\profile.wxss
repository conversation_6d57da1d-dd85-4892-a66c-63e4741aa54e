@import "../../uvue.wxss";
:host{display:flex;flex-direction:column}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 图标相关变量 */
/* 图标颜色预设 */
/* 全局图标基础样式 */
.icon-base.data-v-0fb36f05 {
  font-family: "iconfont";
  font-style: normal;
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
}

/* 图标动画 */
@keyframes icon-rotate-0fb36f05 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
.icon-spin.data-v-0fb36f05 {
  animation: icon-rotate-0fb36f05 1s linear infinite;
}
.container.data-v-0fb36f05 {
  background-color: #f5f5f5;
  min-height: 100vh;
}
.user-header.data-v-0fb36f05 {
  display: flex;
  align-items: center;
  padding: 20px 15px;
  background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
}
.user-header .user-info.data-v-0fb36f05 {
  flex: 1;
  display: flex;
  align-items: center;
}
.user-header .user-info .avatar.data-v-0fb36f05 {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  margin-right: 15px;
  border: 2px solid rgba(255, 255, 255, 0.3);
}
.user-header .user-info .user-details .username.data-v-0fb36f05 {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 4px;
}
.user-header .user-info .user-details .user-desc.data-v-0fb36f05 {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}
.user-header .user-actions.data-v-0fb36f05 {
  padding: 10px;
}
.order-stats.data-v-0fb36f05 {
  margin: 10px 15px;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
}
.order-stats .stats-title.data-v-0fb36f05 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}
.order-stats .stats-title .title-text.data-v-0fb36f05 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.order-stats .stats-title .view-all.data-v-0fb36f05 {
  display: flex;
  align-items: center;
}
.order-stats .stats-title .view-all .view-text.data-v-0fb36f05 {
  font-size: 12px;
  color: #999;
  margin-right: 4px;
}
.order-stats .stats-grid.data-v-0fb36f05 {
  display: flex;
}
.order-stats .stats-grid .stats-item.data-v-0fb36f05 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.order-stats .stats-grid .stats-item.data-v-0fb36f05:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 10px;
  bottom: 10px;
  width: 1px;
  background-color: #eee;
}
.order-stats .stats-grid .stats-item .stats-text.data-v-0fb36f05 {
  font-size: 12px;
  color: #666;
  margin: 8px 0 4px;
}
.order-stats .stats-grid .stats-item .stats-count.data-v-0fb36f05 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.menu-section.data-v-0fb36f05 {
  margin: 10px 15px;
}
.menu-section .menu-group.data-v-0fb36f05 {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
  overflow: hidden;
}
.menu-section .menu-group .menu-item.data-v-0fb36f05 {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f5f5f5;
}
.menu-section .menu-group .menu-item.data-v-0fb36f05:last-child {
  border-bottom: none;
}
.menu-section .menu-group .menu-item .menu-icon.data-v-0fb36f05 {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  border-radius: 20px;
  margin-right: 12px;
}
.menu-section .menu-group .menu-item .menu-text.data-v-0fb36f05 {
  flex: 1;
  font-size: 14px;
  color: #333;
}
.menu-section .menu-group .menu-item .menu-extra.data-v-0fb36f05 {
  display: flex;
  align-items: center;
}
.menu-section .menu-group .menu-item .menu-extra .extra-text.data-v-0fb36f05 {
  font-size: 12px;
  color: #999;
  margin-right: 8px;
}
.logout-section.data-v-0fb36f05 {
  margin: 20px 15px;
}
.logout-section .logout-btn.data-v-0fb36f05 {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 8px;
}
.logout-section .logout-btn .logout-text.data-v-0fb36f05 {
  font-size: 16px;
  color: #ff3b30;
}