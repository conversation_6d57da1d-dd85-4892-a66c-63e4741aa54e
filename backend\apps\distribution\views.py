from django.shortcuts import render, get_object_or_404
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Q, Sum, Count
from decimal import Decimal

from .models import (
    DistributionConfig, DistributionLevel, DistributionRelation,
    CommissionRecord, WithdrawalRecord, PromotionCode, MarketingActivity
)
from .serializers import (
    DistributionConfigSerializer, DistributionLevelSerializer, DistributionRelationSerializer,
    CommissionRecordSerializer, WithdrawalRecordSerializer, PromotionCodeSerializer,
    MarketingActivitySerializer, JoinDistributionSerializer, ApplyWithdrawalSerializer
)
from .services import DistributionRelationService

User = get_user_model()


class DistributionLevelViewSet(viewsets.ModelViewSet):
    """分销等级视图集"""
    queryset = DistributionLevel.objects.all()
    serializer_class = DistributionLevelSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """根据用户权限过滤查询集"""
        if self.request.user.is_staff:
            return DistributionLevel.objects.all()
        return DistributionLevel.objects.filter(is_active=True)


class DistributionRelationViewSet(viewsets.ModelViewSet):
    """分销关系视图集"""
    serializer_class = DistributionRelationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """根据用户权限过滤查询集"""
        if self.request.user.is_staff:
            return DistributionRelation.objects.all()
        return DistributionRelation.objects.filter(user=self.request.user)

    @action(detail=False, methods=['get'])
    def my_relation(self, request):
        """获取我的分销关系"""
        try:
            relation = DistributionRelation.objects.get(user=request.user, is_active=True)
            serializer = self.get_serializer(relation)
            return Response(serializer.data)
        except DistributionRelation.DoesNotExist:
            return Response({'detail': '您还不是分销商'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['get'])
    def children(self, request, pk=None):
        """获取下级分销商列表"""
        relation = self.get_object()
        children = relation.children.filter(is_active=True)
        serializer = self.get_serializer(children, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def distribution_tree(self, request, pk=None):
        """获取分销关系树"""
        relation = self.get_object()
        max_depth = int(request.query_params.get('max_depth', 3))
        tree = DistributionRelationService.get_distribution_tree(relation, max_depth)
        return Response(tree)

    @action(detail=True, methods=['get'])
    def team_statistics(self, request, pk=None):
        """获取团队统计信息"""
        relation = self.get_object()
        stats = DistributionRelationService.get_team_statistics(relation)
        return Response(stats)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAdminUser])
    def transfer(self, request, pk=None):
        """转移分销商（管理员功能）"""
        relation = self.get_object()
        to_parent_id = request.data.get('to_parent_id')

        try:
            to_parent = None
            if to_parent_id:
                to_parent = DistributionRelation.objects.get(
                    id=to_parent_id,
                    is_active=True
                )

            updated_relation = DistributionRelationService.transfer_distributor(
                from_parent=relation.parent,
                to_parent=to_parent,
                distributor=relation
            )

            serializer = self.get_serializer(updated_relation)
            return Response(serializer.data)

        except Exception as e:
            return Response({'detail': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAdminUser])
    def deactivate(self, request, pk=None):
        """停用分销商"""
        relation = self.get_object()
        reason = request.data.get('reason', '')

        try:
            updated_relation = DistributionRelationService.deactivate_distributor(relation, reason)
            serializer = self.get_serializer(updated_relation)
            return Response(serializer.data)
        except Exception as e:
            return Response({'detail': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAdminUser])
    def reactivate(self, request, pk=None):
        """重新激活分销商"""
        relation = self.get_object()

        try:
            updated_relation = DistributionRelationService.reactivate_distributor(relation)
            serializer = self.get_serializer(updated_relation)
            return Response(serializer.data)
        except Exception as e:
            return Response({'detail': str(e)}, status=status.HTTP_400_BAD_REQUEST)


class CommissionRecordViewSet(viewsets.ReadOnlyModelViewSet):
    """佣金记录视图集"""
    serializer_class = CommissionRecordSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """根据用户权限过滤查询集"""
        if self.request.user.is_staff:
            return CommissionRecord.objects.all()

        try:
            relation = DistributionRelation.objects.get(user=self.request.user, is_active=True)
            return CommissionRecord.objects.filter(distributor=relation)
        except DistributionRelation.DoesNotExist:
            return CommissionRecord.objects.none()

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """获取佣金汇总信息"""
        try:
            relation = DistributionRelation.objects.get(user=request.user, is_active=True)
            queryset = CommissionRecord.objects.filter(distributor=relation)

            summary = queryset.aggregate(
                total_commission=Sum('commission_amount'),
                pending_commission=Sum('commission_amount', filter=Q(status='pending')),
                confirmed_commission=Sum('commission_amount', filter=Q(status='confirmed')),
                settled_commission=Sum('commission_amount', filter=Q(status='settled')),
                record_count=Count('id')
            )

            return Response(summary)
        except DistributionRelation.DoesNotExist:
            return Response({'detail': '您还不是分销商'}, status=status.HTTP_404_NOT_FOUND)


class WithdrawalRecordViewSet(viewsets.ModelViewSet):
    """提现记录视图集"""
    serializer_class = WithdrawalRecordSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """根据用户权限过滤查询集"""
        if self.request.user.is_staff:
            return WithdrawalRecord.objects.all()

        try:
            relation = DistributionRelation.objects.get(user=self.request.user, is_active=True)
            return WithdrawalRecord.objects.filter(distributor=relation)
        except DistributionRelation.DoesNotExist:
            return WithdrawalRecord.objects.none()


class PromotionCodeViewSet(viewsets.ModelViewSet):
    """推广码视图集"""
    serializer_class = PromotionCodeSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """根据用户权限过滤查询集"""
        if self.request.user.is_staff:
            return PromotionCode.objects.all()

        try:
            relation = DistributionRelation.objects.get(user=self.request.user, is_active=True)
            return PromotionCode.objects.filter(distributor=relation)
        except DistributionRelation.DoesNotExist:
            return PromotionCode.objects.none()


class MarketingActivityViewSet(viewsets.ModelViewSet):
    """营销活动视图集"""
    serializer_class = MarketingActivitySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """根据用户权限过滤查询集"""
        if self.request.user.is_staff:
            return MarketingActivity.objects.all()
        return MarketingActivity.objects.filter(status='active')


class JoinDistributionView(APIView):
    """加入分销系统"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = JoinDistributionSerializer(data=request.data)
        if serializer.is_valid():
            user = request.user
            invitation_code = serializer.validated_data.get('invitation_code')

            try:
                relation = DistributionRelationService.create_distributor(user, invitation_code)
                serializer = DistributionRelationSerializer(relation)
                return Response(serializer.data, status=status.HTTP_201_CREATED)

            except Exception as e:
                return Response({'detail': str(e)}, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class BindParentView(APIView):
    """绑定推荐人"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        invitation_code = request.data.get('invitation_code')
        if not invitation_code:
            return Response({'detail': '请提供邀请码'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            relation = DistributionRelationService.bind_parent(request.user, invitation_code)
            serializer = DistributionRelationSerializer(relation)
            return Response(serializer.data)
        except Exception as e:
            return Response({'detail': str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MyTeamView(APIView):
    """我的团队"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            relation = DistributionRelation.objects.get(user=request.user, is_active=True)

            # 获取直接下级
            direct_children = relation.children.filter(is_active=True)

            # 获取团队统计
            team_stats = {
                'direct_count': direct_children.count(),
                'total_count': relation.get_total_team_count(),
                'total_sales': relation.total_sales,
                'total_commission': relation.total_commission,
            }

            # 序列化直接下级信息
            children_serializer = DistributionRelationSerializer(direct_children, many=True)

            return Response({
                'stats': team_stats,
                'direct_children': children_serializer.data
            })

        except DistributionRelation.DoesNotExist:
            return Response({'detail': '您还不是分销商'}, status=status.HTTP_404_NOT_FOUND)


class MyCommissionView(APIView):
    """我的佣金"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            relation = DistributionRelation.objects.get(user=request.user, is_active=True)

            # 获取佣金统计
            commission_stats = {
                'total_commission': relation.total_commission,
                'available_commission': relation.available_commission,
                'total_sales': relation.total_sales,
            }

            # 获取最近的佣金记录
            recent_records = CommissionRecord.objects.filter(
                distributor=relation
            ).order_by('-created_at')[:10]

            records_serializer = CommissionRecordSerializer(recent_records, many=True)

            return Response({
                'stats': commission_stats,
                'recent_records': records_serializer.data
            })

        except DistributionRelation.DoesNotExist:
            return Response({'detail': '您还不是分销商'}, status=status.HTTP_404_NOT_FOUND)


class ApplyWithdrawalView(APIView):
    """申请提现"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = ApplyWithdrawalSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            try:
                relation = DistributionRelation.objects.get(user=request.user, is_active=True)

                amount = serializer.validated_data['amount']
                method = serializer.validated_data['method']
                account_info = serializer.validated_data['account_info']
                remark = serializer.validated_data.get('remark', '')

                # 检查可提现余额
                if amount > relation.available_commission:
                    return Response({'detail': '提现金额不能超过可提现佣金'}, status=status.HTTP_400_BAD_REQUEST)

                # 计算手续费（这里可以根据实际需求调整）
                fee = amount * Decimal('0.01')  # 1%手续费

                # 创建提现记录
                withdrawal = WithdrawalRecord.objects.create(
                    distributor=relation,
                    amount=amount,
                    fee=fee,
                    method=method,
                    account_info=account_info,
                    remark=remark,
                    status='pending'
                )

                # 冻结相应的佣金
                relation.available_commission -= amount
                relation.save()

                withdrawal_serializer = WithdrawalRecordSerializer(withdrawal)
                return Response(withdrawal_serializer.data, status=status.HTTP_201_CREATED)

            except DistributionRelation.DoesNotExist:
                return Response({'detail': '您还不是分销商'}, status=status.HTTP_404_NOT_FOUND)
            except Exception as e:
                return Response({'detail': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GeneratePromotionCodeView(APIView):
    """生成推广码"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            relation = DistributionRelation.objects.get(user=request.user, is_active=True)

            name = request.data.get('name', '个人推广码')
            code_type = request.data.get('code_type', 'personal')
            description = request.data.get('description', '')

            # 生成推广码
            import random
            import string
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))

            # 确保推广码唯一
            while PromotionCode.objects.filter(code=code).exists():
                code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))

            promotion_code = PromotionCode.objects.create(
                distributor=relation,
                code=code,
                name=name,
                code_type=code_type,
                description=description,
                status='active'
            )

            serializer = PromotionCodeSerializer(promotion_code)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except DistributionRelation.DoesNotExist:
            return Response({'detail': '您还不是分销商'}, status=status.HTTP_404_NOT_FOUND)


class ValidatePromotionCodeView(APIView):
    """验证推广码"""
    permission_classes = [permissions.AllowAny]

    def get(self, request, code):
        try:
            promotion_code = PromotionCode.objects.get(code=code)

            if promotion_code.is_valid():
                serializer = PromotionCodeSerializer(promotion_code)
                return Response({
                    'valid': True,
                    'code_info': serializer.data
                })
            else:
                return Response({
                    'valid': False,
                    'message': '推广码已失效'
                })

        except PromotionCode.DoesNotExist:
            return Response({
                'valid': False,
                'message': '推广码不存在'
            })


class DistributionStatsView(APIView):
    """分销统计概览"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            relation = DistributionRelation.objects.get(user=request.user, is_active=True)

            # 基础统计
            stats = {
                'level_info': {
                    'name': relation.level.name,
                    'level': relation.level.level,
                    'commission_rate': relation.level.commission_rate,
                },
                'sales_stats': {
                    'total_sales': relation.total_sales,
                    'total_commission': relation.total_commission,
                    'available_commission': relation.available_commission,
                },
                'team_stats': {
                    'direct_count': relation.get_children_count(),
                    'total_count': relation.get_total_team_count(),
                    'referral_count': relation.referral_count,
                },
                'invitation_code': relation.invitation_code,
            }

            return Response(stats)

        except DistributionRelation.DoesNotExist:
            return Response({'detail': '您还不是分销商'}, status=status.HTTP_404_NOT_FOUND)


class CommissionStatsView(APIView):
    """佣金统计"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            relation = DistributionRelation.objects.get(user=request.user, is_active=True)

            # 按类型统计佣金
            commission_by_type = CommissionRecord.objects.filter(
                distributor=relation
            ).values('commission_type').annotate(
                total_amount=Sum('commission_amount'),
                count=Count('id')
            )

            # 按状态统计佣金
            commission_by_status = CommissionRecord.objects.filter(
                distributor=relation
            ).values('status').annotate(
                total_amount=Sum('commission_amount'),
                count=Count('id')
            )

            return Response({
                'by_type': list(commission_by_type),
                'by_status': list(commission_by_status)
            })

        except DistributionRelation.DoesNotExist:
            return Response({'detail': '您还不是分销商'}, status=status.HTTP_404_NOT_FOUND)


class TeamStatsView(APIView):
    """团队统计"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            relation = DistributionRelation.objects.get(user=request.user, is_active=True)

            # 获取直接下级的统计信息
            children = relation.children.filter(is_active=True)
            children_stats = []

            for child in children:
                child_stats = {
                    'user_info': {
                        'id': child.user.id,
                        'username': child.user.username,
                        'nickname': getattr(child.user, 'nickname', ''),
                    },
                    'level': child.level.name,
                    'total_sales': child.total_sales,
                    'total_commission': child.total_commission,
                    'referral_count': child.referral_count,
                    'team_count': child.get_total_team_count(),
                    'activated_at': child.activated_at,
                }
                children_stats.append(child_stats)

            return Response({
                'children_count': len(children_stats),
                'children_stats': children_stats
            })

        except DistributionRelation.DoesNotExist:
            return Response({'detail': '您还不是分销商'}, status=status.HTTP_404_NOT_FOUND)


class AdminDashboardView(APIView):
    """管理员仪表板"""
    permission_classes = [permissions.IsAdminUser]

    def get(self, request):
        # 分销商统计
        distributor_stats = {
            'total_distributors': DistributionRelation.objects.filter(is_active=True).count(),
            'new_distributors_today': DistributionRelation.objects.filter(
                is_active=True,
                activated_at__date=timezone.now().date()
            ).count(),
        }

        # 佣金统计
        commission_stats = CommissionRecord.objects.aggregate(
            total_commission=Sum('commission_amount'),
            pending_commission=Sum('commission_amount', filter=Q(status='pending')),
            confirmed_commission=Sum('commission_amount', filter=Q(status='confirmed')),
            settled_commission=Sum('commission_amount', filter=Q(status='settled')),
        )

        # 提现统计
        withdrawal_stats = WithdrawalRecord.objects.aggregate(
            total_withdrawals=Sum('amount'),
            pending_withdrawals=Sum('amount', filter=Q(status='pending')),
            completed_withdrawals=Sum('amount', filter=Q(status='completed')),
        )

        # 按等级统计分销商
        level_stats = DistributionLevel.objects.filter(is_active=True).annotate(
            distributor_count=Count('distributors', filter=Q(distributors__is_active=True))
        ).values('name', 'level', 'distributor_count')

        return Response({
            'distributor_stats': distributor_stats,
            'commission_stats': commission_stats,
            'withdrawal_stats': withdrawal_stats,
            'level_stats': list(level_stats),
        })


class ApproveWithdrawalView(APIView):
    """审批提现"""
    permission_classes = [permissions.IsAdminUser]

    def post(self, request, pk):
        try:
            withdrawal = WithdrawalRecord.objects.get(pk=pk)

            if withdrawal.status != 'pending':
                return Response({'detail': '只能审批待审核的提现申请'}, status=status.HTTP_400_BAD_REQUEST)

            withdrawal.status = 'approved'
            withdrawal.processed_at = timezone.now()
            withdrawal.admin_remark = request.data.get('admin_remark', '')
            withdrawal.save()

            serializer = WithdrawalRecordSerializer(withdrawal)
            return Response(serializer.data)

        except WithdrawalRecord.DoesNotExist:
            return Response({'detail': '提现记录不存在'}, status=status.HTTP_404_NOT_FOUND)


class RejectWithdrawalView(APIView):
    """拒绝提现"""
    permission_classes = [permissions.IsAdminUser]

    def post(self, request, pk):
        try:
            withdrawal = WithdrawalRecord.objects.get(pk=pk)

            if withdrawal.status != 'pending':
                return Response({'detail': '只能拒绝待审核的提现申请'}, status=status.HTTP_400_BAD_REQUEST)

            withdrawal.status = 'rejected'
            withdrawal.processed_at = timezone.now()
            withdrawal.admin_remark = request.data.get('admin_remark', '提现申请被拒绝')
            withdrawal.save()

            # 退还冻结的佣金
            withdrawal.distributor.available_commission += withdrawal.amount
            withdrawal.distributor.save()

            serializer = WithdrawalRecordSerializer(withdrawal)
            return Response(serializer.data)

        except WithdrawalRecord.DoesNotExist:
            return Response({'detail': '提现记录不存在'}, status=status.HTTP_404_NOT_FOUND)
