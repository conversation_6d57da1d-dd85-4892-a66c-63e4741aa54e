:host{display:flex;flex-direction:column}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 图标相关变量 */
/* 图标颜色预设 */
/* 全局图标基础样式 */
.icon-base.data-v-d330f826 {
  font-family: "iconfont";
  font-style: normal;
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
}

/* 图标动画 */
@keyframes icon-rotate-d330f826 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
.icon-spin.data-v-d330f826 {
  animation: icon-rotate-d330f826 1s linear infinite;
}
.icon.data-v-d330f826 {
  font-family: "iconfont";
  font-style: normal;
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
}
.icon.icon-home.data-v-d330f826::before {
  content: "\e001";
}
.icon.icon-product.data-v-d330f826::before {
  content: "\e002";
}
.icon.icon-cart.data-v-d330f826::before {
  content: "\e003";
}
.icon.icon-order.data-v-d330f826::before {
  content: "\e004";
}
.icon.icon-user.data-v-d330f826::before {
  content: "\e005";
}
.icon.icon-search.data-v-d330f826::before {
  content: "\e006";
}
.icon.icon-share.data-v-d330f826::before {
  content: "\e007";
}
.icon.icon-star.data-v-d330f826::before {
  content: "\e008";
}
.icon.icon-location.data-v-d330f826::before {
  content: "\e009";
}
.icon.icon-phone.data-v-d330f826::before {
  content: "\e010";
}
.icon.icon-wechat.data-v-d330f826::before {
  content: "\e011";
}
.icon.icon-money.data-v-d330f826::before {
  content: "\e012";
}
.icon.icon-team.data-v-d330f826::before {
  content: "\e013";
}
.icon.icon-setting.data-v-d330f826::before {
  content: "\e014";
}
.icon.icon-arrow-right.data-v-d330f826::before {
  content: "\e015";
}
.icon.icon-arrow-left.data-v-d330f826::before {
  content: "\e016";
}
.icon.icon-close.data-v-d330f826::before {
  content: "\e017";
}
.icon.icon-check.data-v-d330f826::before {
  content: "\e018";
}
.icon.icon-plus.data-v-d330f826::before {
  content: "\e019";
}
.icon.icon-minus.data-v-d330f826::before {
  content: "\e020";
}
.icon.icon-edit.data-v-d330f826::before {
  content: "\e021";
}
.icon.icon-delete.data-v-d330f826::before {
  content: "\e022";
}
.icon.icon-refresh.data-v-d330f826::before {
  content: "\e023";
}
.icon.icon-download.data-v-d330f826::before {
  content: "\e024";
}
.icon.icon-upload.data-v-d330f826::before {
  content: "\e025";
}
.icon.icon-camera.data-v-d330f826::before {
  content: "\e026";
}
.icon.icon-image.data-v-d330f826::before {
  content: "\e027";
}
.icon.icon-video.data-v-d330f826::before {
  content: "\e028";
}
.icon.icon-audio.data-v-d330f826::before {
  content: "\e029";
}
.icon.icon-file.data-v-d330f826::before {
  content: "\e030";
}
.icon.icon-folder.data-v-d330f826::before {
  content: "\e031";
}
.icon.icon-link.data-v-d330f826::before {
  content: "\e032";
}
.icon.icon-lock.data-v-d330f826::before {
  content: "\e033";
}
.icon.icon-unlock.data-v-d330f826::before {
  content: "\e034";
}
.icon.icon-eye.data-v-d330f826::before {
  content: "\e035";
}
.icon.icon-eye-close.data-v-d330f826::before {
  content: "\e036";
}
.icon.icon-heart.data-v-d330f826::before {
  content: "\e037";
}
.icon.icon-heart-o.data-v-d330f826::before {
  content: "\e038";
}
.icon.icon-message.data-v-d330f826::before {
  content: "\e039";
}
.icon.icon-notification.data-v-d330f826::before {
  content: "\e040";
}
.icon.icon-calendar.data-v-d330f826::before {
  content: "\e041";
}
.icon.icon-clock.data-v-d330f826::before {
  content: "\e042";
}
.icon.icon-gift.data-v-d330f826::before {
  content: "\e043";
}
.icon.icon-coupon.data-v-d330f826::before {
  content: "\e044";
}
.icon.icon-vip.data-v-d330f826::before {
  content: "\e045";
}
.icon.icon-diamond.data-v-d330f826::before {
  content: "\e046";
}
.icon.icon-fire.data-v-d330f826::before {
  content: "\e047";
}
.icon.icon-new.data-v-d330f826::before {
  content: "\e048";
}
.icon.icon-hot.data-v-d330f826::before {
  content: "\e049";
}
.icon.icon-sale.data-v-d330f826::before {
  content: "\e050";
}
.icon.icon-success.data-v-d330f826::before {
  content: "\e051";
}
.icon.icon-error.data-v-d330f826::before {
  content: "\e052";
}
.icon.icon-warning.data-v-d330f826::before {
  content: "\e053";
}
.icon.icon-info.data-v-d330f826::before {
  content: "\e054";
}
.icon.icon-loading.data-v-d330f826::before {
  content: "\e055";
}
.icon.icon-up.data-v-d330f826::before {
  content: "\e056";
}
.icon.icon-down.data-v-d330f826::before {
  content: "\e057";
}
.icon.icon-left.data-v-d330f826::before {
  content: "\e058";
}
.icon.icon-right.data-v-d330f826::before {
  content: "\e059";
}
.icon.icon-qq.data-v-d330f826::before {
  content: "\e060";
}
.icon.icon-weibo.data-v-d330f826::before {
  content: "\e061";
}
.icon.icon-alipay.data-v-d330f826::before {
  content: "\e062";
}
.icon.icon-distribution.data-v-d330f826::before {
  content: "\e063";
}
.icon.icon-commission.data-v-d330f826::before {
  content: "\e064";
}
.icon.icon-promotion.data-v-d330f826::before {
  content: "\e065";
}
.icon.icon-invite.data-v-d330f826::before {
  content: "\e066";
}
.icon.icon-qrcode.data-v-d330f826::before {
  content: "\e067";
}
.icon.icon-barcode.data-v-d330f826::before {
  content: "\e068";
}
.icon-loading.data-v-d330f826 {
  animation: rotate-d330f826 1s linear infinite;
}
@keyframes rotate-d330f826 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
.icon-xs.data-v-d330f826 {
  font-size: 20rpx !important;
}
.icon-sm.data-v-d330f826 {
  font-size: 24rpx !important;
}
.icon-md.data-v-d330f826 {
  font-size: 32rpx !important;
}
.icon-lg.data-v-d330f826 {
  font-size: 40rpx !important;
}
.icon-xl.data-v-d330f826 {
  font-size: 48rpx !important;
}
.icon-primary.data-v-d330f826 {
  color: #007AFF !important;
}
.icon-success.data-v-d330f826 {
  color: #34C759 !important;
}
.icon-warning.data-v-d330f826 {
  color: #FF9500 !important;
}
.icon-danger.data-v-d330f826 {
  color: #FF3B30 !important;
}
.icon-info.data-v-d330f826 {
  color: #5AC8FA !important;
}
.icon-secondary.data-v-d330f826 {
  color: #8E8E93 !important;
}
.icon-light.data-v-d330f826 {
  color: #F2F2F7 !important;
}
.icon-dark.data-v-d330f826 {
  color: #1C1C1E !important;
}