{"version": 3, "file": "cart.js", "sources": ["pages/cart/cart.uvue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2FydC9jYXJ0LnV2dWU"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 购物车列表 -->\n\t\t<view class=\"cart-list\" v-if=\"cartItems.length > 0\">\n\t\t\t<view class=\"cart-item\" v-for=\"(item, index) in cartItems\" :key=\"index\">\n\t\t\t\t<view class=\"item-checkbox\" @click=\"toggleSelect(index)\">\n\t\t\t\t\t<Icon :name=\"item.selected ? 'checkbox-checked' : 'checkbox'\" size=\"20\" :color=\"item.selected ? '#007aff' : '#ccc'\" />\n\t\t\t\t</view>\n\t\t\t\t<image class=\"item-image\" :src=\"item.product.image\" mode=\"aspectFill\" />\n\t\t\t\t<view class=\"item-info\">\n\t\t\t\t\t<text class=\"item-name\">{{ item.product.name }}</text>\n\t\t\t\t\t<text class=\"item-spec\" v-if=\"item.spec\">{{ item.spec }}</text>\n\t\t\t\t\t<view class=\"item-price\">\n\t\t\t\t\t\t<text class=\"price-text\">￥{{ item.product.price }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"item-actions\">\n\t\t\t\t\t<view class=\"quantity-control\">\n\t\t\t\t\t\t<view class=\"quantity-btn\" @click=\"decreaseQuantity(index)\">\n\t\t\t\t\t\t\t<Icon name=\"minus\" size=\"14\" color=\"#999\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"quantity-text\">{{ item.quantity }}</text>\n\t\t\t\t\t\t<view class=\"quantity-btn\" @click=\"increaseQuantity(index)\">\n\t\t\t\t\t\t\t<Icon name=\"plus\" size=\"14\" color=\"#999\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"delete-btn\" @click=\"removeItem(index)\">\n\t\t\t\t\t\t<Icon name=\"delete\" size=\"16\" color=\"#ff3b30\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 空购物车 -->\n\t\t<view class=\"empty-cart\" v-else>\n\t\t\t<Icon name=\"cart-empty\" size=\"80\" color=\"#ccc\" />\n\t\t\t<text class=\"empty-text\">购物车是空的</text>\n\t\t\t<view class=\"go-shopping-btn\" @click=\"goShopping\">\n\t\t\t\t<text class=\"btn-text\">去逛逛</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 底部操作栏 -->\n\t\t<view class=\"bottom-bar\" v-if=\"cartItems.length > 0\">\n\t\t\t<view class=\"select-all\" @click=\"toggleSelectAll\">\n\t\t\t\t<Icon :name=\"isAllSelected ? 'checkbox-checked' : 'checkbox'\" size=\"20\" :color=\"isAllSelected ? '#007aff' : '#ccc'\" />\n\t\t\t\t<text class=\"select-text\">全选</text>\n\t\t\t</view>\n\t\t\t<view class=\"total-info\">\n\t\t\t\t<text class=\"total-text\">合计：</text>\n\t\t\t\t<text class=\"total-price\">￥{{ totalAmount.toFixed(2) }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"checkout-btn\" :class=\"{ disabled: selectedCount === 0 }\" @click=\"handleCheckout\">\n\t\t\t\t<text class=\"btn-text\">结算({{ selectedCount }})</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup lang=\"uts\">\nimport { ref, computed, onMounted } from 'vue'\nimport Icon from '@/components/common/Icon.uvue'\n\n// 模拟购物车数据\nconst cartItems = ref([\n\t{\n\t\tid: 1,\n\t\tproduct: {\n\t\t\tid: 1,\n\t\t\tname: '夏季清爽T恤 纯棉透气',\n\t\t\tprice: 89.00,\n\t\t\timage: 'https://imggw.zmkj.live/products/tshirt1.jpg'\n\t\t},\n\t\tspec: '白色 L码',\n\t\tquantity: 2,\n\t\tselected: true\n\t},\n\t{\n\t\tid: 2,\n\t\tproduct: {\n\t\t\tid: 2,\n\t\t\tname: '无线蓝牙耳机 降噪立体声',\n\t\t\tprice: 299.00,\n\t\t\timage: 'https://imggw.zmkj.live/products/earphone1.jpg'\n\t\t},\n\t\tspec: '黑色',\n\t\tquantity: 1,\n\t\tselected: true\n\t},\n\t{\n\t\tid: 3,\n\t\tproduct: {\n\t\t\tid: 3,\n\t\t\tname: '简约家居摆件 北欧风格',\n\t\t\tprice: 59.00,\n\t\t\timage: 'https://imggw.zmkj.live/products/decoration1.jpg'\n\t\t},\n\t\tspec: '白色',\n\t\tquantity: 1,\n\t\tselected: false\n\t}\n])\n\n// 计算属性\nconst isAllSelected = computed(() => {\n\treturn cartItems.value.length > 0 && cartItems.value.every(item => item.selected)\n})\n\nconst selectedCount = computed(() => {\n\treturn cartItems.value.filter(item => item.selected).length\n})\n\nconst totalAmount = computed(() => {\n\treturn cartItems.value\n\t\t.filter(item => item.selected)\n\t\t.reduce((total, item) => total + (item.product.price * item.quantity), 0)\n})\n\n// 生命周期\nonMounted(() => {\n\tinitCart()\n})\n\n// 方法\nconst initCart = async () => {\n\tuni.__f__('log','at pages/cart/cart.uvue:126','初始化购物车')\n\t// 这里可以添加数据初始化逻辑\n}\n\nconst toggleSelect = (index: number) => {\n\tcartItems.value[index].selected = !cartItems.value[index].selected\n}\n\nconst toggleSelectAll = () => {\n\tconst newSelectState = !isAllSelected.value\n\tcartItems.value.forEach(item => {\n\t\titem.selected = newSelectState\n\t})\n}\n\nconst decreaseQuantity = (index: number) => {\n\tif (cartItems.value[index].quantity > 1) {\n\t\tcartItems.value[index].quantity--\n\t}\n}\n\nconst increaseQuantity = (index: number) => {\n\tcartItems.value[index].quantity++\n}\n\nconst removeItem = (index: number) => {\n\tuni.showModal({\n\t\ttitle: '提示',\n\t\tcontent: '确定要删除这个商品吗？',\n\t\tsuccess: (res) => {\n\t\t\tif (res.confirm) {\n\t\t\t\tcartItems.value.splice(index, 1)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t})\n}\n\nconst goShopping = () => {\n\tuni.switchTab({\n\t\turl: '/pages/index/index'\n\t})\n}\n\nconst handleCheckout = () => {\n\tif (selectedCount.value === 0) {\n\t\tuni.showToast({\n\t\t\ttitle: '请选择要结算的商品',\n\t\t\ticon: 'none'\n\t\t})\n\t\treturn\n\t}\n\t\n\t// 跳转到结算页面\n\tuni.navigateTo({\n\t\turl: '/pages/order/checkout'\n\t})\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n\tpadding-bottom: 60px;\n}\n\n// 购物车列表\n.cart-list {\n\tpadding: 10px 15px;\n\t\n\t.cart-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 8px;\n\t\tpadding: 15px;\n\t\tmargin-bottom: 10px;\n\t\t\n\t\t.item-checkbox {\n\t\t\tmargin-right: 12px;\n\t\t}\n\t\t\n\t\t.item-image {\n\t\t\twidth: 80px;\n\t\t\theight: 80px;\n\t\t\tborder-radius: 6px;\n\t\t\tmargin-right: 12px;\n\t\t}\n\t\t\n\t\t.item-info {\n\t\t\tflex: 1;\n\t\t\t\n\t\t\t.item-name {\n\t\t\t\tfont-size: 14px;\n\t\t\t\tcolor: #333;\n\t\t\t\tline-height: 1.4;\n\t\t\t\tmargin-bottom: 4px;\n\t\t\t\tdisplay: -webkit-box;\n\t\t\t\t-webkit-box-orient: vertical;\n\t\t\t\t-webkit-line-clamp: 2;\n\t\t\t\toverflow: hidden;\n\t\t\t}\n\t\t\t\n\t\t\t.item-spec {\n\t\t\t\tfont-size: 12px;\n\t\t\t\tcolor: #999;\n\t\t\t\tmargin-bottom: 8px;\n\t\t\t}\n\t\t\t\n\t\t\t.item-price {\n\t\t\t\t.price-text {\n\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: #ff3b30;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.item-actions {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: flex-end;\n\t\t\t\n\t\t\t.quantity-control {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tmargin-bottom: 12px;\n\t\t\t\t\n\t\t\t\t.quantity-btn {\n\t\t\t\t\twidth: 28px;\n\t\t\t\t\theight: 28px;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\tborder: 1px solid #ddd;\n\t\t\t\t\tborder-radius: 4px;\n\t\t\t\t\t\n\t\t\t\t\t&:first-child {\n\t\t\t\t\t\tborder-top-right-radius: 0;\n\t\t\t\t\t\tborder-bottom-right-radius: 0;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&:last-child {\n\t\t\t\t\t\tborder-top-left-radius: 0;\n\t\t\t\t\t\tborder-bottom-left-radius: 0;\n\t\t\t\t\t\tborder-left: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.quantity-text {\n\t\t\t\t\twidth: 40px;\n\t\t\t\t\theight: 28px;\n\t\t\t\t\tline-height: 28px;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tfont-size: 14px;\n\t\t\t\t\tborder-top: 1px solid #ddd;\n\t\t\t\t\tborder-bottom: 1px solid #ddd;\n\t\t\t\t\tbackground-color: #f8f8f8;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.delete-btn {\n\t\t\t\tpadding: 4px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 空购物车\n.empty-cart {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 80px 20px;\n\t\n\t.empty-text {\n\t\tfont-size: 16px;\n\t\tcolor: #999;\n\t\tmargin: 20px 0 30px;\n\t}\n\t\n\t.go-shopping-btn {\n\t\twidth: 120px;\n\t\theight: 40px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground-color: #007aff;\n\t\tborder-radius: 20px;\n\t\t\n\t\t.btn-text {\n\t\t\tfont-size: 14px;\n\t\t\tcolor: #fff;\n\t\t}\n\t}\n}\n\n// 底部操作栏\n.bottom-bar {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\theight: 60px;\n\tdisplay: flex;\n\talign-items: center;\n\tbackground-color: #fff;\n\tborder-top: 1px solid #eee;\n\tpadding: 0 15px;\n\t\n\t.select-all {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-right: 20px;\n\t\t\n\t\t.select-text {\n\t\t\tfont-size: 14px;\n\t\t\tcolor: #333;\n\t\t\tmargin-left: 8px;\n\t\t}\n\t}\n\t\n\t.total-info {\n\t\tflex: 1;\n\t\ttext-align: right;\n\t\tmargin-right: 15px;\n\t\t\n\t\t.total-text {\n\t\t\tfont-size: 14px;\n\t\t\tcolor: #333;\n\t\t}\n\t\t\n\t\t.total-price {\n\t\t\tfont-size: 18px;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #ff3b30;\n\t\t}\n\t}\n\t\n\t.checkout-btn {\n\t\twidth: 100px;\n\t\theight: 40px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground-color: #007aff;\n\t\tborder-radius: 20px;\n\t\t\n\t\t&.disabled {\n\t\t\tbackground-color: #ccc;\n\t\t}\n\t\t\n\t\t.btn-text {\n\t\t\tfont-size: 14px;\n\t\t\tcolor: #fff;\n\t\t}\n\t}\n}\n</style>\n", "import MiniProgramPage from 'F:/zmkj-system/frontend/pages/cart/cart.uvue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "__awaiter", "uni"], "mappings": ";;;;;AA6DA,MAAO,OAAU,MAAA;;;;AAGjB,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACrB,IAAA,cAAA;AAAA,QACC,IAAI;AAAA,QACJ,SAAS,IAAA,cAAA;AAAA,UACR,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,SACP;AAAA,QACD,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,MACV,CAAA;AAAA,MACD,IAAA,cAAA;AAAA,QACC,IAAI;AAAA,QACJ,SAAS,IAAA,cAAA;AAAA,UACR,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,SACP;AAAA,QACD,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,MACV,CAAA;AAAA,MACD,IAAA,cAAA;AAAA,QACC,IAAI;AAAA,QACJ,SAAS,IAAA,cAAA;AAAA,UACR,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,SACP;AAAA,QACD,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,MACV,CAAA;AAAA,IACD,CAAA;AAGD,UAAM,gBAAgBC,cAAAA,SAAS,MAAA;AAC9B,aAAO,UAAU,MAAM,SAAS,KAAK,UAAU,MAAM,MAAM,UAAI;AAAI,eAAA,KAAK;AAAA,MAAQ,CAAA;AAAA,IACjF,CAAC;AAED,UAAM,gBAAgBA,cAAAA,SAAS,MAAA;AAC9B,aAAO,UAAU,MAAM,OAAO,UAAI;AAAI,eAAA,KAAK;AAAA,OAAQ,EAAE;AAAA,IACtD,CAAC;AAED,UAAM,cAAcA,cAAAA,SAAS,MAAA;AAC5B,aAAO,UAAU,MACf,OAAO,UAAQ;AAAA,eAAA,KAAK;AAAA,MAAL,CAAa,EAC5B,OAAO,CAAC,OAAO;AAAS,eAAA,QAAS,KAAK,QAAQ,QAAQ,KAAK;AAAA,MAAS,GAAE,CAAC;AAAA,IAC1E,CAAC;AAGDC,kBAAAA,UAAU,MAAA;AACT;IACD,CAAC;AAGD,UAAM,WAAW,MAAA;AAAA,aAAAC,cAAA,UAAA,MAAA,QAAA,QAAA,aAAA;AAChBC,sBAAAA,MAAI,MAAM,OAAM,+BAA8B,QAAQ;AAAA,MAEtD,CAAA;AAAA;AAED,UAAM,eAAe,CAAC,UAAa;AAClC,gBAAU,MAAM,KAAK,EAAE,WAAW,CAAC,UAAU,MAAM,KAAK,EAAE;AAAA,IAC3D;AAEA,UAAM,kBAAkB,MAAA;AACvB,YAAM,iBAAiB,CAAC,cAAc;AACtC,gBAAU,MAAM,QAAQ,UAAI;AAC3B,aAAK,WAAW;AAAA,MACjB,CAAC;AAAA,IACF;AAEA,UAAM,mBAAmB,CAAC,UAAa;AACtC,UAAI,UAAU,MAAM,KAAK,EAAE,WAAW,GAAG;AACxC,kBAAU,MAAM,KAAK,EAAE;AAAA,MACvB;AAAA,IACF;AAEA,UAAM,mBAAmB,CAAC,UAAa;AACtC,gBAAU,MAAM,KAAK,EAAE;AAAA,IACxB;AAEA,UAAM,aAAa,CAAC,UAAa;AAChCA,0BAAI,UAAU,IAAA,cAAA;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAG;AACZ,cAAI,IAAI,SAAS;AAChB,sBAAU,MAAM,OAAO,OAAO,CAAC;AAC/BA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACN,CAAA;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAA,CAAA;AAAA,IACF;AAEA,UAAM,aAAa,MAAA;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACb,KAAK;AAAA,MACL,CAAA;AAAA,IACF;AAEA,UAAM,iBAAiB,MAAA;AACtB,UAAI,cAAc,UAAU,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACN,CAAA;AACD,eAAM;AAAA,MACN;AAGDA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxLA,GAAG,WAAW,eAAe;"}