#!/usr/bin/env python
"""
轮播图API测试脚本
用于测试轮播图相关的API接口
"""

import os
import sys
import django
import requests
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.core.models import Banner

def test_banner_api():
    """测试轮播图API"""
    base_url = "http://zmkj.nat100.top/api/v1"
    
    print("=" * 50)
    print("轮播图API测试")
    print("=" * 50)
    
    # 1. 测试轮播图列表API
    print("\n1. 测试轮播图列表API")
    try:
        response = requests.get(f"{base_url}/banners/", params={'position': 'home'})
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {str(e)}")
    
    # 2. 测试轮播图位置API
    print("\n2. 测试轮播图位置API")
    try:
        response = requests.get(f"{base_url}/banners/positions/")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {str(e)}")
    
    # 3. 检查数据库中的轮播图数据
    print("\n3. 检查数据库中的轮播图数据")
    try:
        banners = Banner.objects.filter(is_deleted=False)
        print(f"数据库中轮播图总数: {banners.count()}")
        for banner in banners:
            print(f"- {banner.title} (位置: {banner.position}, 状态: {'启用' if banner.is_active else '禁用'})")
    except Exception as e:
        print(f"数据库查询失败: {str(e)}")
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    test_banner_api()
