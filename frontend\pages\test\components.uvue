<template>
	<view class="test-container">
		<Header title="组件测试" :show-back="true" />
		
		<scroll-view class="test-content" scroll-y>
			<!-- Icon组件测试 -->
			<view class="test-section">
				<text class="section-title">Icon 图标组件</text>
				<view class="icon-grid">
					<view class="icon-item" v-for="icon in testIcons" :key="icon.name">
						<Icon :name="icon.name" :size="24" :color="icon.color" />
						<text class="icon-name">{{ icon.name }}</text>
					</view>
				</view>
			</view>

			<!-- Loading组件测试 -->
			<view class="test-section">
				<text class="section-title">Loading 加载组件</text>
				<view class="button-group">
					<view class="test-button" @click="showLoading">
						<text class="button-text">显示Loading</text>
					</view>
					<view class="test-button" @click="showInlineLoading = !showInlineLoading">
						<text class="button-text">切换内联Loading</text>
					</view>
				</view>
				<Loading :visible="showInlineLoading" :mask="false" text="加载中..." />
			</view>

			<!-- Empty组件测试 -->
			<view class="test-section">
				<text class="section-title">Empty 空状态组件</text>
				<view class="button-group">
					<view class="test-button" @click="currentEmptyType = 'default'">
						<text class="button-text">默认</text>
					</view>
					<view class="test-button" @click="currentEmptyType = 'search'">
						<text class="button-text">搜索</text>
					</view>
					<view class="test-button" @click="currentEmptyType = 'cart'">
						<text class="button-text">购物车</text>
					</view>
				</view>
				<Empty :type="currentEmptyType" :show-action="true" @action="handleEmptyAction" />
			</view>

			<!-- Modal组件测试 -->
			<view class="test-section">
				<text class="section-title">Modal 模态框组件</text>
				<view class="button-group">
					<view class="test-button" @click="showModal = true">
						<text class="button-text">显示Modal</text>
					</view>
					<view class="test-button" @click="showAlertModal">
						<text class="button-text">Alert模态框</text>
					</view>
					<view class="test-button" @click="showConfirmModal">
						<text class="button-text">Confirm模态框</text>
					</view>
				</view>
			</view>

			<!-- 测试结果 -->
			<view class="test-section">
				<text class="section-title">测试结果</text>
				<view class="test-results">
					<text class="result-item" v-for="(result, index) in testResults" :key="index">
						{{ result }}
					</text>
				</view>
			</view>
		</scroll-view>

		<!-- Loading组件 -->
		<Loading 
			:visible="showLoadingModal" 
			text="正在加载..." 
			@mask-click="showLoadingModal = false" 
		/>

		<!-- Modal组件 -->
		<Modal
			:visible="showModal"
			title="测试模态框"
			content="这是一个测试模态框的内容"
			@close="showModal = false"
			@confirm="handleModalConfirm"
			@cancel="handleModalCancel"
		/>

		<Modal
			:visible="showAlert"
			title="提示"
			content="这是一个Alert模态框"
			type="alert"
			@close="showAlert = false"
			@confirm="handleAlertConfirm"
		/>

		<Modal
			:visible="showConfirm"
			title="确认"
			content="确定要执行这个操作吗？"
			type="confirm"
			@close="showConfirm = false"
			@confirm="handleConfirmConfirm"
			@cancel="handleConfirmCancel"
		/>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Header from '@/components/common/Header.uvue'
import Icon from '@/components/common/Icon.uvue'
import Loading from '@/components/common/Loading.uvue'
import Empty from '@/components/common/Empty.uvue'
import Modal from '@/components/common/Modal.uvue'

// 响应式数据
const showLoadingModal = ref(false)
const showInlineLoading = ref(false)
const showModal = ref(false)
const showAlert = ref(false)
const showConfirm = ref(false)
const currentEmptyType = ref('default')
const testResults = ref<string[]>([])

// 测试图标数据
const testIcons = ref([
	{ name: 'home', color: '#007AFF' },
	{ name: 'user', color: '#34C759' },
	{ name: 'search', color: '#FF9500' },
	{ name: 'cart', color: '#FF3B30' },
	{ name: 'heart', color: '#FF2D92' },
	{ name: 'star', color: '#FFCC00' },
	{ name: 'settings', color: '#8E8E93' },
	{ name: 'location', color: '#5856D6' }
])

// 生命周期
onMounted(() => {
	addTestResult('组件测试页面加载完成')
	testComponents()
})

// 方法
const addTestResult = (message: string) => {
	const timestamp = new Date().toLocaleTimeString()
	testResults.value.unshift(`[${timestamp}] ${message}`)
}

const testComponents = () => {
	// 测试Icon组件
	addTestResult('Icon组件测试: 显示8个不同图标')
	
	// 测试其他组件的基本功能
	setTimeout(() => {
		addTestResult('所有组件基础功能正常')
	}, 1000)
}

const showLoading = () => {
	showLoadingModal.value = true
	addTestResult('显示Loading模态框')
	
	// 3秒后自动关闭
	setTimeout(() => {
		showLoadingModal.value = false
		addTestResult('Loading模态框自动关闭')
	}, 3000)
}

const handleEmptyAction = () => {
	addTestResult(`Empty组件Action点击: ${currentEmptyType.value}`)
	uni.showToast({
		title: 'Empty Action 点击',
		icon: 'success'
	})
}

const showAlertModal = () => {
	showAlert.value = true
	addTestResult('显示Alert模态框')
}

const showConfirmModal = () => {
	showConfirm.value = true
	addTestResult('显示Confirm模态框')
}

const handleModalConfirm = () => {
	addTestResult('Modal确认按钮点击')
	uni.showToast({
		title: '确认',
		icon: 'success'
	})
}

const handleModalCancel = () => {
	addTestResult('Modal取消按钮点击')
	uni.showToast({
		title: '取消',
		icon: 'none'
	})
}

const handleAlertConfirm = () => {
	addTestResult('Alert确认按钮点击')
	uni.showToast({
		title: 'Alert确认',
		icon: 'success'
	})
}

const handleConfirmConfirm = () => {
	addTestResult('Confirm确认按钮点击')
	uni.showToast({
		title: 'Confirm确认',
		icon: 'success'
	})
}

const handleConfirmCancel = () => {
	addTestResult('Confirm取消按钮点击')
	uni.showToast({
		title: 'Confirm取消',
		icon: 'none'
	})
}
</script>

<style lang="scss" scoped>
.test-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.test-content {
	padding: 15px;
	padding-top: 100px; // 为Header留出空间
}

.test-section {
	background-color: #fff;
	border-radius: 8px;
	padding: 20px;
	margin-bottom: 15px;
	
	.section-title {
		font-size: 18px;
		font-weight: 600;
		color: #333;
		margin-bottom: 15px;
	}
}

.icon-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 15px;
	
	.icon-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 60px;
		
		.icon-name {
			font-size: 12px;
			color: #666;
			margin-top: 5px;
			text-align: center;
		}
	}
}

.button-group {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
	margin-bottom: 15px;
}

.test-button {
	padding: 8px 16px;
	background-color: #007AFF;
	border-radius: 6px;
	
	.button-text {
		font-size: 14px;
		color: #fff;
	}
	
	&:active {
		opacity: 0.8;
	}
}

.test-results {
	max-height: 200px;
	overflow-y: auto;
	
	.result-item {
		display: block;
		font-size: 12px;
		color: #666;
		line-height: 1.5;
		padding: 2px 0;
		border-bottom: 1px solid #f0f0f0;
		
		&:last-child {
			border-bottom: none;
		}
	}
}
</style>
