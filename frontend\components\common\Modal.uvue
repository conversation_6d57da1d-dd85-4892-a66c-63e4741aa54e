<template>
	<view class="modal-container" v-if="visible">
		<!-- 遮罩层 -->
		<view class="modal-mask" @click="handleMaskClick"></view>
		
		<!-- 模态框内容 -->
		<view class="modal-content" :class="contentClass">
			<!-- 头部 -->
			<view class="modal-header" v-if="showHeader">
				<text class="modal-title">{{ title }}</text>
				<view class="modal-close" @click="handleClose" v-if="showClose">
					<Icon name="close" size="16" color="#999" />
				</view>
			</view>
			
			<!-- 内容区域 -->
			<view class="modal-body">
				<slot>
					<text class="modal-text" v-if="content">{{ content }}</text>
				</slot>
			</view>
			
			<!-- 底部按钮 -->
			<view class="modal-footer" v-if="showFooter">
				<view class="modal-button cancel-button" @click="handleCancel" v-if="showCancel">
					<text class="button-text cancel-text">{{ cancelText }}</text>
				</view>
				<view class="modal-button confirm-button" @click="handleConfirm" v-if="showConfirm">
					<text class="button-text confirm-text">{{ confirmText }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Icon from './Icon.uvue'

// Props定义
interface Props {
	visible?: boolean
	title?: string
	content?: string
	showHeader?: boolean
	showClose?: boolean
	showFooter?: boolean
	showCancel?: boolean
	showConfirm?: boolean
	cancelText?: string
	confirmText?: string
	maskClosable?: boolean
	width?: string
	type?: 'default' | 'alert' | 'confirm' | 'custom'
}

const props = withDefaults(defineProps<Props>(), {
	visible: false,
	title: '提示',
	content: '',
	showHeader: true,
	showClose: true,
	showFooter: true,
	showCancel: true,
	showConfirm: true,
	cancelText: '取消',
	confirmText: '确定',
	maskClosable: true,
	width: '300px',
	type: 'default'
})

// Emits定义
const emit = defineEmits<{
	close: []
	cancel: []
	confirm: []
	maskClick: []
}>()

// 计算属性
const contentClass = computed(() => {
	return `modal-content-${props.type}`
})

// 方法
const handleMaskClick = () => {
	emit('maskClick')
	if (props.maskClosable) {
		handleClose()
	}
}

const handleClose = () => {
	emit('close')
}

const handleCancel = () => {
	emit('cancel')
	emit('close')
}

const handleConfirm = () => {
	emit('confirm')
	emit('close')
}
</script>

<style lang="scss" scoped>
.modal-container {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
	
	.modal-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		animation: modal-fade-in 0.3s ease-out;
	}
	
	.modal-content {
		position: relative;
		background-color: #FFFFFF;
		border-radius: 12px;
		max-width: 90%;
		width: 300px;
		max-height: 80%;
		overflow: hidden;
		animation: modal-slide-in 0.3s ease-out;
		
		&.modal-content-alert {
			.modal-footer {
				.cancel-button {
					display: none;
				}
			}
		}
		
		&.modal-content-confirm {
			// 默认样式
		}
		
		&.modal-content-custom {
			.modal-header,
			.modal-footer {
				display: none;
			}
		}
	}
	
	.modal-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20px 20px 0;
		
		.modal-title {
			font-size: 18px;
			font-weight: 600;
			color: #333333;
			flex: 1;
		}
		
		.modal-close {
			padding: 4px;
			margin-left: 12px;
		}
	}
	
	.modal-body {
		padding: 20px;
		
		.modal-text {
			font-size: 16px;
			color: #666666;
			line-height: 1.5;
			text-align: center;
		}
	}
	
	.modal-footer {
		display: flex;
		border-top: 1px solid #EEEEEE;
		
		.modal-button {
			flex: 1;
			height: 50px;
			display: flex;
			align-items: center;
			justify-content: center;
			
			&:not(:last-child) {
				border-right: 1px solid #EEEEEE;
			}
			
			.button-text {
				font-size: 16px;
			}
			
			&.cancel-button {
				.cancel-text {
					color: #666666;
				}
			}
			
			&.confirm-button {
				.confirm-text {
					color: #007AFF;
					font-weight: 500;
				}
			}
		}
	}
}

@keyframes modal-fade-in {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes modal-slide-in {
	from {
		opacity: 0;
		transform: scale(0.8) translateY(-20px);
	}
	to {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}
</style>
