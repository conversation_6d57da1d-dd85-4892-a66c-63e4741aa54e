from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone
from decimal import Decimal
import uuid
from apps.core.models import BaseModel
from apps.users.models import User
from apps.products.models import Product


class Order(BaseModel):
    """订单模型"""
    STATUS_CHOICES = [
        ('pending', '待支付'),
        ('paid', '已支付'),
        ('processing', '处理中'),
        ('shipped', '已发货'),
        ('delivered', '已送达'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
        ('refunding', '退款中'),
        ('refunded', '已退款'),
    ]
    
    PAYMENT_METHODS = [
        ('wechat', '微信支付'),
        ('alipay', '支付宝'),
        ('balance', '余额支付'),
        ('offline', '线下支付'),
    ]
    
    order_no = models.CharField(
        verbose_name='订单号', 
        max_length=32, 
        unique=True, 
        help_text='系统生成的唯一订单号'
    )
    user = models.ForeignKey(
        User, 
        on_delete=models.PROTECT, 
        related_name='orders',
        verbose_name='下单用户',
        help_text='订单所属用户'
    )
    
    # 商品信息
    total_amount = models.DecimalField(
        verbose_name='订单总金额', 
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text='订单商品总金额'
    )
    shipping_fee = models.DecimalField(
        verbose_name='运费', 
        max_digits=8, 
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0'))],
        help_text='配送费用'
    )
    discount_amount = models.DecimalField(
        verbose_name='优惠金额', 
        max_digits=8, 
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0'))],
        help_text='优惠券、折扣等减免金额'
    )
    final_amount = models.DecimalField(
        verbose_name='实付金额', 
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text='用户实际支付金额'
    )
    
    # 订单状态
    status = models.CharField(
        verbose_name='订单状态', 
        max_length=20, 
        choices=STATUS_CHOICES, 
        default='pending',
        help_text='当前订单状态'
    )
    payment_method = models.CharField(
        verbose_name='支付方式', 
        max_length=20, 
        choices=PAYMENT_METHODS,
        blank=True,
        help_text='用户选择的支付方式'
    )
    
    # 收货信息
    recipient_name = models.CharField(verbose_name='收货人', max_length=50, help_text='收件人姓名')
    recipient_phone = models.CharField(verbose_name='收货电话', max_length=20, help_text='收件人电话')
    shipping_address = models.CharField(verbose_name='收货地址', max_length=500, help_text='详细收货地址')
    
    # 时间记录
    paid_at = models.DateTimeField(verbose_name='支付时间', null=True, blank=True, help_text='订单支付时间')
    shipped_at = models.DateTimeField(verbose_name='发货时间', null=True, blank=True, help_text='订单发货时间')
    delivered_at = models.DateTimeField(verbose_name='送达时间', null=True, blank=True, help_text='订单送达时间')
    completed_at = models.DateTimeField(verbose_name='完成时间', null=True, blank=True, help_text='订单完成时间')
    
    # 其他信息
    remark = models.TextField(verbose_name='订单备注', blank=True, help_text='用户留言或特殊要求')
    admin_remark = models.TextField(verbose_name='管理员备注', blank=True, help_text='内部处理备注')
    tracking_number = models.CharField(
        verbose_name='快递单号', 
        max_length=50, 
        blank=True, 
        help_text='物流快递追踪号'
    )
    
    class Meta:
        verbose_name = '订单'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['order_no']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"订单{self.order_no} - {self.user.username}"
    
    def save(self, *args, **kwargs):
        """保存时自动生成订单号"""
        if not self.order_no:
            self.order_no = self.generate_order_no()
        super().save(*args, **kwargs)
    
    @staticmethod
    def generate_order_no():
        """生成唯一订单号"""
        import time
        timestamp = str(int(time.time()))
        random_str = str(uuid.uuid4().hex)[:8].upper()
        return f"ZM{timestamp}{random_str}"
    
    def get_status_display_color(self):
        """获取状态显示颜色"""
        color_map = {
            'pending': '#f39c12',     # 橙色
            'paid': '#3498db',        # 蓝色
            'processing': '#9b59b6',  # 紫色
            'shipped': '#2ecc71',     # 绿色
            'delivered': '#27ae60',   # 深绿色
            'completed': '#95a5a6',   # 灰色
            'cancelled': '#e74c3c',   # 红色
            'refunding': '#e67e22',   # 深橙色
            'refunded': '#34495e',    # 深灰色
        }
        return color_map.get(self.status, '#95a5a6')
    
    def can_cancel(self):
        """判断订单是否可以取消"""
        return self.status in ['pending', 'paid']
    
    def can_refund(self):
        """判断订单是否可以退款"""
        return self.status in ['paid', 'processing', 'shipped']


class OrderItem(BaseModel):
    """订单项模型"""
    order = models.ForeignKey(
        Order, 
        on_delete=models.CASCADE, 
        related_name='items',
        verbose_name='所属订单'
    )
    product = models.ForeignKey(
        Product, 
        on_delete=models.PROTECT,
        verbose_name='商品',
        help_text='订单中的商品'
    )
    product_name = models.CharField(
        verbose_name='商品名称', 
        max_length=200, 
        help_text='下单时的商品名称快照'
    )
    product_sku = models.CharField(
        verbose_name='商品编码', 
        max_length=100, 
        help_text='下单时的商品SKU快照'
    )
    product_price = models.DecimalField(
        verbose_name='商品单价', 
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text='下单时的商品价格快照'
    )
    quantity = models.PositiveIntegerField(
        verbose_name='购买数量',
        validators=[MinValueValidator(1)],
        help_text='用户购买的商品数量'
    )
    subtotal = models.DecimalField(
        verbose_name='小计金额', 
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text='单价 × 数量的小计金额'
    )
    
    class Meta:
        verbose_name = '订单项'
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['order']),
            models.Index(fields=['product']),
        ]
    
    def __str__(self):
        return f"{self.order.order_no} - {self.product_name} × {self.quantity}"
    
    def save(self, *args, **kwargs):
        """保存时自动计算小计"""
        self.subtotal = self.product_price * self.quantity
        super().save(*args, **kwargs)


class Payment(BaseModel):
    """支付记录模型"""
    PAYMENT_STATUS = [
        ('pending', '待支付'),
        ('processing', '支付中'),
        ('success', '支付成功'),
        ('failed', '支付失败'),
        ('cancelled', '已取消'),
        ('refunded', '已退款'),
    ]
    
    PAYMENT_TYPES = [
        ('payment', '支付'),
        ('refund', '退款'),
    ]
    
    order = models.ForeignKey(
        Order, 
        on_delete=models.CASCADE, 
        related_name='payments',
        verbose_name='关联订单'
    )
    payment_no = models.CharField(
        verbose_name='支付单号', 
        max_length=32, 
        unique=True,
        help_text='系统生成的支付单号'
    )
    third_party_no = models.CharField(
        verbose_name='第三方交易号', 
        max_length=64, 
        blank=True,
        help_text='微信、支付宝等第三方平台交易号'
    )
    payment_method = models.CharField(
        verbose_name='支付方式', 
        max_length=20, 
        choices=Order.PAYMENT_METHODS,
        help_text='支付渠道'
    )
    payment_type = models.CharField(
        verbose_name='支付类型', 
        max_length=20, 
        choices=PAYMENT_TYPES,
        default='payment',
        help_text='支付或退款'
    )
    amount = models.DecimalField(
        verbose_name='支付金额', 
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text='实际支付或退款金额'
    )
    status = models.CharField(
        verbose_name='支付状态', 
        max_length=20, 
        choices=PAYMENT_STATUS, 
        default='pending',
        help_text='当前支付状态'
    )
    paid_at = models.DateTimeField(
        verbose_name='支付时间', 
        null=True, 
        blank=True,
        help_text='实际支付完成时间'
    )
    callback_data = models.JSONField(
        verbose_name='回调数据', 
        default=dict, 
        blank=True,
        help_text='第三方支付平台回调的原始数据'
    )
    remark = models.TextField(
        verbose_name='支付备注', 
        blank=True,
        help_text='支付相关备注信息'
    )
    
    class Meta:
        verbose_name = '支付记录'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['order']),
            models.Index(fields=['payment_no']),
            models.Index(fields=['third_party_no']),
            models.Index(fields=['status']),
        ]
    
    def __str__(self):
        return f"支付{self.payment_no} - {self.order.order_no}"
    
    def save(self, *args, **kwargs):
        """保存时自动生成支付单号"""
        if not self.payment_no:
            self.payment_no = self.generate_payment_no()
        super().save(*args, **kwargs)
    
    @staticmethod
    def generate_payment_no():
        """生成唯一支付单号"""
        import time
        timestamp = str(int(time.time()))
        random_str = str(uuid.uuid4().hex)[:6].upper()
        return f"PAY{timestamp}{random_str}"


class OrderLog(BaseModel):
    """订单操作日志模型"""
    ACTION_TYPES = [
        ('create', '创建订单'),
        ('pay', '支付订单'),
        ('cancel', '取消订单'),
        ('ship', '发货'),
        ('deliver', '送达'),
        ('complete', '完成'),
        ('refund', '退款'),
        ('remark', '备注'),
        ('status_change', '状态变更'),
    ]
    
    order = models.ForeignKey(
        Order, 
        on_delete=models.CASCADE, 
        related_name='logs',
        verbose_name='关联订单'
    )
    action_type = models.CharField(
        verbose_name='操作类型', 
        max_length=20, 
        choices=ACTION_TYPES,
        help_text='具体的操作类型'
    )
    action_user = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        verbose_name='操作人',
        help_text='执行操作的用户，为空表示系统操作'
    )
    old_status = models.CharField(
        verbose_name='原状态', 
        max_length=20, 
        blank=True,
        help_text='操作前的订单状态'
    )
    new_status = models.CharField(
        verbose_name='新状态', 
        max_length=20, 
        blank=True,
        help_text='操作后的订单状态'
    )
    content = models.TextField(
        verbose_name='操作内容',
        help_text='详细的操作描述'
    )
    extra_data = models.JSONField(
        verbose_name='额外数据', 
        default=dict, 
        blank=True,
        help_text='操作相关的额外数据'
    )
    
    class Meta:
        verbose_name = '订单日志'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['order', '-created_at']),
            models.Index(fields=['action_type']),
        ]
    
    def __str__(self):
        return f"{self.order.order_no} - {self.get_action_type_display()}"
