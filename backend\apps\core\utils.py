# -*- coding: utf-8 -*-
"""
智梦科技系统 - 核心工具函数模块

提供系统中常用的工具函数，包括：
- 字符串处理工具
- 时间处理工具
- 数据验证工具
- 文件处理工具
- 加密解密工具
- 响应格式化工具

创建时间：2025年7月30日
维护人员：智梦科技开发团队
"""

import hashlib
import uuid
import re
import json
import base64
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Any, Dict, List, Optional, Union
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.utils import timezone
from django.http import JsonResponse
from rest_framework import status


class StringUtils:
    """字符串处理工具类"""
    
    @staticmethod
    def generate_unique_code(prefix: str = '', length: int = 8) -> str:
        """
        生成唯一编码
        
        Args:
            prefix: 前缀
            length: 编码长度
            
        Returns:
            str: 唯一编码
        """
        import random
        import string
        
        chars = string.ascii_uppercase + string.digits
        code = ''.join(random.choices(chars, k=length))
        return f"{prefix}{code}" if prefix else code
    
    @staticmethod
    def mask_phone(phone: str) -> str:
        """
        手机号脱敏处理
        
        Args:
            phone: 手机号
            
        Returns:
            str: 脱敏后的手机号
        """
        if not phone or len(phone) < 11:
            return phone
        return f"{phone[:3]}****{phone[-4:]}"
    
    @staticmethod
    def mask_email(email: str) -> str:
        """
        邮箱脱敏处理
        
        Args:
            email: 邮箱地址
            
        Returns:
            str: 脱敏后的邮箱
        """
        if not email or '@' not in email:
            return email
        
        username, domain = email.split('@', 1)
        if len(username) <= 2:
            masked_username = username
        else:
            masked_username = f"{username[0]}***{username[-1]}"
        
        return f"{masked_username}@{domain}"
    
    @staticmethod
    def is_valid_phone(phone: str) -> bool:
        """
        验证手机号格式
        
        Args:
            phone: 手机号
            
        Returns:
            bool: 是否有效
        """
        pattern = r'^1[3-9]\d{9}$'
        return bool(re.match(pattern, phone))
    
    @staticmethod
    def is_valid_id_card(id_card: str) -> bool:
        """
        验证身份证号格式
        
        Args:
            id_card: 身份证号
            
        Returns:
            bool: 是否有效
        """
        pattern = r'^\d{17}[\dXx]$'
        return bool(re.match(pattern, id_card))


class TimeUtils:
    """时间处理工具类"""
    
    @staticmethod
    def now() -> datetime:
        """获取当前时间"""
        return timezone.now()
    
    @staticmethod
    def format_datetime(dt: datetime, fmt: str = '%Y-%m-%d %H:%M:%S') -> str:
        """
        格式化时间
        
        Args:
            dt: 时间对象
            fmt: 格式字符串
            
        Returns:
            str: 格式化后的时间字符串
        """
        return dt.strftime(fmt)
    
    @staticmethod
    def days_ago(days: int) -> datetime:
        """
        获取N天前的时间
        
        Args:
            days: 天数
            
        Returns:
            datetime: N天前的时间
        """
        return timezone.now() - timedelta(days=days)
    
    @staticmethod
    def days_later(days: int) -> datetime:
        """
        获取N天后的时间
        
        Args:
            days: 天数
            
        Returns:
            datetime: N天后的时间
        """
        return timezone.now() + timedelta(days=days)
    
    @staticmethod
    def is_expired(expire_time: datetime) -> bool:
        """
        检查是否已过期
        
        Args:
            expire_time: 过期时间
            
        Returns:
            bool: 是否已过期
        """
        return timezone.now() > expire_time


class ValidationUtils:
    """数据验证工具类"""
    
    @staticmethod
    def validate_email_format(email: str) -> bool:
        """
        验证邮箱格式
        
        Args:
            email: 邮箱地址
            
        Returns:
            bool: 是否有效
        """
        try:
            validate_email(email)
            return True
        except ValidationError:
            return False
    
    @staticmethod
    def validate_password_strength(password: str) -> Dict[str, Any]:
        """
        验证密码强度
        
        Args:
            password: 密码
            
        Returns:
            dict: 验证结果
        """
        result = {
            'is_valid': True,
            'errors': [],
            'strength': 'weak'
        }
        
        if len(password) < 8:
            result['is_valid'] = False
            result['errors'].append('密码长度至少8位')
        
        if not re.search(r'[a-z]', password):
            result['errors'].append('密码应包含小写字母')
        
        if not re.search(r'[A-Z]', password):
            result['errors'].append('密码应包含大写字母')
        
        if not re.search(r'\d', password):
            result['errors'].append('密码应包含数字')
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            result['errors'].append('密码应包含特殊字符')
        
        # 计算强度
        strength_score = 0
        if len(password) >= 8:
            strength_score += 1
        if re.search(r'[a-z]', password):
            strength_score += 1
        if re.search(r'[A-Z]', password):
            strength_score += 1
        if re.search(r'\d', password):
            strength_score += 1
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            strength_score += 1
        
        if strength_score >= 4:
            result['strength'] = 'strong'
        elif strength_score >= 3:
            result['strength'] = 'medium'
        
        if result['errors']:
            result['is_valid'] = False
        
        return result


class CryptoUtils:
    """加密解密工具类"""
    
    @staticmethod
    def generate_uuid() -> str:
        """生成UUID"""
        return str(uuid.uuid4())
    
    @staticmethod
    def md5_hash(text: str) -> str:
        """
        MD5哈希
        
        Args:
            text: 待哈希的文本
            
        Returns:
            str: MD5哈希值
        """
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    @staticmethod
    def sha256_hash(text: str) -> str:
        """
        SHA256哈希
        
        Args:
            text: 待哈希的文本
            
        Returns:
            str: SHA256哈希值
        """
        return hashlib.sha256(text.encode('utf-8')).hexdigest()
    
    @staticmethod
    def base64_encode(text: str) -> str:
        """
        Base64编码
        
        Args:
            text: 待编码的文本
            
        Returns:
            str: Base64编码结果
        """
        return base64.b64encode(text.encode('utf-8')).decode('utf-8')
    
    @staticmethod
    def base64_decode(encoded_text: str) -> str:
        """
        Base64解码
        
        Args:
            encoded_text: Base64编码的文本
            
        Returns:
            str: 解码结果
        """
        return base64.b64decode(encoded_text.encode('utf-8')).decode('utf-8')


class ResponseUtils:
    """响应格式化工具类"""
    
    @staticmethod
    def success_response(data: Any = None, message: str = '操作成功', 
                        status_code: int = status.HTTP_200_OK) -> JsonResponse:
        """
        成功响应
        
        Args:
            data: 响应数据
            message: 响应消息
            status_code: HTTP状态码
            
        Returns:
            JsonResponse: JSON响应
        """
        response_data = {
            'success': True,
            'message': message,
            'data': data,
            'timestamp': timezone.now().isoformat()
        }
        return JsonResponse(response_data, status=status_code)
    
    @staticmethod
    def error_response(message: str = '操作失败', errors: List[str] = None,
                      status_code: int = status.HTTP_400_BAD_REQUEST) -> JsonResponse:
        """
        错误响应
        
        Args:
            message: 错误消息
            errors: 错误详情列表
            status_code: HTTP状态码
            
        Returns:
            JsonResponse: JSON响应
        """
        response_data = {
            'success': False,
            'message': message,
            'errors': errors or [],
            'timestamp': timezone.now().isoformat()
        }
        return JsonResponse(response_data, status=status_code)


class FileUtils:
    """文件处理工具类"""
    
    @staticmethod
    def get_file_extension(filename: str) -> str:
        """
        获取文件扩展名
        
        Args:
            filename: 文件名
            
        Returns:
            str: 文件扩展名
        """
        return filename.split('.')[-1].lower() if '.' in filename else ''
    
    @staticmethod
    def is_image_file(filename: str) -> bool:
        """
        判断是否为图片文件
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 是否为图片文件
        """
        image_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
        return FileUtils.get_file_extension(filename) in image_extensions
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小
        
        Args:
            size_bytes: 文件大小（字节）
            
        Returns:
            str: 格式化后的文件大小
        """
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f}{size_names[i]}"


class DecimalUtils:
    """数值处理工具类"""
    
    @staticmethod
    def safe_decimal(value: Union[str, int, float], default: Decimal = Decimal('0')) -> Decimal:
        """
        安全转换为Decimal
        
        Args:
            value: 待转换的值
            default: 默认值
            
        Returns:
            Decimal: 转换结果
        """
        try:
            return Decimal(str(value))
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def format_money(amount: Union[Decimal, float, int], currency: str = '¥') -> str:
        """
        格式化金额
        
        Args:
            amount: 金额
            currency: 货币符号
            
        Returns:
            str: 格式化后的金额
        """
        decimal_amount = DecimalUtils.safe_decimal(amount)
        return f"{currency}{decimal_amount:.2f}"
