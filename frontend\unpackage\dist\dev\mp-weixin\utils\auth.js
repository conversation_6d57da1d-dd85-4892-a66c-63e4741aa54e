"use strict";
require("../common/vendor.js");
const utils_storage = require("./storage.js");
const TOKEN_KEY = "user_token";
const USER_INFO_KEY = "user_info";
const LOGIN_TIME_KEY = "login_time";
const TOKEN_EXPIRE_TIME = 7 * 24 * 60 * 60 * 1e3;
function getToken() {
  const token = utils_storage.getStorage(TOKEN_KEY);
  const loginTime = utils_storage.getStorage(LOGIN_TIME_KEY);
  if (!token || !loginTime) {
    return null;
  }
  const now = Date.now();
  if (now - loginTime > TOKEN_EXPIRE_TIME) {
    removeToken();
    return null;
  }
  return token;
}
function removeToken() {
  utils_storage.removeStorage(TOKEN_KEY);
  utils_storage.removeStorage(LOGIN_TIME_KEY);
  utils_storage.removeStorage(USER_INFO_KEY);
}
exports.getToken = getToken;
exports.removeToken = removeToken;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/auth.js.map
