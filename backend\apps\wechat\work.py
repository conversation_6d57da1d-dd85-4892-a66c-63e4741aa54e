"""
企业微信模块
"""
import time
import requests
from django.conf import settings
from django.contrib.auth import get_user_model
from .models import WechatConfig

User = get_user_model()


class WechatWork:
    """企业微信API类"""
    
    def __init__(self, config_name='default'):
        # 从数据库获取配置
        config = WechatConfig.get_config('work', config_name)
        self.corp_id = config.get('corp_id', '')
        self.corp_secret = config.get('corp_secret', '')
        self.agent_id = config.get('agent_id', '')
        self.api_base = 'https://qyapi.weixin.qq.com/cgi-bin'
        self._access_token = None
        self._access_token_expires = 0
    
    def get_access_token(self):
        """获取access_token"""
        if self._access_token and time.time() < self._access_token_expires:
            return self._access_token
        
        url = f"{self.api_base}/gettoken"
        params = {
            'corpid': self.corp_id,
            'corpsecret': self.corp_secret
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if data.get('errcode') != 0:
            raise Exception(f"获取access_token失败: {data.get('errmsg', '未知错误')}")
        
        self._access_token = data['access_token']
        self._access_token_expires = time.time() + data['expires_in'] - 300  # 提前5分钟过期
        
        return self._access_token
    
    def get_user_info(self, code):
        """通过code获取用户信息"""
        access_token = self.get_access_token()
        
        # 获取用户ID
        url = f"{self.api_base}/user/getuserinfo"
        params = {
            'access_token': access_token,
            'code': code
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if data.get('errcode') != 0:
            raise Exception(f"获取用户信息失败: {data.get('errmsg', '未知错误')}")
        
        user_id = data.get('UserId')
        if not user_id:
            raise Exception("未获取到用户ID")
        
        # 获取用户详细信息
        return self.get_user_detail(user_id)
    
    def get_user_detail(self, user_id):
        """获取用户详细信息"""
        access_token = self.get_access_token()
        url = f"{self.api_base}/user/get"
        params = {
            'access_token': access_token,
            'userid': user_id
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if data.get('errcode') != 0:
            raise Exception(f"获取用户详细信息失败: {data.get('errmsg', '未知错误')}")
        
        return data
    
    def get_department_list(self, department_id=None):
        """获取部门列表"""
        access_token = self.get_access_token()
        url = f"{self.api_base}/department/list"
        params = {
            'access_token': access_token
        }
        if department_id:
            params['id'] = department_id
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if data.get('errcode') != 0:
            raise Exception(f"获取部门列表失败: {data.get('errmsg', '未知错误')}")
        
        return data.get('department', [])
    
    def get_department_users(self, department_id, fetch_child=False):
        """获取部门成员"""
        access_token = self.get_access_token()
        url = f"{self.api_base}/user/list"
        params = {
            'access_token': access_token,
            'department_id': department_id,
            'fetch_child': 1 if fetch_child else 0
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if data.get('errcode') != 0:
            raise Exception(f"获取部门成员失败: {data.get('errmsg', '未知错误')}")
        
        return data.get('userlist', [])
    
    def sync_contacts(self):
        """同步通讯录"""
        try:
            # 获取所有部门
            departments = self.get_department_list()
            
            # 同步用户
            synced_users = []
            for dept in departments:
                dept_id = dept['id']
                users = self.get_department_users(dept_id)
                
                for user_data in users:
                    user = self.sync_user(user_data)
                    if user:
                        synced_users.append(user)
            
            return {
                'success': True,
                'synced_count': len(synced_users),
                'users': synced_users
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def sync_user(self, user_data):
        """同步单个用户"""
        try:
            user_id = user_data['userid']
            name = user_data.get('name', '')
            email = user_data.get('email', '')
            mobile = user_data.get('mobile', '')
            
            # 查找或创建用户
            user, created = User.objects.get_or_create(
                username=user_id,
                defaults={
                    'first_name': name,
                    'email': email,
                    'phone': mobile,
                    'is_active': user_data.get('status') == 1,
                }
            )
            
            # 更新用户信息
            if not created:
                user.first_name = name
                user.email = email or user.email
                user.phone = mobile or user.phone
                user.is_active = user_data.get('status') == 1
                user.save()
            
            return user
            
        except Exception as e:
            print(f"同步用户失败: {e}")
            return None
    
    def send_message(self, user_ids, content, msg_type='text', agent_id=None):
        """发送消息"""
        access_token = self.get_access_token()
        url = f"{self.api_base}/message/send"
        
        # 构建消息数据
        data = {
            'touser': '|'.join(user_ids) if isinstance(user_ids, list) else user_ids,
            'msgtype': msg_type,
            'agentid': agent_id or 1000001,  # 默认应用ID
        }
        
        if msg_type == 'text':
            data['text'] = {
                'content': content
            }
        elif msg_type == 'textcard':
            data['textcard'] = content
        elif msg_type == 'news':
            data['news'] = content
        
        response = requests.post(url, json=data, params={'access_token': access_token})
        result = response.json()
        
        if result.get('errcode') != 0:
            raise Exception(f"发送消息失败: {result.get('errmsg', '未知错误')}")
        
        return result
    
    def send_text_message(self, user_ids, content, agent_id=None):
        """发送文本消息"""
        return self.send_message(user_ids, content, 'text', agent_id)
    
    def send_textcard_message(self, user_ids, title, description, url, btntxt="详情", agent_id=None):
        """发送文本卡片消息"""
        content = {
            'title': title,
            'description': description,
            'url': url,
            'btntxt': btntxt
        }
        return self.send_message(user_ids, content, 'textcard', agent_id)
    
    def send_news_message(self, user_ids, articles, agent_id=None):
        """发送图文消息"""
        content = {
            'articles': articles
        }
        return self.send_message(user_ids, content, 'news', agent_id)
    
    def create_group_chat(self, name, owner, user_list, chat_id=None):
        """创建群聊"""
        access_token = self.get_access_token()
        url = f"{self.api_base}/appchat/create"
        
        data = {
            'name': name,
            'owner': owner,
            'userlist': user_list
        }
        
        if chat_id:
            data['chatid'] = chat_id
        
        response = requests.post(url, json=data, params={'access_token': access_token})
        result = response.json()
        
        if result.get('errcode') != 0:
            raise Exception(f"创建群聊失败: {result.get('errmsg', '未知错误')}")
        
        return result
    
    def get_group_chat(self, chat_id):
        """获取群聊信息"""
        access_token = self.get_access_token()
        url = f"{self.api_base}/appchat/get"
        params = {
            'access_token': access_token,
            'chatid': chat_id
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if data.get('errcode') != 0:
            raise Exception(f"获取群聊信息失败: {data.get('errmsg', '未知错误')}")
        
        return data
    
    def send_group_message(self, chat_id, content, msg_type='text'):
        """发送群聊消息"""
        access_token = self.get_access_token()
        url = f"{self.api_base}/appchat/send"
        
        data = {
            'chatid': chat_id,
            'msgtype': msg_type,
        }
        
        if msg_type == 'text':
            data['text'] = {
                'content': content
            }
        elif msg_type == 'textcard':
            data['textcard'] = content
        elif msg_type == 'news':
            data['news'] = content
        
        response = requests.post(url, json=data, params={'access_token': access_token})
        result = response.json()
        
        if result.get('errcode') != 0:
            raise Exception(f"发送群聊消息失败: {result.get('errmsg', '未知错误')}")
        
        return result
