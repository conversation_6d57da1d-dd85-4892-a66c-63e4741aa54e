from django.db import transaction
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.exceptions import ValidationError
from decimal import Decimal
import logging

from .models import DistributionRelation, DistributionLevel, CommissionRecord
from .utils import generate_invitation_code, check_level_upgrade, is_distribution_enabled

User = get_user_model()
logger = logging.getLogger(__name__)


class DistributionRelationService:
    """分销关系管理服务"""
    
    @staticmethod
    def create_distributor(user, invitation_code=None):
        """创建分销商"""
        if not is_distribution_enabled():
            raise ValidationError('分销系统未启用')
        
        # 检查用户是否已经是分销商
        if DistributionRelation.objects.filter(user=user).exists():
            raise ValidationError('用户已经是分销商')
        
        with transaction.atomic():
            # 获取默认分销等级
            default_level = DistributionLevel.objects.filter(
                is_active=True
            ).order_by('level').first()
            
            if not default_level:
                raise ValidationError('系统暂未配置分销等级')
            
            # 查找推荐人
            parent = None
            if invitation_code:
                try:
                    parent = DistributionRelation.objects.get(
                        invitation_code=invitation_code,
                        is_active=True
                    )
                except DistributionRelation.DoesNotExist:
                    raise ValidationError('邀请码无效')
            
            # 创建分销关系
            relation = DistributionRelation.objects.create(
                user=user,
                parent=parent,
                level=default_level,
                is_active=True,
                activated_at=timezone.now()
            )
            
            # 更新推荐人的推荐数量
            if parent:
                parent.referral_count += 1
                parent.save()
                
                # 检查推荐人是否可以升级
                check_level_upgrade(parent)
            
            logger.info(f'创建分销商成功: 用户{user.username}, 推荐人{parent.user.username if parent else "无"}')
            return relation
    
    @staticmethod
    def bind_parent(user, invitation_code):
        """绑定推荐人（仅限未绑定推荐人的分销商）"""
        try:
            relation = DistributionRelation.objects.get(user=user, is_active=True)
        except DistributionRelation.DoesNotExist:
            raise ValidationError('用户不是分销商')
        
        if relation.parent:
            raise ValidationError('已有推荐人，无法重新绑定')
        
        try:
            parent = DistributionRelation.objects.get(
                invitation_code=invitation_code,
                is_active=True
            )
        except DistributionRelation.DoesNotExist:
            raise ValidationError('邀请码无效')
        
        # 检查是否会形成循环推荐
        if DistributionRelationService._check_circular_reference(relation, parent):
            raise ValidationError('无法绑定，会形成循环推荐关系')
        
        with transaction.atomic():
            relation.parent = parent
            relation.save()
            
            # 更新推荐人的推荐数量
            parent.referral_count += 1
            parent.save()
            
            # 检查推荐人是否可以升级
            check_level_upgrade(parent)
        
        logger.info(f'绑定推荐人成功: 用户{user.username}, 推荐人{parent.user.username}')
        return relation
    
    @staticmethod
    def _check_circular_reference(relation, potential_parent):
        """检查是否会形成循环推荐关系"""
        current = potential_parent
        max_depth = 10  # 防止无限循环
        depth = 0
        
        while current and depth < max_depth:
            if current == relation:
                return True
            current = current.parent
            depth += 1
        
        return False
    
    @staticmethod
    def get_distribution_tree(relation, max_depth=3):
        """获取分销关系树"""
        def _build_tree(rel, current_depth=0):
            if current_depth >= max_depth:
                return None
            
            tree_node = {
                'relation_id': rel.id,
                'user': {
                    'id': rel.user.id,
                    'username': rel.user.username,
                    'nickname': getattr(rel.user, 'nickname', ''),
                },
                'level': {
                    'name': rel.level.name,
                    'level': rel.level.level,
                    'commission_rate': rel.level.commission_rate,
                },
                'stats': {
                    'total_sales': rel.total_sales,
                    'total_commission': rel.total_commission,
                    'available_commission': rel.available_commission,
                    'referral_count': rel.referral_count,
                },
                'activated_at': rel.activated_at,
                'children': []
            }
            
            # 获取直接下级
            children = rel.children.filter(is_active=True).order_by('-total_sales')
            for child in children:
                child_tree = _build_tree(child, current_depth + 1)
                if child_tree:
                    tree_node['children'].append(child_tree)
            
            return tree_node
        
        return _build_tree(relation)
    
    @staticmethod
    def get_team_statistics(relation):
        """获取团队统计信息"""
        def _calculate_team_stats(rel, depth=0, max_depth=10):
            if depth >= max_depth:
                return {
                    'total_members': 0,
                    'total_sales': Decimal('0.00'),
                    'total_commission': Decimal('0.00'),
                    'active_members': 0
                }
            
            # 当前分销商的统计
            stats = {
                'total_members': 1,
                'total_sales': rel.total_sales,
                'total_commission': rel.total_commission,
                'active_members': 1 if rel.is_active else 0
            }
            
            # 累加下级统计
            for child in rel.children.all():
                child_stats = _calculate_team_stats(child, depth + 1, max_depth)
                stats['total_members'] += child_stats['total_members']
                stats['total_sales'] += child_stats['total_sales']
                stats['total_commission'] += child_stats['total_commission']
                stats['active_members'] += child_stats['active_members']
            
            return stats
        
        team_stats = _calculate_team_stats(relation)
        
        # 添加层级统计
        level_stats = {}
        for level in DistributionLevel.objects.filter(is_active=True):
            count = DistributionRelation.objects.filter(
                level=level,
                is_active=True
            ).count()
            level_stats[level.name] = count
        
        return {
            'team_stats': team_stats,
            'level_distribution': level_stats,
            'direct_children_count': relation.children.filter(is_active=True).count(),
            'invitation_code': relation.invitation_code
        }
    
    @staticmethod
    def transfer_distributor(from_parent, to_parent, distributor):
        """转移分销商（管理员功能）"""
        if not distributor.parent == from_parent:
            raise ValidationError('分销商不属于指定的上级')
        
        # 检查是否会形成循环推荐
        if DistributionRelationService._check_circular_reference(distributor, to_parent):
            raise ValidationError('无法转移，会形成循环推荐关系')
        
        with transaction.atomic():
            # 更新原推荐人的推荐数量
            if from_parent:
                from_parent.referral_count = max(0, from_parent.referral_count - 1)
                from_parent.save()
            
            # 更新新推荐人的推荐数量
            if to_parent:
                to_parent.referral_count += 1
                to_parent.save()
                
                # 检查新推荐人是否可以升级
                check_level_upgrade(to_parent)
            
            # 更新分销商的上级
            distributor.parent = to_parent
            distributor.save()
        
        logger.info(f'转移分销商成功: {distributor.user.username} 从 {from_parent.user.username if from_parent else "无"} 转移到 {to_parent.user.username if to_parent else "无"}')
        return distributor
    
    @staticmethod
    def deactivate_distributor(relation, reason=''):
        """停用分销商"""
        with transaction.atomic():
            relation.is_active = False
            relation.save()
            
            # 更新推荐人的推荐数量
            if relation.parent:
                relation.parent.referral_count = max(0, relation.parent.referral_count - 1)
                relation.parent.save()
            
            # 处理下级分销商（可以选择转移给上级或者保持独立）
            children = relation.children.filter(is_active=True)
            for child in children:
                child.parent = relation.parent  # 转移给上级
                child.save()
        
        logger.info(f'停用分销商: {relation.user.username}, 原因: {reason}')
        return relation
    
    @staticmethod
    def reactivate_distributor(relation):
        """重新激活分销商"""
        with transaction.atomic():
            relation.is_active = True
            relation.activated_at = timezone.now()
            relation.save()
            
            # 更新推荐人的推荐数量
            if relation.parent:
                relation.parent.referral_count += 1
                relation.parent.save()
        
        logger.info(f'重新激活分销商: {relation.user.username}')
        return relation
