# -*- coding: utf-8 -*-
"""
智梦科技系统 - 产品推荐算法模块

产品推荐系统的核心算法，包括：
- 协同过滤推荐
- 基于内容的推荐
- 混合推荐算法
- 热门推荐
- 个性化推荐

创建时间：2025年7月30日
维护人员：智梦科技开发团队
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from django.db.models import Count, Avg, Q
from django.contrib.auth import get_user_model
from .models import RecommendationConfig, UserBehaviorLog
from .services import BehaviorAnalyzer

User = get_user_model()
logger = logging.getLogger(__name__)


class RecommendationEngine:
    """推荐引擎"""
    
    def __init__(self):
        self.algorithms = {
            'collaborative': self._collaborative_filtering,
            'content_based': self._content_based_filtering,
            'hybrid': self._hybrid_recommendation,
            'popularity': self._popularity_based,
            'random': self._random_recommendation
        }
    
    def get_recommendations(self, user_id: Optional[int], target_type: str = 'product',
                          limit: int = 10, context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        获取推荐结果
        
        Args:
            user_id: 用户ID
            target_type: 目标类型
            limit: 推荐数量限制
            context: 上下文信息
            
        Returns:
            list: 推荐结果列表
        """
        try:
            # 获取活跃的推荐配置
            configs = RecommendationConfig.objects.filter(is_active=True).order_by('-weight')
            
            if not configs.exists():
                logger.warning("没有找到活跃的推荐配置")
                return self._popularity_based(user_id, target_type, limit, context)
            
            all_recommendations = []
            total_weight = sum(config.weight for config in configs)
            
            # 根据配置权重分配推荐数量
            for config in configs:
                algorithm = self.algorithms.get(config.algorithm_type)
                if not algorithm:
                    logger.warning(f"未知的推荐算法: {config.algorithm_type}")
                    continue
                
                # 计算该算法应该推荐的数量
                algo_limit = max(1, int(limit * config.weight / total_weight))
                
                try:
                    recommendations = algorithm(user_id, target_type, algo_limit, context, config.parameters)
                    
                    # 添加算法信息
                    for rec in recommendations:
                        rec['algorithm'] = config.algorithm_type
                        rec['config_name'] = config.name
                    
                    all_recommendations.extend(recommendations)
                    
                except Exception as e:
                    logger.error(f"推荐算法 {config.algorithm_type} 执行失败: {e}")
                    continue
            
            # 去重和排序
            unique_recommendations = self._deduplicate_recommendations(all_recommendations)
            sorted_recommendations = sorted(unique_recommendations, key=lambda x: x.get('score', 0), reverse=True)
            
            return sorted_recommendations[:limit]
            
        except Exception as e:
            logger.error(f"获取推荐结果失败: {e}")
            # 降级到热门推荐
            return self._popularity_based(user_id, target_type, limit, context)
    
    def _collaborative_filtering(self, user_id: Optional[int], target_type: str, 
                               limit: int, context: Dict[str, Any] = None,
                               parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        协同过滤推荐
        
        基于用户行为相似性进行推荐
        """
        if not user_id:
            # 对于匿名用户，降级到热门推荐
            return self._popularity_based(user_id, target_type, limit, context)
        
        try:
            # 获取用户的行为历史
            user_behaviors = UserBehaviorLog.objects.filter(
                user_id=user_id,
                target_type=target_type,
                action_type__in=['view', 'purchase', 'favorite']
            ).values_list('target_id', flat=True).distinct()
            
            if not user_behaviors:
                return self._popularity_based(user_id, target_type, limit, context)
            
            # 找到有相似行为的其他用户
            similar_users = UserBehaviorLog.objects.filter(
                target_type=target_type,
                target_id__in=user_behaviors
            ).exclude(user_id=user_id).values('user_id').annotate(
                common_count=Count('target_id')
            ).filter(common_count__gte=2).order_by('-common_count')[:50]
            
            similar_user_ids = [u['user_id'] for u in similar_users]
            
            # 获取相似用户喜欢但当前用户未接触的项目
            recommendations = UserBehaviorLog.objects.filter(
                user_id__in=similar_user_ids,
                target_type=target_type,
                action_type__in=['purchase', 'favorite']
            ).exclude(target_id__in=user_behaviors).values('target_id').annotate(
                score=Count('id')
            ).order_by('-score')[:limit]
            
            result = []
            for rec in recommendations:
                result.append({
                    'target_id': rec['target_id'],
                    'target_type': target_type,
                    'score': rec['score'],
                    'reason': '基于相似用户喜好推荐'
                })
            
            return result
            
        except Exception as e:
            logger.error(f"协同过滤推荐失败: {e}")
            return []
    
    def _content_based_filtering(self, user_id: Optional[int], target_type: str,
                               limit: int, context: Dict[str, Any] = None,
                               parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        基于内容的推荐
        
        基于用户历史偏好的内容特征进行推荐
        """
        try:
            if not user_id:
                return self._popularity_based(user_id, target_type, limit, context)
            
            # 获取用户的行为偏好
            user_preferences = self._analyze_user_preferences(user_id, target_type)
            
            if not user_preferences:
                return self._popularity_based(user_id, target_type, limit, context)
            
            # 基于偏好特征推荐相似内容
            # 这里需要根据具体的内容特征来实现
            # 暂时使用简化的实现
            
            popular_items = BehaviorAnalyzer.get_popular_targets(target_type, days=30, limit=limit*2)
            
            result = []
            for item in popular_items[:limit]:
                result.append({
                    'target_id': item['target_id'],
                    'target_type': target_type,
                    'score': item['view_count'] * 0.8,  # 降低权重以区分算法
                    'reason': '基于内容相似性推荐'
                })
            
            return result
            
        except Exception as e:
            logger.error(f"基于内容的推荐失败: {e}")
            return []
    
    def _hybrid_recommendation(self, user_id: Optional[int], target_type: str,
                             limit: int, context: Dict[str, Any] = None,
                             parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        混合推荐算法
        
        结合多种推荐算法的结果
        """
        try:
            # 获取协同过滤推荐
            collaborative_recs = self._collaborative_filtering(user_id, target_type, limit//2, context)
            
            # 获取基于内容的推荐
            content_recs = self._content_based_filtering(user_id, target_type, limit//2, context)
            
            # 获取热门推荐作为补充
            popular_recs = self._popularity_based(user_id, target_type, limit//4, context)
            
            # 合并结果
            all_recs = collaborative_recs + content_recs + popular_recs
            
            # 去重和重新评分
            unique_recs = self._deduplicate_recommendations(all_recs)
            
            # 混合评分
            for rec in unique_recs:
                rec['score'] = rec.get('score', 0) * 1.2  # 提高混合推荐的权重
                rec['reason'] = '基于混合算法推荐'
            
            return sorted(unique_recs, key=lambda x: x['score'], reverse=True)[:limit]
            
        except Exception as e:
            logger.error(f"混合推荐失败: {e}")
            return []
    
    def _popularity_based(self, user_id: Optional[int], target_type: str,
                         limit: int, context: Dict[str, Any] = None,
                         parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        基于热门度的推荐
        
        推荐最受欢迎的项目
        """
        try:
            days = parameters.get('days', 7) if parameters else 7
            popular_items = BehaviorAnalyzer.get_popular_targets(target_type, days=days, limit=limit)
            
            result = []
            for item in popular_items:
                result.append({
                    'target_id': item['target_id'],
                    'target_type': target_type,
                    'score': item['view_count'],
                    'reason': f'近{days}天热门推荐'
                })
            
            return result
            
        except Exception as e:
            logger.error(f"热门推荐失败: {e}")
            return []
    
    def _random_recommendation(self, user_id: Optional[int], target_type: str,
                             limit: int, context: Dict[str, Any] = None,
                             parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        随机推荐
        
        随机推荐一些项目，增加推荐的多样性
        """
        try:
            import random
            
            # 获取所有可推荐的项目
            all_targets = UserBehaviorLog.objects.filter(
                target_type=target_type
            ).values_list('target_id', flat=True).distinct()
            
            # 随机选择
            random_targets = random.sample(list(all_targets), min(limit, len(all_targets)))
            
            result = []
            for target_id in random_targets:
                result.append({
                    'target_id': target_id,
                    'target_type': target_type,
                    'score': random.uniform(0.1, 0.5),  # 随机推荐给较低分数
                    'reason': '随机推荐，发现新内容'
                })
            
            return result
            
        except Exception as e:
            logger.error(f"随机推荐失败: {e}")
            return []
    
    def _analyze_user_preferences(self, user_id: int, target_type: str) -> Dict[str, Any]:
        """
        分析用户偏好
        
        Args:
            user_id: 用户ID
            target_type: 目标类型
            
        Returns:
            dict: 用户偏好分析结果
        """
        try:
            # 获取用户行为统计
            behavior_stats = BehaviorAnalyzer.get_user_behavior_stats(user_id, days=90)
            
            # 分析偏好的行为类型
            preferred_actions = []
            for action_stat in behavior_stats.get('action_stats', []):
                if action_stat['count'] > 5:  # 阈值可配置
                    preferred_actions.append(action_stat['action_type'])
            
            return {
                'preferred_actions': preferred_actions,
                'total_behaviors': behavior_stats.get('total_behaviors', 0),
                'activity_level': 'high' if behavior_stats.get('total_behaviors', 0) > 50 else 'low'
            }
            
        except Exception as e:
            logger.error(f"分析用户偏好失败: {e}")
            return {}
    
    def _deduplicate_recommendations(self, recommendations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        去除重复推荐
        
        Args:
            recommendations: 推荐列表
            
        Returns:
            list: 去重后的推荐列表
        """
        seen = set()
        unique_recs = []
        
        for rec in recommendations:
            key = (rec['target_id'], rec['target_type'])
            if key not in seen:
                seen.add(key)
                unique_recs.append(rec)
        
        return unique_recs


# 全局推荐引擎实例
recommendation_engine = RecommendationEngine()
