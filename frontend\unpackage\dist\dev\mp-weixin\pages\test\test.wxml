<view id="{{j}}" change:eS="{{uV.sS}}" eS="{{$eS[j]}}" change:eA="{{uV.sA}}" eA="{{$eA[j]}}" class="{{['test-page', 'data-v-f37d094f', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}"><view class="header data-v-f37d094f"><text class="title data-v-f37d094f">API测试页面</text></view><view class="test-section data-v-f37d094f"><text class="section-title data-v-f37d094f">轮播图API测试</text><button bindtap="{{a}}" class="test-btn data-v-f37d094f">测试轮播图API</button><view wx:if="{{b}}" class="loading data-v-f37d094f"><text class="data-v-f37d094f">加载中...</text></view><view wx:if="{{c}}" class="error data-v-f37d094f"><text class="data-v-f37d094f">错误: {{d}}</text></view><view wx:if="{{e}}" class="result data-v-f37d094f"><text class="result-title data-v-f37d094f">轮播图数据:</text><view wx:for="{{f}}" wx:for-item="banner" wx:key="c" class="banner-item data-v-f37d094f"><text class="banner-title data-v-f37d094f">{{banner.a}}</text><text class="banner-url data-v-f37d094f">{{banner.b}}</text></view></view></view><view class="test-section data-v-f37d094f"><text class="section-title data-v-f37d094f">配置信息</text><view class="config-info data-v-f37d094f"><text class="data-v-f37d094f">API地址: {{g}}</text><text class="data-v-f37d094f">CDN地址: {{h}}</text><text class="data-v-f37d094f">当前环境: {{i}}</text></view></view></view><wxs src="/common/uniView.wxs" module="uV"/>
