from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    DistributionConfig, DistributionLevel, DistributionRelation,
    CommissionRecord, WithdrawalRecord, PromotionCode, MarketingActivity
)

User = get_user_model()


class DistributionConfigSerializer(serializers.ModelSerializer):
    """分销配置序列化器"""
    class Meta:
        model = DistributionConfig
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']


class DistributionLevelSerializer(serializers.ModelSerializer):
    """分销等级序列化器"""
    distributor_count = serializers.SerializerMethodField()
    
    class Meta:
        model = DistributionLevel
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']
    
    def get_distributor_count(self, obj):
        """获取该等级的分销商数量"""
        return obj.distributors.filter(is_active=True).count()


class UserSimpleSerializer(serializers.ModelSerializer):
    """用户简单信息序列化器"""
    class Meta:
        model = User
        fields = ['id', 'username', 'nickname', 'avatar']


class DistributionRelationSerializer(serializers.ModelSerializer):
    """分销关系序列化器"""
    user_info = UserSimpleSerializer(source='user', read_only=True)
    parent_info = UserSimpleSerializer(source='parent.user', read_only=True)
    level_info = DistributionLevelSerializer(source='level', read_only=True)
    children_count = serializers.SerializerMethodField()
    team_count = serializers.SerializerMethodField()
    
    class Meta:
        model = DistributionRelation
        fields = [
            'id', 'user', 'user_info', 'parent', 'parent_info', 
            'level', 'level_info', 'invitation_code', 'total_sales',
            'total_commission', 'available_commission', 'referral_count',
            'children_count', 'team_count', 'is_active', 'activated_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'invitation_code', 'total_sales', 'total_commission',
            'available_commission', 'referral_count', 'created_at', 'updated_at'
        ]
    
    def get_children_count(self, obj):
        """获取直接下级数量"""
        return obj.get_children_count()
    
    def get_team_count(self, obj):
        """获取团队总人数"""
        return obj.get_total_team_count()


class CommissionRecordSerializer(serializers.ModelSerializer):
    """佣金记录序列化器"""
    distributor_info = serializers.SerializerMethodField()
    order_info = serializers.SerializerMethodField()
    commission_type_display = serializers.CharField(source='get_commission_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = CommissionRecord
        fields = [
            'id', 'distributor', 'distributor_info', 'order', 'order_info',
            'commission_type', 'commission_type_display', 'commission_rate',
            'order_amount', 'commission_amount', 'status', 'status_display',
            'settled_at', 'remark', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_distributor_info(self, obj):
        """获取分销商信息"""
        return {
            'id': obj.distributor.user.id,
            'username': obj.distributor.user.username,
            'nickname': getattr(obj.distributor.user, 'nickname', ''),
        }
    
    def get_order_info(self, obj):
        """获取订单信息"""
        return {
            'id': obj.order.id,
            'order_no': obj.order.order_no,
            'total_amount': obj.order.total_amount,
            'final_amount': obj.order.final_amount,
        }


class WithdrawalRecordSerializer(serializers.ModelSerializer):
    """提现记录序列化器"""
    distributor_info = serializers.SerializerMethodField()
    method_display = serializers.CharField(source='get_method_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = WithdrawalRecord
        fields = [
            'id', 'distributor', 'distributor_info', 'withdrawal_no',
            'amount', 'fee', 'actual_amount', 'method', 'method_display',
            'account_info', 'status', 'status_display', 'processed_at',
            'remark', 'admin_remark', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'withdrawal_no', 'actual_amount', 'processed_at',
            'created_at', 'updated_at'
        ]
    
    def get_distributor_info(self, obj):
        """获取分销商信息"""
        return {
            'id': obj.distributor.user.id,
            'username': obj.distributor.user.username,
            'nickname': getattr(obj.distributor.user, 'nickname', ''),
        }


class PromotionCodeSerializer(serializers.ModelSerializer):
    """推广码序列化器"""
    distributor_info = serializers.SerializerMethodField()
    code_type_display = serializers.CharField(source='get_code_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    is_valid = serializers.SerializerMethodField()
    
    class Meta:
        model = PromotionCode
        fields = [
            'id', 'distributor', 'distributor_info', 'code', 'name',
            'code_type', 'code_type_display', 'target_id', 'commission_rate',
            'usage_limit', 'used_count', 'start_time', 'end_time',
            'status', 'status_display', 'is_valid', 'description',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['used_count', 'created_at', 'updated_at']
    
    def get_distributor_info(self, obj):
        """获取分销商信息"""
        return {
            'id': obj.distributor.user.id,
            'username': obj.distributor.user.username,
            'nickname': getattr(obj.distributor.user, 'nickname', ''),
        }
    
    def get_is_valid(self, obj):
        """获取推广码是否有效"""
        return obj.is_valid()


class MarketingActivitySerializer(serializers.ModelSerializer):
    """营销活动序列化器"""
    activity_type_display = serializers.CharField(source='get_activity_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    creator_info = UserSimpleSerializer(source='creator', read_only=True)
    is_active = serializers.SerializerMethodField()
    remaining_budget = serializers.SerializerMethodField()
    
    class Meta:
        model = MarketingActivity
        fields = [
            'id', 'name', 'activity_type', 'activity_type_display',
            'description', 'rules', 'start_time', 'end_time',
            'target_users', 'budget', 'used_budget', 'remaining_budget',
            'participant_count', 'status', 'status_display',
            'creator', 'creator_info', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'used_budget', 'participant_count', 'created_at', 'updated_at'
        ]
    
    def get_is_active(self, obj):
        """获取活动是否进行中"""
        return obj.is_active()
    
    def get_remaining_budget(self, obj):
        """获取剩余预算"""
        if obj.budget:
            return obj.budget - obj.used_budget
        return None


class JoinDistributionSerializer(serializers.Serializer):
    """加入分销序列化器"""
    invitation_code = serializers.CharField(
        max_length=20,
        required=False,
        help_text='邀请码（可选）'
    )
    
    def validate_invitation_code(self, value):
        """验证邀请码"""
        if value:
            try:
                relation = DistributionRelation.objects.get(
                    invitation_code=value,
                    is_active=True
                )
                return value
            except DistributionRelation.DoesNotExist:
                raise serializers.ValidationError('邀请码无效')
        return value


class ApplyWithdrawalSerializer(serializers.Serializer):
    """申请提现序列化器"""
    amount = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=0.01,
        help_text='提现金额'
    )
    method = serializers.ChoiceField(
        choices=WithdrawalRecord.WITHDRAWAL_METHODS,
        help_text='提现方式'
    )
    account_info = serializers.JSONField(help_text='账户信息')
    remark = serializers.CharField(
        max_length=500,
        required=False,
        allow_blank=True,
        help_text='提现备注'
    )
    
    def validate_amount(self, value):
        """验证提现金额"""
        user = self.context['request'].user
        try:
            relation = DistributionRelation.objects.get(user=user, is_active=True)
            if value > relation.available_commission:
                raise serializers.ValidationError('提现金额不能超过可提现佣金')
        except DistributionRelation.DoesNotExist:
            raise serializers.ValidationError('您还不是分销商')
        return value


class BindParentSerializer(serializers.Serializer):
    """绑定推荐人序列化器"""
    invitation_code = serializers.CharField(
        max_length=20,
        required=True,
        help_text='邀请码'
    )

    def validate_invitation_code(self, value):
        """验证邀请码"""
        try:
            relation = DistributionRelation.objects.get(
                invitation_code=value,
                is_active=True
            )
            return value
        except DistributionRelation.DoesNotExist:
            raise serializers.ValidationError('邀请码无效')


class TransferDistributorSerializer(serializers.Serializer):
    """转移分销商序列化器"""
    to_parent_id = serializers.IntegerField(
        required=False,
        allow_null=True,
        help_text='目标上级分销商ID（为空表示转为根分销商）'
    )
    reason = serializers.CharField(
        max_length=500,
        required=False,
        allow_blank=True,
        help_text='转移原因'
    )

    def validate_to_parent_id(self, value):
        """验证目标上级分销商"""
        if value is not None:
            try:
                DistributionRelation.objects.get(id=value, is_active=True)
            except DistributionRelation.DoesNotExist:
                raise serializers.ValidationError('目标上级分销商不存在')
        return value


class DistributionTreeSerializer(serializers.Serializer):
    """分销关系树序列化器"""
    relation_id = serializers.IntegerField()
    user = serializers.DictField()
    level = serializers.DictField()
    stats = serializers.DictField()
    activated_at = serializers.DateTimeField()
    children = serializers.ListField(child=serializers.DictField(), default=list)


class TeamStatisticsSerializer(serializers.Serializer):
    """团队统计序列化器"""
    team_stats = serializers.DictField()
    level_distribution = serializers.DictField()
    direct_children_count = serializers.IntegerField()
    invitation_code = serializers.CharField()
