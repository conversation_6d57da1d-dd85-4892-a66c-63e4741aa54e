# Generated by Django 5.2.4 on 2025-07-26 07:52

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('wechat', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='WechatConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('platform', models.CharField(choices=[('official', '公众号'), ('miniprogram', '小程序'), ('work', '企业微信'), ('payment', '微信支付')], max_length=20, verbose_name='平台类型')),
                ('name', models.CharField(max_length=100, verbose_name='配置名称')),
                ('app_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='应用ID')),
                ('app_secret', models.CharField(blank=True, max_length=200, null=True, verbose_name='应用密钥')),
                ('token', models.CharField(blank=True, max_length=100, null=True, verbose_name='Token')),
                ('encoding_aes_key', models.CharField(blank=True, max_length=200, null=True, verbose_name='消息加解密密钥')),
                ('corp_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='企业ID')),
                ('corp_secret', models.CharField(blank=True, max_length=200, null=True, verbose_name='企业密钥')),
                ('agent_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='应用ID')),
                ('mch_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='商户号')),
                ('mch_key', models.CharField(blank=True, max_length=200, null=True, verbose_name='商户密钥')),
                ('cert_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='证书路径')),
                ('key_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='私钥路径')),
                ('extra_config', models.JSONField(blank=True, default=dict, verbose_name='额外配置')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('description', models.TextField(blank=True, null=True, verbose_name='配置说明')),
            ],
            options={
                'verbose_name': '微信配置',
                'verbose_name_plural': '微信配置',
                'ordering': ['platform', 'name'],
                'unique_together': {('platform', 'name')},
            },
        ),
    ]
