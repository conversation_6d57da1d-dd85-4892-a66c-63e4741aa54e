# Generated by Django 5.2.4 on 2025-07-26 07:42

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WechatTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('template_id', models.Char<PERSON>ield(max_length=100, unique=True, verbose_name='模板ID')),
                ('title', models.CharField(max_length=100, verbose_name='模板标题')),
                ('primary_industry', models.CharField(max_length=50, verbose_name='主行业')),
                ('deputy_industry', models.CharField(max_length=50, verbose_name='副行业')),
                ('content', models.TextField(verbose_name='模板内容')),
                ('example', models.TextField(blank=True, null=True, verbose_name='模板示例')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '微信模板消息',
                'verbose_name_plural': '微信模板消息',
            },
        ),
        migrations.CreateModel(
            name='WechatMenu',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('name', models.CharField(max_length=40, verbose_name='菜单标题')),
                ('type', models.CharField(blank=True, choices=[('click', '点击推事件'), ('view', '跳转URL'), ('scancode_push', '扫码推事件'), ('scancode_waitmsg', '扫码推事件且弹出"消息接收中"提示框'), ('pic_sysphoto', '弹出系统拍照发图'), ('pic_photo_or_album', '弹出拍照或者相册发图'), ('pic_weixin', '弹出微信相册发图器'), ('location_select', '弹出地理位置选择器'), ('media_id', '下发消息（除文本消息）'), ('view_limited', '跳转图文消息URL')], max_length=20, null=True, verbose_name='菜单类型')),
                ('key', models.CharField(blank=True, max_length=128, null=True, verbose_name='菜单KEY值')),
                ('url', models.URLField(blank=True, null=True, verbose_name='网页链接')),
                ('media_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='媒体文件ID')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='wechat.wechatmenu', verbose_name='父菜单')),
            ],
            options={
                'verbose_name': '微信菜单',
                'verbose_name_plural': '微信菜单',
                'ordering': ['sort_order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='WechatUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('openid', models.CharField(max_length=100, unique=True, verbose_name='微信OpenID')),
                ('unionid', models.CharField(blank=True, max_length=100, null=True, verbose_name='微信UnionID')),
                ('nickname', models.CharField(blank=True, max_length=100, null=True, verbose_name='微信昵称')),
                ('avatar_url', models.URLField(blank=True, null=True, verbose_name='微信头像URL')),
                ('gender', models.IntegerField(choices=[(0, '未知'), (1, '男'), (2, '女')], default=0, verbose_name='性别')),
                ('city', models.CharField(blank=True, max_length=50, null=True, verbose_name='城市')),
                ('province', models.CharField(blank=True, max_length=50, null=True, verbose_name='省份')),
                ('country', models.CharField(blank=True, max_length=50, null=True, verbose_name='国家')),
                ('language', models.CharField(default='zh_CN', max_length=20, verbose_name='语言')),
                ('subscribe', models.BooleanField(default=False, verbose_name='是否关注公众号')),
                ('subscribe_time', models.DateTimeField(blank=True, null=True, verbose_name='关注时间')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='wechat_profile', to=settings.AUTH_USER_MODEL, verbose_name='关联用户')),
            ],
            options={
                'verbose_name': '微信用户',
                'verbose_name_plural': '微信用户',
            },
        ),
        migrations.CreateModel(
            name='WechatTemplateMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('data', models.JSONField(verbose_name='模板数据')),
                ('url', models.URLField(blank=True, null=True, verbose_name='跳转链接')),
                ('miniprogram', models.JSONField(blank=True, null=True, verbose_name='小程序信息')),
                ('status', models.CharField(choices=[('pending', '待发送'), ('sent', '已发送'), ('failed', '发送失败')], default='pending', max_length=10, verbose_name='发送状态')),
                ('msg_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='消息ID')),
                ('error_msg', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='发送时间')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='wechat.wechattemplate', verbose_name='模板')),
                ('wechat_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='template_messages', to='wechat.wechatuser', verbose_name='微信用户')),
            ],
            options={
                'verbose_name': '模板消息记录',
                'verbose_name_plural': '模板消息记录',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WechatMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('msg_id', models.CharField(max_length=100, unique=True, verbose_name='消息ID')),
                ('msg_type', models.CharField(choices=[('text', '文本消息'), ('image', '图片消息'), ('voice', '语音消息'), ('video', '视频消息'), ('music', '音乐消息'), ('news', '图文消息'), ('location', '位置消息'), ('link', '链接消息'), ('event', '事件消息')], max_length=20, verbose_name='消息类型')),
                ('direction', models.CharField(choices=[('in', '接收'), ('out', '发送')], max_length=3, verbose_name='消息方向')),
                ('content', models.TextField(verbose_name='消息内容')),
                ('media_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='媒体ID')),
                ('pic_url', models.URLField(blank=True, null=True, verbose_name='图片URL')),
                ('location_x', models.FloatField(blank=True, null=True, verbose_name='地理位置纬度')),
                ('location_y', models.FloatField(blank=True, null=True, verbose_name='地理位置经度')),
                ('scale', models.IntegerField(blank=True, null=True, verbose_name='地图缩放大小')),
                ('label', models.CharField(blank=True, max_length=200, null=True, verbose_name='地理位置信息')),
                ('title', models.CharField(blank=True, max_length=200, null=True, verbose_name='消息标题')),
                ('description', models.TextField(blank=True, null=True, verbose_name='消息描述')),
                ('url', models.URLField(blank=True, null=True, verbose_name='消息链接')),
                ('event', models.CharField(blank=True, max_length=50, null=True, verbose_name='事件类型')),
                ('event_key', models.CharField(blank=True, max_length=200, null=True, verbose_name='事件KEY值')),
                ('wechat_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='wechat.wechatuser', verbose_name='微信用户')),
            ],
            options={
                'verbose_name': '微信消息',
                'verbose_name_plural': '微信消息',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='wechatuser',
            index=models.Index(fields=['openid'], name='wechat_wech_openid_0f5457_idx'),
        ),
        migrations.AddIndex(
            model_name='wechatuser',
            index=models.Index(fields=['unionid'], name='wechat_wech_unionid_c5c5af_idx'),
        ),
        migrations.AddIndex(
            model_name='wechatmessage',
            index=models.Index(fields=['msg_id'], name='wechat_wech_msg_id_d2ebf6_idx'),
        ),
        migrations.AddIndex(
            model_name='wechatmessage',
            index=models.Index(fields=['wechat_user', '-created_at'], name='wechat_wech_wechat__ba0680_idx'),
        ),
        migrations.AddIndex(
            model_name='wechatmessage',
            index=models.Index(fields=['msg_type'], name='wechat_wech_msg_typ_93f751_idx'),
        ),
        migrations.AddIndex(
            model_name='wechatmessage',
            index=models.Index(fields=['direction'], name='wechat_wech_directi_1e812e_idx'),
        ),
    ]
