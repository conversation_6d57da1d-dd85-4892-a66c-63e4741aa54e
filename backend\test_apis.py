#!/usr/bin/env python
"""
API测试脚本
用于验证所有API接口是否正常工作
"""

import requests
import json

BASE_URL = 'http://127.0.0.1:8000'

def test_core_apis():
    """测试核心API"""
    print("🔧 测试核心API...")
    
    # 测试系统信息
    try:
        response = requests.get(f'{BASE_URL}/api/v1/core/system/info/')
        print(f"  系统信息API: {response.status_code} - {response.json()['name'] if response.status_code == 200 else 'Failed'}")
    except Exception as e:
        print(f"  系统信息API: 失败 - {e}")
    
    # 测试健康检查
    try:
        response = requests.get(f'{BASE_URL}/api/v1/core/system/health/')
        print(f"  健康检查API: {response.status_code} - {response.json()['status'] if response.status_code == 200 else 'Failed'}")
    except Exception as e:
        print(f"  健康检查API: 失败 - {e}")

def test_user_apis():
    """测试用户API"""
    print("👤 测试用户API...")
    
    # 测试用户注册
    try:
        response = requests.get(f'{BASE_URL}/api/v1/users/users/')
        print(f"  用户列表API: {response.status_code}")
    except Exception as e:
        print(f"  用户列表API: 失败 - {e}")

def test_product_apis():
    """测试产品API"""
    print("🛒 测试产品API...")
    
    # 测试产品分类
    try:
        response = requests.get(f'{BASE_URL}/api/v1/products/categories/')
        print(f"  产品分类API: {response.status_code}")
    except Exception as e:
        print(f"  产品分类API: 失败 - {e}")
    
    # 测试产品列表
    try:
        response = requests.get(f'{BASE_URL}/api/v1/products/products/')
        print(f"  产品列表API: {response.status_code}")
    except Exception as e:
        print(f"  产品列表API: 失败 - {e}")

def test_order_apis():
    """测试订单API"""
    print("📋 测试订单API...")
    
    # 测试订单列表
    try:
        response = requests.get(f'{BASE_URL}/api/v1/orders/orders/')
        print(f"  订单列表API: {response.status_code}")
    except Exception as e:
        print(f"  订单列表API: 失败 - {e}")

def test_admin_access():
    """测试管理后台访问"""
    print("🔑 测试管理后台...")
    
    try:
        response = requests.get(f'{BASE_URL}/admin/', allow_redirects=False)
        print(f"  管理后台访问: {response.status_code}")
    except Exception as e:
        print(f"  管理后台访问: 失败 - {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试智梦科技系统API...")
    print("="*50)
    
    test_core_apis()
    print()
    test_user_apis()
    print()
    test_product_apis()
    print()
    test_order_apis()
    print()
    test_admin_access()
    
    print("="*50)
    print("✅ API测试完成！")

if __name__ == '__main__':
    main() 