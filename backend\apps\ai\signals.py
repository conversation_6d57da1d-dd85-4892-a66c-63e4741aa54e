# -*- coding: utf-8 -*-
"""
智梦科技系统 - AI功能信号处理

AI功能模块的Django信号处理器
"""

import logging
from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.utils import timezone
from .models import (
    AIServiceConfig, ChatbotConversation, ChatbotMessage,
    RecommendationConfig, UserBehaviorLog, AITask
)
from .services import ai_service_manager

logger = logging.getLogger(__name__)


@receiver(post_save, sender=AIServiceConfig)
def ai_service_config_saved(sender, instance, created, **kwargs):
    """AI服务配置保存后的处理"""
    try:
        if created:
            logger.info(f"新增AI服务配置: {instance.name}")
        else:
            logger.info(f"更新AI服务配置: {instance.name}")
        
        # 重新加载服务配置
        ai_service_manager.reload_services()
        
    except Exception as e:
        logger.error(f"处理AI服务配置保存信号失败: {e}")


@receiver(post_delete, sender=AIServiceConfig)
def ai_service_config_deleted(sender, instance, **kwargs):
    """AI服务配置删除后的处理"""
    try:
        logger.info(f"删除AI服务配置: {instance.name}")
        
        # 重新加载服务配置
        ai_service_manager.reload_services()
        
    except Exception as e:
        logger.error(f"处理AI服务配置删除信号失败: {e}")


@receiver(post_save, sender=ChatbotConversation)
def chatbot_conversation_saved(sender, instance, created, **kwargs):
    """智能客服对话保存后的处理"""
    try:
        if created:
            logger.info(f"创建新的智能客服对话: {instance.session_id}")
            
            # 创建欢迎消息
            welcome_message = "您好！我是智梦科技的智能客服助手，很高兴为您服务！请问有什么可以帮助您的吗？"
            
            ChatbotMessage.objects.create(
                conversation=instance,
                message_type='bot',
                content=welcome_message,
                metadata={'type': 'welcome', 'auto_generated': True}
            )
            
        else:
            # 如果对话状态变为已结束，设置结束时间
            if instance.status == 'closed' and not instance.end_time:
                instance.end_time = timezone.now()
                instance.save(update_fields=['end_time'])
                logger.info(f"智能客服对话已结束: {instance.session_id}")
        
    except Exception as e:
        logger.error(f"处理智能客服对话保存信号失败: {e}")


@receiver(post_save, sender=ChatbotMessage)
def chatbot_message_saved(sender, instance, created, **kwargs):
    """智能客服消息保存后的处理"""
    try:
        if created:
            logger.debug(f"新增智能客服消息: {instance.conversation.session_id} - {instance.message_type}")
            
            # 如果是用户消息，可以触发一些自动化处理
            if instance.message_type == 'user':
                # 这里可以添加消息分析、意图识别等逻辑
                pass
        
    except Exception as e:
        logger.error(f"处理智能客服消息保存信号失败: {e}")


@receiver(post_save, sender=RecommendationConfig)
def recommendation_config_saved(sender, instance, created, **kwargs):
    """推荐算法配置保存后的处理"""
    try:
        if created:
            logger.info(f"新增推荐算法配置: {instance.name}")
        else:
            logger.info(f"更新推荐算法配置: {instance.name}")
        
        # 这里可以添加推荐引擎重新加载配置的逻辑
        
    except Exception as e:
        logger.error(f"处理推荐算法配置保存信号失败: {e}")


@receiver(post_save, sender=UserBehaviorLog)
def user_behavior_log_saved(sender, instance, created, **kwargs):
    """用户行为日志保存后的处理"""
    try:
        if created:
            logger.debug(f"记录用户行为: {instance.user or '匿名用户'} - {instance.action_type}")
            
            # 这里可以添加实时行为分析、推荐更新等逻辑
            # 例如：如果是购买行为，可以触发推荐模型更新
            if instance.action_type == 'purchase':
                # 触发推荐模型更新
                pass
        
    except Exception as e:
        logger.error(f"处理用户行为日志保存信号失败: {e}")


@receiver(pre_save, sender=AITask)
def ai_task_pre_save(sender, instance, **kwargs):
    """AI任务保存前的处理"""
    try:
        # 如果任务状态从pending变为running，设置开始时间
        if instance.pk:
            try:
                old_instance = AITask.objects.get(pk=instance.pk)
                if old_instance.status == 'pending' and instance.status == 'running':
                    instance.start_time = timezone.now()
                    logger.info(f"AI任务开始执行: {instance.name}")
                
                # 如果任务状态变为completed或failed，设置结束时间
                elif old_instance.status == 'running' and instance.status in ['completed', 'failed']:
                    instance.end_time = timezone.now()
                    logger.info(f"AI任务执行结束: {instance.name} - {instance.status}")
                    
            except AITask.DoesNotExist:
                pass
        
    except Exception as e:
        logger.error(f"处理AI任务保存前信号失败: {e}")


@receiver(post_save, sender=AITask)
def ai_task_saved(sender, instance, created, **kwargs):
    """AI任务保存后的处理"""
    try:
        if created:
            logger.info(f"创建新的AI任务: {instance.name} - {instance.task_type}")
        
        # 根据任务状态执行相应的处理
        if instance.status == 'completed':
            logger.info(f"AI任务完成: {instance.name}")
            # 这里可以添加任务完成后的通知、清理等逻辑
            
        elif instance.status == 'failed':
            logger.warning(f"AI任务失败: {instance.name} - {instance.error_message}")
            # 这里可以添加任务失败后的通知、重试等逻辑
        
    except Exception as e:
        logger.error(f"处理AI任务保存信号失败: {e}")


# 自定义信号
from django.dispatch import Signal

# 定义自定义信号
ai_service_status_changed = Signal()
recommendation_updated = Signal()
behavior_pattern_detected = Signal()


@receiver(ai_service_status_changed)
def handle_ai_service_status_changed(sender, service_name, old_status, new_status, **kwargs):
    """处理AI服务状态变化"""
    try:
        logger.info(f"AI服务状态变化: {service_name} - {old_status} -> {new_status}")
        
        # 这里可以添加服务状态变化的处理逻辑
        # 例如：发送通知、更新监控指标等
        
    except Exception as e:
        logger.error(f"处理AI服务状态变化信号失败: {e}")


@receiver(recommendation_updated)
def handle_recommendation_updated(sender, user_id, target_type, recommendations, **kwargs):
    """处理推荐更新"""
    try:
        logger.debug(f"推荐更新: 用户{user_id} - {target_type} - {len(recommendations)}个推荐")
        
        # 这里可以添加推荐更新的处理逻辑
        # 例如：缓存推荐结果、发送推送通知等
        
    except Exception as e:
        logger.error(f"处理推荐更新信号失败: {e}")


@receiver(behavior_pattern_detected)
def handle_behavior_pattern_detected(sender, user_id, pattern_type, pattern_data, **kwargs):
    """处理行为模式检测"""
    try:
        logger.info(f"检测到行为模式: 用户{user_id} - {pattern_type}")
        
        # 这里可以添加行为模式检测的处理逻辑
        # 例如：触发个性化推荐、发送营销活动等
        
    except Exception as e:
        logger.error(f"处理行为模式检测信号失败: {e}")


def connect_signals():
    """连接所有信号处理器"""
    logger.info("AI功能模块信号处理器已连接")


def disconnect_signals():
    """断开所有信号处理器"""
    logger.info("AI功能模块信号处理器已断开")
