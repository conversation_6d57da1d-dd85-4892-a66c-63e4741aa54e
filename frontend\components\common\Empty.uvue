<template>
	<view class="empty-container" :class="containerClass">
		<!-- 空状态图标 -->
		<view class="empty-icon">
			<Icon :name="iconName" :size="iconSize" :color="iconColor" />
		</view>
		
		<!-- 空状态文本 -->
		<text class="empty-text" v-if="text">{{ text }}</text>
		<text class="empty-description" v-if="description">{{ description }}</text>
		
		<!-- 操作按钮 -->
		<view class="empty-action" v-if="showAction">
			<view class="action-button" @click="handleAction">
				<text class="button-text">{{ actionText }}</text>
			</view>
		</view>
		
		<!-- 自定义插槽 -->
		<slot></slot>
	</view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Icon from './Icon.uvue'

// Props定义
interface Props {
	type?: 'default' | 'search' | 'network' | 'cart' | 'order' | 'product' | 'custom'
	text?: string
	description?: string
	showAction?: boolean
	actionText?: string
	size?: 'small' | 'medium' | 'large'
	iconName?: string
	iconSize?: number
	iconColor?: string
}

const props = withDefaults(defineProps<Props>(), {
	type: 'default',
	text: '',
	description: '',
	showAction: false,
	actionText: '重新加载',
	size: 'medium',
	iconName: '',
	iconSize: 80,
	iconColor: '#CCCCCC'
})

// Emits定义
const emit = defineEmits<{
	action: []
}>()

// 计算属性
const containerClass = computed(() => {
	return `empty-${props.size}`
})

const iconName = computed(() => {
	if (props.iconName) return props.iconName
	
	const iconMap = {
		default: 'empty',
		search: 'search-empty',
		network: 'wifi-off',
		cart: 'cart-empty',
		order: 'order-empty',
		product: 'product-empty',
		custom: 'empty'
	}
	
	return iconMap[props.type] || 'empty'
})

const text = computed(() => {
	if (props.text) return props.text
	
	const textMap = {
		default: '暂无数据',
		search: '没有找到相关内容',
		network: '网络连接失败',
		cart: '购物车是空的',
		order: '暂无订单',
		product: '暂无商品',
		custom: '暂无内容'
	}
	
	return textMap[props.type] || '暂无数据'
})

const description = computed(() => {
	if (props.description) return props.description
	
	const descMap = {
		default: '',
		search: '试试其他关键词',
		network: '请检查网络连接后重试',
		cart: '快去挑选喜欢的商品吧',
		order: '您还没有任何订单',
		product: '暂时没有相关商品',
		custom: ''
	}
	
	return descMap[props.type] || ''
})

const actionText = computed(() => {
	if (props.actionText !== '重新加载') return props.actionText
	
	const actionMap = {
		default: '刷新',
		search: '重新搜索',
		network: '重新加载',
		cart: '去逛逛',
		order: '去下单',
		product: '看看其他',
		custom: '重试'
	}
	
	return actionMap[props.type] || '刷新'
})

// 方法
const handleAction = () => {
	emit('action')
}
</script>

<style lang="scss" scoped>
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px 20px;
	
	&.empty-small {
		padding: 20px 15px;
		
		.empty-icon {
			margin-bottom: 12px;
		}
		
		.empty-text {
			font-size: 13px;
			margin-bottom: 6px;
		}
		
		.empty-description {
			font-size: 11px;
			margin-bottom: 15px;
		}
	}
	
	&.empty-medium {
		padding: 40px 20px;
		
		.empty-icon {
			margin-bottom: 16px;
		}
		
		.empty-text {
			font-size: 16px;
			margin-bottom: 8px;
		}
		
		.empty-description {
			font-size: 13px;
			margin-bottom: 20px;
		}
	}
	
	&.empty-large {
		padding: 60px 30px;
		
		.empty-icon {
			margin-bottom: 20px;
		}
		
		.empty-text {
			font-size: 18px;
			margin-bottom: 10px;
		}
		
		.empty-description {
			font-size: 14px;
			margin-bottom: 25px;
		}
	}
	
	.empty-icon {
		margin-bottom: 16px;
	}
	
	.empty-text {
		font-size: 16px;
		color: #666666;
		font-weight: 500;
		margin-bottom: 8px;
		text-align: center;
	}
	
	.empty-description {
		font-size: 13px;
		color: #999999;
		margin-bottom: 20px;
		text-align: center;
		line-height: 1.4;
	}
	
	.empty-action {
		.action-button {
			padding: 8px 20px;
			background-color: #007AFF;
			border-radius: 20px;
			
			.button-text {
				font-size: 14px;
				color: #FFFFFF;
			}
		}
	}
}
</style>
