#!/usr/bin/env python
"""
开发管理脚本
用于简化Django项目的日常开发操作
"""

import os
import sys
import subprocess
import argparse

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def run_command(command, shell=False):
    """运行命令"""
    try:
        if shell:
            result = subprocess.run(command, shell=True, check=True, text=True, capture_output=True)
        else:
            result = subprocess.run(command.split(), check=True, text=True, capture_output=True)
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def setup_environment():
    """设置开发环境"""
    print("设置开发环境...")
    
    # 检查虚拟环境
    if not os.path.exists("venv"):
        print("创建虚拟环境...")
        if not run_command("python -m venv venv"):
            return False
    
    # 激活虚拟环境并安装依赖
    print("安装依赖包...")
    if os.name == 'nt':  # Windows
        if not run_command("venv\\Scripts\\pip install -r requirements.txt", shell=True):
            return False
    else:  # macOS/Linux
        if not run_command("source venv/bin/activate && pip install -r requirements.txt", shell=True):
            return False
    
    print("环境设置完成!")
    return True

def run_migrations():
    """运行数据库迁移"""
    print("运行数据库迁移...")
    return run_command("python manage.py migrate")

def create_superuser():
    """创建超级用户"""
    print("创建超级用户...")
    return run_command("python manage.py createsuperuser")

def run_server(port=8000):
    """运行开发服务器"""
    print(f"启动开发服务器 (端口: {port})...")
    return run_command(f"python manage.py runserver {port}")

def run_tests():
    """运行测试"""
    print("运行测试...")
    return run_command("python manage.py test")

def collect_static():
    """收集静态文件"""
    print("收集静态文件...")
    return run_command("python manage.py collectstatic --noinput")

def show_migrations():
    """显示迁移状态"""
    print("显示迁移状态...")
    return run_command("python manage.py showmigrations")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="智梦科技系统开发管理工具")
    parser.add_argument("command", choices=[
        "setup", "migrate", "superuser", "run", "test", 
        "collectstatic", "showmigrations"
    ], help="要执行的命令")
    parser.add_argument("--port", type=int, default=8000, help="开发服务器端口")
    
    args = parser.parse_args()
    
    # 切换到backend目录
    os.chdir(os.path.join(os.path.dirname(__file__), '..'))
    
    # 执行命令
    if args.command == "setup":
        setup_environment()
    elif args.command == "migrate":
        run_migrations()
    elif args.command == "superuser":
        create_superuser()
    elif args.command == "run":
        run_server(args.port)
    elif args.command == "test":
        run_tests()
    elif args.command == "collectstatic":
        collect_static()
    elif args.command == "showmigrations":
        show_migrations()

if __name__ == "__main__":
    main()
