<template>
	<view class="container">
		<!-- 顶部搜索栏 -->
		<view class="header">
			<view class="search-bar" @click="goToSearch">
				<Icon name="search" size="16" color="#999" />
				<text class="search-placeholder">搜索商品</text>
			</view>
			<view class="scan-btn" @click="handleScan">
				<Icon name="scan" size="20" color="#333" />
			</view>
		</view>

		<!-- 轮播图 -->
		<view class="banner-section">
			<swiper class="banner-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500">
				<swiper-item v-for="(banner, index) in banners" :key="index">
					<image class="banner-image" :src="banner.image" mode="aspectFill" @click="handleBannerClick(banner)" />
				</swiper-item>
			</swiper>
		</view>

		<!-- 功能导航 -->
		<view class="nav-section">
			<view class="nav-grid">
				<view class="nav-item" v-for="(nav, index) in navItems" :key="index" @click="handleNavClick(nav)">
					<view class="nav-icon">
						<Icon :name="nav.icon" size="24" :color="nav.color" />
					</view>
					<text class="nav-text">{{ nav.title }}</text>
				</view>
			</view>
		</view>

		<!-- 商品分类 -->
		<view class="category-section">
			<view class="section-header">
				<text class="section-title">商品分类</text>
				<view class="more-btn" @click="goToCategory">
					<text class="more-text">更多</text>
					<Icon name="arrow-right" size="12" color="#999" />
				</view>
			</view>
			<scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false">
				<view class="category-list">
					<view class="category-item" v-for="(category, index) in categories" :key="index" @click="handleCategoryClick(category)">
						<image class="category-image" :src="category.image" mode="aspectFill" />
						<text class="category-name">{{ category.name }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 推荐商品 -->
		<view class="product-section">
			<view class="section-header">
				<text class="section-title">推荐商品</text>
				<view class="more-btn" @click="goToProductList">
					<text class="more-text">更多</text>
					<Icon name="arrow-right" size="12" color="#999" />
				</view>
			</view>
			<view class="product-grid">
				<view class="product-item" v-for="(product, index) in recommendProducts" :key="index" @click="handleProductClick(product)">
					<image class="product-image" :src="product.image" mode="aspectFill" />
					<view class="product-info">
						<text class="product-name">{{ product.name }}</text>
						<view class="product-price">
							<text class="price-current">￥{{ product.price }}</text>
							<text class="price-original" v-if="product.originalPrice">￥{{ product.originalPrice }}</text>
						</view>
						<view class="product-tags">
							<text class="tag hot" v-if="product.isHot">热销</text>
							<text class="tag new" v-if="product.isNew">新品</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载更多 -->
		<view class="load-more" v-if="hasMore">
			<text class="load-text" v-if="!loading">上拉加载更多</text>
			<text class="load-text" v-else>加载中...</text>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Icon from '@/components/common/Icon.uvue'
import { CONFIG } from '@/utils/config'
import { http } from '@/utils/request'

// 响应式数据
const loading = ref(false)
const hasMore = ref(true)

// 轮播图数据
const banners = ref([])

// 导航菜单数据
const navItems = ref([
	{ icon: 'category', title: '分类', color: '#ff6b35', path: '/pages/products/category' },
	{ icon: 'hot', title: '热销', color: '#ff3b30', path: '/pages/products/list?is_hot=true' },
	{ icon: 'new', title: '新品', color: '#34c759', path: '/pages/products/list?is_new=true' },
	{ icon: 'gift', title: '优惠', color: '#ff9500', path: '/pages/coupons/list' },
	{ icon: 'team', title: '分销', color: '#007aff', path: '/pages/distribution/index' },
	{ icon: 'service', title: '客服', color: '#5856d6', path: '/pages/service/index' },
	{ icon: 'location', title: '门店', color: '#af52de', path: '/pages/stores/list' },
	{ icon: 'more', title: '更多', color: '#8e8e93', path: '/pages/more/index' },
	{ icon: 'settings', title: '测试', color: '#5856d6', path: '/pages/test/components' }
])

// 商品分类数据
const categories = ref([
	{ id: 1, name: '服装', image: `${CONFIG.CDN_BASE_URL}/categories/clothing.jpg` },
	{ id: 2, name: '数码', image: `${CONFIG.CDN_BASE_URL}/categories/digital.jpg` },
	{ id: 3, name: '家居', image: `${CONFIG.CDN_BASE_URL}/categories/home.jpg` },
	{ id: 4, name: '美妆', image: `${CONFIG.CDN_BASE_URL}/categories/beauty.jpg` },
	{ id: 5, name: '食品', image: `${CONFIG.CDN_BASE_URL}/categories/food.jpg` },
	{ id: 6, name: '运动', image: `${CONFIG.CDN_BASE_URL}/categories/sports.jpg` }
])

// 推荐商品数据
const recommendProducts = ref([
	{
		id: 1,
		name: '夏季清爽T恤',
		price: 89.00,
		originalPrice: 129.00,
		image: `${CONFIG.CDN_BASE_URL}/products/tshirt1.jpg`,
		isHot: true,
		isNew: false
	},
	{
		id: 2,
		name: '无线蓝牙耳机',
		price: 299.00,
		originalPrice: null,
		image: `${CONFIG.CDN_BASE_URL}/products/earphone1.jpg`,
		isHot: false,
		isNew: true
	},
	{
		id: 3,
		name: '简约家居摆件',
		price: 59.00,
		originalPrice: 89.00,
		image: `${CONFIG.CDN_BASE_URL}/products/decoration1.jpg`,
		isHot: true,
		isNew: false
	},
	{
		id: 4,
		name: '天然护肤套装',
		price: 199.00,
		originalPrice: 299.00,
		image: `${CONFIG.CDN_BASE_URL}/products/skincare1.jpg`,
		isHot: false,
		isNew: true
	}
])

// 生命周期
onMounted(() => {
	initPage()
})

// 获取轮播图数据
const loadBanners = async () => {
	try {
		const response = await http.get('/core/banners/', {
			position: 'home'
		})
		if (response.code === 200) {
			banners.value = response.data
		}
	} catch (error) {
		console.error('获取轮播图失败:', error)
		// 使用默认数据
		banners.value = [
			{
				id: 1,
				image: 'https://via.placeholder.com/750x300/FF6B6B/FFFFFF?text=夏季新品上市',
				title: '夏季新品上市',
				link_type: 'category',
				link_value: '1'
			},
			{
				id: 2,
				image: 'https://via.placeholder.com/750x300/4ECDC4/FFFFFF?text=限时特惠活动',
				title: '限时特惠活动',
				link_type: 'url',
				link_value: '/pages/products/list?is_hot=true'
			},
			{
				id: 3,
				image: 'https://via.placeholder.com/750x300/45B7D1/FFFFFF?text=分销招募中',
				title: '分销招募中',
				link_type: 'page',
				link_value: '/pages/distribution/index'
			}
		]
	}
}

// 方法
const initPage = async () => {
	console.log('初始化首页')
	await loadBanners()
}

const goToSearch = () => {
	uni.navigateTo({
		url: '/pages/products/search'
	})
}

const handleScan = async () => {
	uni.scanCode({
		success: (res) => {
			uni.showToast({
				title: `扫码结果: ${res.result}`,
				icon: 'none'
			})
		},
		fail: () => {
			uni.showToast({
				title: '扫码失败',
				icon: 'error'
			})
		}
	})
}

const handleBannerClick = (banner: any) => {
	console.log('点击轮播图:', banner)

	// 根据链接类型处理跳转
	switch (banner.link_type) {
		case 'product':
			uni.navigateTo({
				url: `/pages/products/detail?id=${banner.link_value}`
			})
			break
		case 'category':
			uni.navigateTo({
				url: `/pages/products/list?category=${banner.link_value}`
			})
			break
		case 'url':
			uni.navigateTo({
				url: banner.link_value
			})
			break
		case 'page':
			uni.navigateTo({
				url: banner.link_value
			})
			break
		case 'none':
		default:
			// 无链接，不做任何操作
			break
	}
}

const handleNavClick = (nav: any) => {
	uni.navigateTo({
		url: nav.path
	})
}

const goToCategory = () => {
	uni.navigateTo({
		url: '/pages/products/category'
	})
}

const handleCategoryClick = (category: any) => {
	uni.navigateTo({
		url: `/pages/products/list?category_id=${category.id}`
	})
}

const goToProductList = () => {
	uni.navigateTo({
		url: '/pages/products/list?is_recommended=true'
	})
}

const handleProductClick = (product: any) => {
	uni.navigateTo({
		url: `/pages/products/detail?id=${product.id}`
	})
}
</script>
<style lang="scss" scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

// 顶部搜索栏
.header {
	display: flex;
	align-items: center;
	padding: 10px 15px;
	background-color: #fff;
	border-bottom: 1px solid #eee;

	.search-bar {
		flex: 1;
		display: flex;
		align-items: center;
		height: 36px;
		padding: 0 12px;
		background-color: #f5f5f5;
		border-radius: 18px;
		margin-right: 10px;

		.search-placeholder {
			margin-left: 8px;
			font-size: 14px;
			color: #999;
		}
	}

	.scan-btn {
		width: 36px;
		height: 36px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

// 轮播图
.banner-section {
	margin: 10px 15px;

	.banner-swiper {
		height: 180px;
		border-radius: 8px;
		overflow: hidden;

		.banner-image {
			width: 100%;
			height: 100%;
		}
	}
}

// 功能导航
.nav-section {
	margin: 10px 15px;
	background-color: #fff;
	border-radius: 8px;
	padding: 20px 0;

	.nav-grid {
		display: flex;
		flex-wrap: wrap;

		.nav-item {
			width: 25%;
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-bottom: 20px;

			&:nth-child(n+5) {
				margin-bottom: 0;
			}

			.nav-icon {
				width: 44px;
				height: 44px;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: #f8f8f8;
				border-radius: 22px;
				margin-bottom: 8px;
			}

			.nav-text {
				font-size: 12px;
				color: #333;
			}
		}
	}
}

// 通用区块样式
.section-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 15px;
	margin-bottom: 15px;

	.section-title {
		font-size: 16px;
		font-weight: 600;
		color: #333;
	}

	.more-btn {
		display: flex;
		align-items: center;

		.more-text {
			font-size: 12px;
			color: #999;
			margin-right: 4px;
		}
	}
}

// 商品分类
.category-section {
	margin: 10px 0;
	background-color: #fff;
	padding: 15px 0;

	.category-scroll {
		white-space: nowrap;

		.category-list {
			display: flex;
			padding: 0 15px;

			.category-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				margin-right: 20px;
				min-width: 60px;

				&:last-child {
					margin-right: 15px;
				}

				.category-image {
					width: 50px;
					height: 50px;
					border-radius: 25px;
					margin-bottom: 8px;
				}

				.category-name {
					font-size: 12px;
					color: #333;
					text-align: center;
				}
			}
		}
	}
}

// 推荐商品
.product-section {
	margin: 10px 0;
	background-color: #fff;
	padding: 15px 0;

	.product-grid {
		display: flex;
		flex-wrap: wrap;
		padding: 0 15px;

		.product-item {
			width: calc(50% - 5px);
			margin-right: 10px;
			margin-bottom: 15px;
			background-color: #fff;
			border-radius: 8px;
			overflow: hidden;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

			&:nth-child(2n) {
				margin-right: 0;
			}

			.product-image {
				width: 100%;
				height: 140px;
			}

			.product-info {
				padding: 12px;

				.product-name {
					font-size: 14px;
					color: #333;
					line-height: 1.4;
					margin-bottom: 8px;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
				}

				.product-price {
					display: flex;
					align-items: center;
					margin-bottom: 8px;

					.price-current {
						font-size: 16px;
						font-weight: 600;
						color: #ff3b30;
						margin-right: 8px;
					}

					.price-original {
						font-size: 12px;
						color: #999;
						text-decoration: line-through;
					}
				}

				.product-tags {
					display: flex;

					.tag {
						font-size: 10px;
						padding: 2px 6px;
						border-radius: 2px;
						margin-right: 4px;

						&.hot {
							background-color: #ff3b30;
							color: #fff;
						}

						&.new {
							background-color: #34c759;
							color: #fff;
						}
					}
				}
			}
		}
	}
}

// 加载更多
.load-more {
	padding: 20px;
	text-align: center;

	.load-text {
		font-size: 14px;
		color: #999;
	}
}
</style>
