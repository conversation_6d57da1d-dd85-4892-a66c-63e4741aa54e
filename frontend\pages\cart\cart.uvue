<template>
	<view class="container">
		<!-- 购物车列表 -->
		<view class="cart-list" v-if="cartItems.length > 0">
			<view class="cart-item" v-for="(item, index) in cartItems" :key="index">
				<view class="item-checkbox" @click="toggleSelect(index)">
					<Icon :name="item.selected ? 'checkbox-checked' : 'checkbox'" size="20" :color="item.selected ? '#007aff' : '#ccc'" />
				</view>
				<image class="item-image" :src="item.product.image" mode="aspectFill" />
				<view class="item-info">
					<text class="item-name">{{ item.product.name }}</text>
					<text class="item-spec" v-if="item.spec">{{ item.spec }}</text>
					<view class="item-price">
						<text class="price-text">￥{{ item.product.price }}</text>
					</view>
				</view>
				<view class="item-actions">
					<view class="quantity-control">
						<view class="quantity-btn" @click="decreaseQuantity(index)">
							<Icon name="minus" size="14" color="#999" />
						</view>
						<text class="quantity-text">{{ item.quantity }}</text>
						<view class="quantity-btn" @click="increaseQuantity(index)">
							<Icon name="plus" size="14" color="#999" />
						</view>
					</view>
					<view class="delete-btn" @click="removeItem(index)">
						<Icon name="delete" size="16" color="#ff3b30" />
					</view>
				</view>
			</view>
		</view>

		<!-- 空购物车 -->
		<view class="empty-cart" v-else>
			<Icon name="cart-empty" size="80" color="#ccc" />
			<text class="empty-text">购物车是空的</text>
			<view class="go-shopping-btn" @click="goShopping">
				<text class="btn-text">去逛逛</text>
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-bar" v-if="cartItems.length > 0">
			<view class="select-all" @click="toggleSelectAll">
				<Icon :name="isAllSelected ? 'checkbox-checked' : 'checkbox'" size="20" :color="isAllSelected ? '#007aff' : '#ccc'" />
				<text class="select-text">全选</text>
			</view>
			<view class="total-info">
				<text class="total-text">合计：</text>
				<text class="total-price">￥{{ totalAmount.toFixed(2) }}</text>
			</view>
			<view class="checkout-btn" :class="{ disabled: selectedCount === 0 }" @click="handleCheckout">
				<text class="btn-text">结算({{ selectedCount }})</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import Icon from '@/components/common/Icon.uvue'

// 模拟购物车数据
const cartItems = ref([
	{
		id: 1,
		product: {
			id: 1,
			name: '夏季清爽T恤 纯棉透气',
			price: 89.00,
			image: 'https://imggw.zmkj.live/products/tshirt1.jpg'
		},
		spec: '白色 L码',
		quantity: 2,
		selected: true
	},
	{
		id: 2,
		product: {
			id: 2,
			name: '无线蓝牙耳机 降噪立体声',
			price: 299.00,
			image: 'https://imggw.zmkj.live/products/earphone1.jpg'
		},
		spec: '黑色',
		quantity: 1,
		selected: true
	},
	{
		id: 3,
		product: {
			id: 3,
			name: '简约家居摆件 北欧风格',
			price: 59.00,
			image: 'https://imggw.zmkj.live/products/decoration1.jpg'
		},
		spec: '白色',
		quantity: 1,
		selected: false
	}
])

// 计算属性
const isAllSelected = computed(() => {
	return cartItems.value.length > 0 && cartItems.value.every(item => item.selected)
})

const selectedCount = computed(() => {
	return cartItems.value.filter(item => item.selected).length
})

const totalAmount = computed(() => {
	return cartItems.value
		.filter(item => item.selected)
		.reduce((total, item) => total + (item.product.price * item.quantity), 0)
})

// 生命周期
onMounted(() => {
	initCart()
})

// 方法
const initCart = async () => {
	console.log('初始化购物车')
	// 这里可以添加数据初始化逻辑
}

const toggleSelect = (index: number) => {
	cartItems.value[index].selected = !cartItems.value[index].selected
}

const toggleSelectAll = () => {
	const newSelectState = !isAllSelected.value
	cartItems.value.forEach(item => {
		item.selected = newSelectState
	})
}

const decreaseQuantity = (index: number) => {
	if (cartItems.value[index].quantity > 1) {
		cartItems.value[index].quantity--
	}
}

const increaseQuantity = (index: number) => {
	cartItems.value[index].quantity++
}

const removeItem = (index: number) => {
	uni.showModal({
		title: '提示',
		content: '确定要删除这个商品吗？',
		success: (res) => {
			if (res.confirm) {
				cartItems.value.splice(index, 1)
				uni.showToast({
					title: '删除成功',
					icon: 'success'
				})
			}
		}
	})
}

const goShopping = () => {
	uni.switchTab({
		url: '/pages/index/index'
	})
}

const handleCheckout = () => {
	if (selectedCount.value === 0) {
		uni.showToast({
			title: '请选择要结算的商品',
			icon: 'none'
		})
		return
	}
	
	// 跳转到结算页面
	uni.navigateTo({
		url: '/pages/order/checkout'
	})
}
</script>

<style lang="scss" scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 60px;
}

// 购物车列表
.cart-list {
	padding: 10px 15px;
	
	.cart-item {
		display: flex;
		align-items: center;
		background-color: #fff;
		border-radius: 8px;
		padding: 15px;
		margin-bottom: 10px;
		
		.item-checkbox {
			margin-right: 12px;
		}
		
		.item-image {
			width: 80px;
			height: 80px;
			border-radius: 6px;
			margin-right: 12px;
		}
		
		.item-info {
			flex: 1;
			
			.item-name {
				font-size: 14px;
				color: #333;
				line-height: 1.4;
				margin-bottom: 4px;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
			}
			
			.item-spec {
				font-size: 12px;
				color: #999;
				margin-bottom: 8px;
			}
			
			.item-price {
				.price-text {
					font-size: 16px;
					font-weight: 600;
					color: #ff3b30;
				}
			}
		}
		
		.item-actions {
			display: flex;
			flex-direction: column;
			align-items: flex-end;
			
			.quantity-control {
				display: flex;
				align-items: center;
				margin-bottom: 12px;
				
				.quantity-btn {
					width: 28px;
					height: 28px;
					display: flex;
					align-items: center;
					justify-content: center;
					border: 1px solid #ddd;
					border-radius: 4px;
					
					&:first-child {
						border-top-right-radius: 0;
						border-bottom-right-radius: 0;
					}
					
					&:last-child {
						border-top-left-radius: 0;
						border-bottom-left-radius: 0;
						border-left: none;
					}
				}
				
				.quantity-text {
					width: 40px;
					height: 28px;
					line-height: 28px;
					text-align: center;
					font-size: 14px;
					border-top: 1px solid #ddd;
					border-bottom: 1px solid #ddd;
					background-color: #f8f8f8;
				}
			}
			
			.delete-btn {
				padding: 4px;
			}
		}
	}
}

// 空购物车
.empty-cart {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80px 20px;
	
	.empty-text {
		font-size: 16px;
		color: #999;
		margin: 20px 0 30px;
	}
	
	.go-shopping-btn {
		width: 120px;
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #007aff;
		border-radius: 20px;
		
		.btn-text {
			font-size: 14px;
			color: #fff;
		}
	}
}

// 底部操作栏
.bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 60px;
	display: flex;
	align-items: center;
	background-color: #fff;
	border-top: 1px solid #eee;
	padding: 0 15px;
	
	.select-all {
		display: flex;
		align-items: center;
		margin-right: 20px;
		
		.select-text {
			font-size: 14px;
			color: #333;
			margin-left: 8px;
		}
	}
	
	.total-info {
		flex: 1;
		text-align: right;
		margin-right: 15px;
		
		.total-text {
			font-size: 14px;
			color: #333;
		}
		
		.total-price {
			font-size: 18px;
			font-weight: 600;
			color: #ff3b30;
		}
	}
	
	.checkout-btn {
		width: 100px;
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #007aff;
		border-radius: 20px;
		
		&.disabled {
			background-color: #ccc;
		}
		
		.btn-text {
			font-size: 14px;
			color: #fff;
		}
	}
}
</style>
