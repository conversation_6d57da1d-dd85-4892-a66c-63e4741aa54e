{% extends "admin/base.html" %}
{% load i18n static %}

{% block extrahead %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static 'admin/css/responsive.css' %}">
<link rel="stylesheet" type="text/css" href="{% static 'admin/css/themes.css' %}">
<link rel="stylesheet" type="text/css" href="{% static 'admin/css/custom_admin.css' %}">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
{% endblock %}

{% block bodyclass %}{{ block.super }} theme-{{ request.session.admin_theme|default:'default' }}{% endblock %}

{% block usertools %}
{{ block.super }}
<div id="theme-selector" style="float: right; margin-right: 20px;">
    <form method="get" action="{% url 'admin:switch_theme' theme='default' %}">
        <label for="theme-select">{% trans '主题' %}:</label>
        <select id="theme-select" onchange="location.href=this.value">
            {% for theme_key, theme_name in available_themes.items %}
            <option value="{% url 'admin:switch_theme' theme=theme_key %}" {% if request.session.admin_theme == theme_key %}selected{% endif %}>
                {{ theme_name }}
            </option>
            {% endfor %}
        </select>
    </form>
</div>
{% endblock %}