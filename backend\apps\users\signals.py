from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.conf import settings

User = get_user_model()

@receiver(post_save, sender=User)
def handle_user_creation(sender, instance, created, **kwargs):
    """用户创建或更新时的信号处理函数"""
    if created:
        # 用户创建时发送欢迎邮件
        if instance.email:
            send_mail(
                '欢迎加入智梦科技系统',
                f'尊敬的{instance.username}，欢迎注册智梦科技系统！\n\n您的账号已成功创建，祝您使用愉快。',
                settings.DEFAULT_FROM_EMAIL,
                [instance.email],
                fail_silently=True,
            )
        # 可以添加其他初始化逻辑，如创建默认用户资料等

    # 用户更新时的处理逻辑
    else:
        # 例如：当用户验证状态变更时发送通知
        if instance.is_verified and not kwargs.get('raw', False):
            if instance.email:
                send_mail(
                    '您的智梦科技系统账号已验证',
                    f'尊敬的{instance.username}，您的账号已成功验证。\n\n现在您可以使用系统的全部功能了。',
                    settings.DEFAULT_FROM_EMAIL,
                    [instance.email],
                    fail_silently=True,
                )