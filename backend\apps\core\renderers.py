from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.utils.serializer_helpers import ReturnDict, ReturnList

class StandardResponseRenderer(JSONRenderer):
    """
    统一API响应格式渲染器
    输出格式: {code: int, message: str, data: any}
    """
    def render(self, data, accepted_media_type=None, renderer_context=None):
        renderer_context = renderer_context or {}
        response = renderer_context.get('response', None)
        status_code = response.status_code if response else 200

        # 默认成功响应
        result = {
            'code': 0,
            'message': 'success',
            'data': data
        }

        # 处理错误响应
        if status_code >= 400:
            # 处理DRF默认错误格式
            if isinstance(data, dict) and ('detail' in data or 'non_field_errors' in data):
                message = data.get('detail') or data.get('non_field_errors')[0] if data.get('non_field_errors') else '请求错误'
                result['code'] = status_code
                result['message'] = message
                result['data'] = data
            else:
                result['code'] = status_code
                result['message'] = str(data) if isinstance(data, str) else '请求错误'
                result['data'] = None

        # 处理分页数据
        if isinstance(data, dict) and ('count' in data and 'results' in data):
            result['data'] = {
                'items': data['results'],
                'pagination': {
                    'total': data['count'],
                    'page': renderer_context.get('request').query_params.get('page', 1),
                    'page_size': data.get('page_size', 20),
                    'pages': (data['count'] + data.get('page_size', 20) - 1) // data.get('page_size', 20)
                }
            }

        return super().render(result, accepted_media_type, renderer_context)