@import "../../uvue.wxss";
:host{display:flex;flex-direction:column}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 图标相关变量 */
/* 图标颜色预设 */
/* 全局图标基础样式 */
.icon-base.data-v-00a60067 {
  font-family: "iconfont";
  font-style: normal;
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
}

/* 图标动画 */
@keyframes icon-rotate-00a60067 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
.icon-spin.data-v-00a60067 {
  animation: icon-rotate-00a60067 1s linear infinite;
}
.container.data-v-00a60067 {
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-00a60067 {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}
.header .search-bar.data-v-00a60067 {
  flex: 1;
  display: flex;
  align-items: center;
  height: 36px;
  padding: 0 12px;
  background-color: #f5f5f5;
  border-radius: 18px;
  margin-right: 10px;
}
.header .search-bar .search-placeholder.data-v-00a60067 {
  margin-left: 8px;
  font-size: 14px;
  color: #999;
}
.header .scan-btn.data-v-00a60067 {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.banner-section.data-v-00a60067 {
  margin: 10px 15px;
}
.banner-section .banner-swiper.data-v-00a60067 {
  height: 180px;
  border-radius: 8px;
  overflow: hidden;
}
.banner-section .banner-swiper .banner-image.data-v-00a60067 {
  width: 100%;
  height: 100%;
}
.nav-section.data-v-00a60067 {
  margin: 10px 15px;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px 0;
}
.nav-section .nav-grid.data-v-00a60067 {
  display: flex;
  flex-wrap: wrap;
}
.nav-section .nav-grid .nav-item.data-v-00a60067 {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}
.nav-section .nav-grid .nav-item.data-v-00a60067:nth-child(n+5) {
  margin-bottom: 0;
}
.nav-section .nav-grid .nav-item .nav-icon.data-v-00a60067 {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  border-radius: 22px;
  margin-bottom: 8px;
}
.nav-section .nav-grid .nav-item .nav-text.data-v-00a60067 {
  font-size: 12px;
  color: #333;
}
.section-header.data-v-00a60067 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  margin-bottom: 15px;
}
.section-header .section-title.data-v-00a60067 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.section-header .more-btn.data-v-00a60067 {
  display: flex;
  align-items: center;
}
.section-header .more-btn .more-text.data-v-00a60067 {
  font-size: 12px;
  color: #999;
  margin-right: 4px;
}
.category-section.data-v-00a60067 {
  margin: 10px 0;
  background-color: #fff;
  padding: 15px 0;
}
.category-section .category-scroll.data-v-00a60067 {
  white-space: nowrap;
}
.category-section .category-scroll .category-list.data-v-00a60067 {
  display: flex;
  padding: 0 15px;
}
.category-section .category-scroll .category-list .category-item.data-v-00a60067 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20px;
  min-width: 60px;
}
.category-section .category-scroll .category-list .category-item.data-v-00a60067:last-child {
  margin-right: 15px;
}
.category-section .category-scroll .category-list .category-item .category-image.data-v-00a60067 {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  margin-bottom: 8px;
}
.category-section .category-scroll .category-list .category-item .category-name.data-v-00a60067 {
  font-size: 12px;
  color: #333;
  text-align: center;
}
.product-section.data-v-00a60067 {
  margin: 10px 0;
  background-color: #fff;
  padding: 15px 0;
}
.product-section .product-grid.data-v-00a60067 {
  display: flex;
  flex-wrap: wrap;
  padding: 0 15px;
}
.product-section .product-grid .product-item.data-v-00a60067 {
  width: calc(50% - 5px);
  margin-right: 10px;
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.product-section .product-grid .product-item.data-v-00a60067:nth-child(2n) {
  margin-right: 0;
}
.product-section .product-grid .product-item .product-image.data-v-00a60067 {
  width: 100%;
  height: 140px;
}
.product-section .product-grid .product-item .product-info.data-v-00a60067 {
  padding: 12px;
}
.product-section .product-grid .product-item .product-info .product-name.data-v-00a60067 {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.product-section .product-grid .product-item .product-info .product-price.data-v-00a60067 {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.product-section .product-grid .product-item .product-info .product-price .price-current.data-v-00a60067 {
  font-size: 16px;
  font-weight: 600;
  color: #ff3b30;
  margin-right: 8px;
}
.product-section .product-grid .product-item .product-info .product-price .price-original.data-v-00a60067 {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}
.product-section .product-grid .product-item .product-info .product-tags.data-v-00a60067 {
  display: flex;
}
.product-section .product-grid .product-item .product-info .product-tags .tag.data-v-00a60067 {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 2px;
  margin-right: 4px;
}
.product-section .product-grid .product-item .product-info .product-tags .tag.hot.data-v-00a60067 {
  background-color: #ff3b30;
  color: #fff;
}
.product-section .product-grid .product-item .product-info .product-tags .tag.new.data-v-00a60067 {
  background-color: #34c759;
  color: #fff;
}
.load-more.data-v-00a60067 {
  padding: 20px;
  text-align: center;
}
.load-more .load-text.data-v-00a60067 {
  font-size: 14px;
  color: #999;
}