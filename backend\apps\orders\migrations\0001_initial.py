# Generated by Django 5.2.4 on 2025-07-25 09:25

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('order_no', models.CharField(help_text='系统生成的唯一订单号', max_length=32, unique=True, verbose_name='订单号')),
                ('total_amount', models.DecimalField(decimal_places=2, help_text='订单商品总金额', max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='订单总金额')),
                ('shipping_fee', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='配送费用', max_digits=8, validators=[django.core.validators.MinValueValidator(0)], verbose_name='运费')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='优惠券、折扣等减免金额', max_digits=8, validators=[django.core.validators.MinValueValidator(0)], verbose_name='优惠金额')),
                ('final_amount', models.DecimalField(decimal_places=2, help_text='用户实际支付金额', max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='实付金额')),
                ('status', models.CharField(choices=[('pending', '待支付'), ('paid', '已支付'), ('processing', '处理中'), ('shipped', '已发货'), ('delivered', '已送达'), ('completed', '已完成'), ('cancelled', '已取消'), ('refunding', '退款中'), ('refunded', '已退款')], default='pending', help_text='当前订单状态', max_length=20, verbose_name='订单状态')),
                ('payment_method', models.CharField(blank=True, choices=[('wechat', '微信支付'), ('alipay', '支付宝'), ('balance', '余额支付'), ('offline', '线下支付')], help_text='用户选择的支付方式', max_length=20, verbose_name='支付方式')),
                ('recipient_name', models.CharField(help_text='收件人姓名', max_length=50, verbose_name='收货人')),
                ('recipient_phone', models.CharField(help_text='收件人电话', max_length=20, verbose_name='收货电话')),
                ('shipping_address', models.CharField(help_text='详细收货地址', max_length=500, verbose_name='收货地址')),
                ('paid_at', models.DateTimeField(blank=True, help_text='订单支付时间', null=True, verbose_name='支付时间')),
                ('shipped_at', models.DateTimeField(blank=True, help_text='订单发货时间', null=True, verbose_name='发货时间')),
                ('delivered_at', models.DateTimeField(blank=True, help_text='订单送达时间', null=True, verbose_name='送达时间')),
                ('completed_at', models.DateTimeField(blank=True, help_text='订单完成时间', null=True, verbose_name='完成时间')),
                ('remark', models.TextField(blank=True, help_text='用户留言或特殊要求', verbose_name='订单备注')),
                ('admin_remark', models.TextField(blank=True, help_text='内部处理备注', verbose_name='管理员备注')),
                ('tracking_number', models.CharField(blank=True, help_text='物流快递追踪号', max_length=50, verbose_name='快递单号')),
                ('user', models.ForeignKey(help_text='订单所属用户', on_delete=django.db.models.deletion.PROTECT, related_name='orders', to=settings.AUTH_USER_MODEL, verbose_name='下单用户')),
            ],
            options={
                'verbose_name': '订单',
                'verbose_name_plural': '订单',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('product_name', models.CharField(help_text='下单时的商品名称快照', max_length=200, verbose_name='商品名称')),
                ('product_sku', models.CharField(help_text='下单时的商品SKU快照', max_length=100, verbose_name='商品编码')),
                ('product_price', models.DecimalField(decimal_places=2, help_text='下单时的商品价格快照', max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='商品单价')),
                ('quantity', models.PositiveIntegerField(help_text='用户购买的商品数量', validators=[django.core.validators.MinValueValidator(1)], verbose_name='购买数量')),
                ('subtotal', models.DecimalField(decimal_places=2, help_text='单价 × 数量的小计金额', max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='小计金额')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='orders.order', verbose_name='所属订单')),
                ('product', models.ForeignKey(help_text='订单中的商品', on_delete=django.db.models.deletion.PROTECT, to='products.product', verbose_name='商品')),
            ],
            options={
                'verbose_name': '订单项',
                'verbose_name_plural': '订单项',
            },
        ),
        migrations.CreateModel(
            name='OrderLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('action_type', models.CharField(choices=[('create', '创建订单'), ('pay', '支付订单'), ('cancel', '取消订单'), ('ship', '发货'), ('deliver', '送达'), ('complete', '完成'), ('refund', '退款'), ('remark', '备注'), ('status_change', '状态变更')], help_text='具体的操作类型', max_length=20, verbose_name='操作类型')),
                ('old_status', models.CharField(blank=True, help_text='操作前的订单状态', max_length=20, verbose_name='原状态')),
                ('new_status', models.CharField(blank=True, help_text='操作后的订单状态', max_length=20, verbose_name='新状态')),
                ('content', models.TextField(help_text='详细的操作描述', verbose_name='操作内容')),
                ('extra_data', models.JSONField(blank=True, default=dict, help_text='操作相关的额外数据', verbose_name='额外数据')),
                ('action_user', models.ForeignKey(blank=True, help_text='执行操作的用户，为空表示系统操作', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='操作人')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='orders.order', verbose_name='关联订单')),
            ],
            options={
                'verbose_name': '订单日志',
                'verbose_name_plural': '订单日志',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('payment_no', models.CharField(help_text='系统生成的支付单号', max_length=32, unique=True, verbose_name='支付单号')),
                ('third_party_no', models.CharField(blank=True, help_text='微信、支付宝等第三方平台交易号', max_length=64, verbose_name='第三方交易号')),
                ('payment_method', models.CharField(choices=[('wechat', '微信支付'), ('alipay', '支付宝'), ('balance', '余额支付'), ('offline', '线下支付')], help_text='支付渠道', max_length=20, verbose_name='支付方式')),
                ('payment_type', models.CharField(choices=[('payment', '支付'), ('refund', '退款')], default='payment', help_text='支付或退款', max_length=20, verbose_name='支付类型')),
                ('amount', models.DecimalField(decimal_places=2, help_text='实际支付或退款金额', max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='支付金额')),
                ('status', models.CharField(choices=[('pending', '待支付'), ('processing', '支付中'), ('success', '支付成功'), ('failed', '支付失败'), ('cancelled', '已取消'), ('refunded', '已退款')], default='pending', help_text='当前支付状态', max_length=20, verbose_name='支付状态')),
                ('paid_at', models.DateTimeField(blank=True, help_text='实际支付完成时间', null=True, verbose_name='支付时间')),
                ('callback_data', models.JSONField(blank=True, default=dict, help_text='第三方支付平台回调的原始数据', verbose_name='回调数据')),
                ('remark', models.TextField(blank=True, help_text='支付相关备注信息', verbose_name='支付备注')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='orders.order', verbose_name='关联订单')),
            ],
            options={
                'verbose_name': '支付记录',
                'verbose_name_plural': '支付记录',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['user', 'status'], name='orders_orde_user_id_02a211_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['order_no'], name='orders_orde_order_n_7a9a09_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['status'], name='orders_orde_status_c6dd84_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['created_at'], name='orders_orde_created_0e92de_idx'),
        ),
        migrations.AddIndex(
            model_name='orderitem',
            index=models.Index(fields=['order'], name='orders_orde_order_i_5d347b_idx'),
        ),
        migrations.AddIndex(
            model_name='orderitem',
            index=models.Index(fields=['product'], name='orders_orde_product_32ff41_idx'),
        ),
        migrations.AddIndex(
            model_name='orderlog',
            index=models.Index(fields=['order', '-created_at'], name='orders_orde_order_i_9c2722_idx'),
        ),
        migrations.AddIndex(
            model_name='orderlog',
            index=models.Index(fields=['action_type'], name='orders_orde_action__d9d80d_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['order'], name='orders_paym_order_i_8c8d98_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['payment_no'], name='orders_paym_payment_6e7653_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['third_party_no'], name='orders_paym_third_p_80b252_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['status'], name='orders_paym_status_83f434_idx'),
        ),
    ]
