from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.db import transaction
from apps.distribution.models import DistributionRelation, DistributionLevel
from apps.distribution.services import DistributionRelationService
from apps.distribution.utils import check_level_upgrade

User = get_user_model()


class Command(BaseCommand):
    help = '分销关系管理命令'

    def add_arguments(self, parser):
        parser.add_argument(
            '--action',
            type=str,
            choices=['check_upgrades', 'fix_referral_counts', 'validate_relations', 'stats'],
            required=True,
            help='要执行的操作'
        )
        parser.add_argument(
            '--user-id',
            type=int,
            help='指定用户ID（某些操作需要）'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅显示将要执行的操作，不实际执行'
        )

    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'check_upgrades':
            self.check_level_upgrades(options.get('dry_run', False))
        elif action == 'fix_referral_counts':
            self.fix_referral_counts(options.get('dry_run', False))
        elif action == 'validate_relations':
            self.validate_relations()
        elif action == 'stats':
            self.show_statistics()

    def check_level_upgrades(self, dry_run=False):
        """检查并处理等级升级"""
        self.stdout.write(self.style.SUCCESS('开始检查分销商等级升级...'))
        
        relations = DistributionRelation.objects.filter(is_active=True)
        upgraded_count = 0
        
        for relation in relations:
            old_level = relation.level
            result = check_level_upgrade(relation)
            
            if result['upgraded']:
                upgraded_count += 1
                if dry_run:
                    self.stdout.write(
                        f'[DRY RUN] 用户 {relation.user.username} 可升级: '
                        f'{old_level.name} -> {result["new_level"].name}'
                    )
                else:
                    self.stdout.write(
                        f'用户 {relation.user.username} 已升级: '
                        f'{old_level.name} -> {result["new_level"].name}'
                    )
        
        if dry_run:
            self.stdout.write(f'[DRY RUN] 共有 {upgraded_count} 个分销商可以升级')
        else:
            self.stdout.write(f'成功升级 {upgraded_count} 个分销商')

    def fix_referral_counts(self, dry_run=False):
        """修复推荐数量统计"""
        self.stdout.write(self.style.SUCCESS('开始修复推荐数量统计...'))
        
        relations = DistributionRelation.objects.filter(is_active=True)
        fixed_count = 0
        
        with transaction.atomic():
            for relation in relations:
                # 计算实际的推荐数量
                actual_count = relation.children.filter(is_active=True).count()
                
                if relation.referral_count != actual_count:
                    fixed_count += 1
                    if dry_run:
                        self.stdout.write(
                            f'[DRY RUN] 用户 {relation.user.username} 推荐数量不匹配: '
                            f'记录值={relation.referral_count}, 实际值={actual_count}'
                        )
                    else:
                        relation.referral_count = actual_count
                        relation.save()
                        self.stdout.write(
                            f'修复用户 {relation.user.username} 推荐数量: '
                            f'{relation.referral_count} -> {actual_count}'
                        )
        
        if dry_run:
            self.stdout.write(f'[DRY RUN] 共有 {fixed_count} 个分销商的推荐数量需要修复')
        else:
            self.stdout.write(f'成功修复 {fixed_count} 个分销商的推荐数量')

    def validate_relations(self):
        """验证分销关系的完整性"""
        self.stdout.write(self.style.SUCCESS('开始验证分销关系完整性...'))
        
        issues = []
        
        # 检查循环引用
        relations = DistributionRelation.objects.filter(is_active=True)
        for relation in relations:
            if self._has_circular_reference(relation):
                issues.append(f'循环引用: 用户 {relation.user.username} (ID: {relation.id})')
        
        # 检查孤儿节点（parent不存在但parent_id不为空）
        orphan_relations = DistributionRelation.objects.filter(
            parent__isnull=False,
            parent__is_active=False
        )
        for relation in orphan_relations:
            issues.append(f'孤儿节点: 用户 {relation.user.username} 的上级已停用')
        
        # 检查等级不匹配
        for relation in relations:
            if not self._check_level_requirements(relation):
                issues.append(
                    f'等级不匹配: 用户 {relation.user.username} 不满足当前等级 {relation.level.name} 的要求'
                )
        
        if issues:
            self.stdout.write(self.style.ERROR(f'发现 {len(issues)} 个问题:'))
            for issue in issues:
                self.stdout.write(f'  - {issue}')
        else:
            self.stdout.write(self.style.SUCCESS('分销关系完整性验证通过'))

    def show_statistics(self):
        """显示分销系统统计信息"""
        self.stdout.write(self.style.SUCCESS('分销系统统计信息:'))
        
        # 总体统计
        total_distributors = DistributionRelation.objects.filter(is_active=True).count()
        total_inactive = DistributionRelation.objects.filter(is_active=False).count()
        
        self.stdout.write(f'活跃分销商: {total_distributors}')
        self.stdout.write(f'停用分销商: {total_inactive}')
        
        # 按等级统计
        self.stdout.write('\n按等级统计:')
        levels = DistributionLevel.objects.filter(is_active=True).order_by('level')
        for level in levels:
            count = DistributionRelation.objects.filter(
                level=level,
                is_active=True
            ).count()
            self.stdout.write(f'  {level.name}: {count} 人')
        
        # 层级深度统计
        self.stdout.write('\n层级深度统计:')
        depth_stats = {}
        root_relations = DistributionRelation.objects.filter(
            parent__isnull=True,
            is_active=True
        )
        
        for root in root_relations:
            max_depth = self._calculate_max_depth(root)
            depth_stats[max_depth] = depth_stats.get(max_depth, 0) + 1
        
        for depth, count in sorted(depth_stats.items()):
            self.stdout.write(f'  最大深度 {depth}: {count} 个分销树')

    def _has_circular_reference(self, relation, visited=None, max_depth=20):
        """检查是否存在循环引用"""
        if visited is None:
            visited = set()
        
        if relation.id in visited or len(visited) > max_depth:
            return True
        
        if not relation.parent:
            return False
        
        visited.add(relation.id)
        return self._has_circular_reference(relation.parent, visited, max_depth)

    def _check_level_requirements(self, relation):
        """检查分销商是否满足当前等级要求"""
        level = relation.level
        return (
            relation.total_sales >= level.min_sales and
            relation.referral_count >= level.min_referrals
        )

    def _calculate_max_depth(self, relation, current_depth=0, max_depth=20):
        """计算分销树的最大深度"""
        if current_depth > max_depth:
            return current_depth
        
        children = relation.children.filter(is_active=True)
        if not children.exists():
            return current_depth
        
        max_child_depth = current_depth
        for child in children:
            child_depth = self._calculate_max_depth(child, current_depth + 1, max_depth)
            max_child_depth = max(max_child_depth, child_depth)
        
        return max_child_depth
