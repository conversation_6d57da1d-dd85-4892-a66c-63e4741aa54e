{% extends "admin/base_site.html" %}
{% load i18n custom_filters %}

{% block content %}
<div style="padding: 20px;">
    <h2>{% trans '主题已切换' %}</h2>
    <p>{% trans '当前主题' %}: {{ available_themes|get_item:theme }}</p>
    <p><a href="{% url 'admin:index' %}">{% trans '返回管理首页' %}</a></p>
    
    <h3>{% trans '可用主题' %}:</h3>
    <ul>
        {% for theme_key, theme_name in available_themes.items %}
        <li><a href="{% url 'admin:switch_theme' theme=theme_key %}">{{ theme_name }}</a></li>
        {% endfor %}
    </ul>
</div>
{% endblock %}