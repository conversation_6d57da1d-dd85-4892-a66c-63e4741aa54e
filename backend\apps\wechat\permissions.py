"""
微信模块权限控制
"""
from rest_framework import permissions
from django.contrib.auth.models import Group


class WechatAdminPermission(permissions.BasePermission):
    """微信管理员权限"""
    
    def has_permission(self, request, view):
        """检查用户是否有微信管理权限"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 超级管理员拥有所有权限
        if request.user.is_superuser:
            return True
        
        # 检查用户是否在微信管理员组
        try:
            wechat_admin_group = Group.objects.get(name='微信管理员')
            return wechat_admin_group in request.user.groups.all()
        except Group.DoesNotExist:
            return False


class WechatConfigPermission(permissions.BasePermission):
    """微信配置权限"""
    
    def has_permission(self, request, view):
        """检查用户是否有微信配置权限"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 超级管理员拥有所有权限
        if request.user.is_superuser:
            return True
        
        # 只读操作允许所有认证用户
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 写操作需要管理员权限
        try:
            wechat_admin_group = Group.objects.get(name='微信管理员')
            return wechat_admin_group in request.user.groups.all()
        except Group.DoesNotExist:
            return False


class WechatPaymentPermission(permissions.BasePermission):
    """微信支付权限"""
    
    def has_permission(self, request, view):
        """检查用户是否有微信支付权限"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 超级管理员拥有所有权限
        if request.user.is_superuser:
            return True
        
        # 检查用户是否在支付管理员组
        try:
            payment_admin_group = Group.objects.get(name='支付管理员')
            return payment_admin_group in request.user.groups.all()
        except Group.DoesNotExist:
            return False


class WechatStatsPermission(permissions.BasePermission):
    """微信统计权限"""
    
    def has_permission(self, request, view):
        """检查用户是否有微信统计权限"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 超级管理员拥有所有权限
        if request.user.is_superuser:
            return True
        
        # 检查用户是否在统计查看组
        try:
            stats_viewer_group = Group.objects.get(name='统计查看员')
            return stats_viewer_group in request.user.groups.all()
        except Group.DoesNotExist:
            # 如果没有统计查看员组，允许所有认证用户查看
            return True


class WechatUserPermission(permissions.BasePermission):
    """微信用户权限"""
    
    def has_permission(self, request, view):
        """检查用户是否有微信用户管理权限"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 超级管理员拥有所有权限
        if request.user.is_superuser:
            return True
        
        # 只读操作允许所有认证用户
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 写操作需要管理员权限
        try:
            wechat_admin_group = Group.objects.get(name='微信管理员')
            return wechat_admin_group in request.user.groups.all()
        except Group.DoesNotExist:
            return False


class WechatMessagePermission(permissions.BasePermission):
    """微信消息权限"""
    
    def has_permission(self, request, view):
        """检查用户是否有微信消息管理权限"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 超级管理员拥有所有权限
        if request.user.is_superuser:
            return True
        
        # 只读操作允许所有认证用户
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 写操作需要管理员权限
        try:
            wechat_admin_group = Group.objects.get(name='微信管理员')
            return wechat_admin_group in request.user.groups.all()
        except Group.DoesNotExist:
            return False


class WechatCallbackPermission(permissions.BasePermission):
    """微信回调权限"""
    
    def has_permission(self, request, view):
        """微信回调不需要用户认证，但需要验证签名"""
        # 微信回调接口不需要用户认证
        # 签名验证在视图中进行
        return True


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    自定义权限，只允许对象的所有者编辑它
    """
    
    def has_object_permission(self, request, view, obj):
        # 读权限允许任何请求
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 写权限只给对象的所有者
        return obj.user == request.user


class WechatAPIRatePermission(permissions.BasePermission):
    """微信API频率限制权限"""
    
    def has_permission(self, request, view):
        """检查API调用频率限制"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 超级管理员不受频率限制
        if request.user.is_superuser:
            return True
        
        # 这里可以实现具体的频率限制逻辑
        # 例如使用Redis或缓存来记录用户的API调用次数
        from django.core.cache import cache
        
        cache_key = f'wechat_api_rate_{request.user.id}'
        current_calls = cache.get(cache_key, 0)
        
        # 每分钟最多100次调用
        max_calls_per_minute = 100
        
        if current_calls >= max_calls_per_minute:
            return False
        
        # 增加调用次数
        cache.set(cache_key, current_calls + 1, 60)  # 60秒过期
        
        return True


def create_wechat_groups():
    """创建微信相关的用户组"""
    groups_to_create = [
        ('微信管理员', '拥有微信模块的完整管理权限'),
        ('支付管理员', '拥有微信支付相关的管理权限'),
        ('统计查看员', '拥有微信统计数据的查看权限'),
    ]
    
    created_groups = []
    for group_name, description in groups_to_create:
        group, created = Group.objects.get_or_create(name=group_name)
        if created:
            created_groups.append(group_name)
    
    return created_groups


def assign_user_to_wechat_group(user, group_name):
    """将用户分配到微信相关组"""
    try:
        group = Group.objects.get(name=group_name)
        user.groups.add(group)
        return True
    except Group.DoesNotExist:
        return False


def remove_user_from_wechat_group(user, group_name):
    """从微信相关组中移除用户"""
    try:
        group = Group.objects.get(name=group_name)
        user.groups.remove(group)
        return True
    except Group.DoesNotExist:
        return False


def check_user_wechat_permission(user, permission_type):
    """检查用户的微信权限"""
    if not user or not user.is_authenticated:
        return False
    
    if user.is_superuser:
        return True
    
    permission_groups = {
        'admin': '微信管理员',
        'payment': '支付管理员',
        'stats': '统计查看员',
    }
    
    group_name = permission_groups.get(permission_type)
    if not group_name:
        return False
    
    try:
        group = Group.objects.get(name=group_name)
        return group in user.groups.all()
    except Group.DoesNotExist:
        return False
