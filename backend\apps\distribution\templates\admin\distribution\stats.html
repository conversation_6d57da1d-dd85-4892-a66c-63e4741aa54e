{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}分销系统统计 | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block branding %}
<h1 id="site-name"><a href="{% url 'admin:index' %}">智梦科技 - 分销系统管理</a></h1>
{% endblock %}

{% block nav-global %}{% endblock %}

{% block content %}
<div class="dashboard">
    <h1>分销系统统计概览</h1>
    
    <!-- 核心指标卡片 -->
    <div class="stats-cards" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
        <div class="stat-card" style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007cba;">
            <h3 style="margin: 0 0 10px 0; color: #333;">活跃分销商</h3>
            <div style="font-size: 2em; font-weight: bold; color: #007cba;">{{ total_distributors }}</div>
        </div>
        
        <div class="stat-card" style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;">
            <h3 style="margin: 0 0 10px 0; color: #333;">总佣金收入</h3>
            <div style="font-size: 2em; font-weight: bold; color: #28a745;">¥{{ total_commission|floatformat:2 }}</div>
        </div>
        
        <div class="stat-card" style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;">
            <h3 style="margin: 0 0 10px 0; color: #333;">待处理提现</h3>
            <div style="font-size: 1.5em; font-weight: bold; color: #ffc107;">
                {{ pending_withdrawals.count }} 笔<br>
                <small style="font-size: 0.7em;">¥{{ pending_withdrawals.amount|default:0|floatformat:2 }}</small>
            </div>
        </div>
        
        <div class="stat-card" style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #17a2b8;">
            <h3 style="margin: 0 0 10px 0; color: #333;">活跃推广码</h3>
            <div style="font-size: 2em; font-weight: bold; color: #17a2b8;">{{ active_promotions }}</div>
        </div>
        
        <div class="stat-card" style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #6f42c1;">
            <h3 style="margin: 0 0 10px 0; color: #333;">进行中活动</h3>
            <div style="font-size: 2em; font-weight: bold; color: #6f42c1;">{{ active_activities }}</div>
        </div>
    </div>
    
    <!-- 等级分布 -->
    <div class="level-distribution" style="margin: 30px 0;">
        <h2>分销商等级分布</h2>
        <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">等级名称</th>
                        <th style="padding: 12px; text-align: center; border-bottom: 2px solid #dee2e6;">等级</th>
                        <th style="padding: 12px; text-align: right; border-bottom: 2px solid #dee2e6;">分销商数量</th>
                        <th style="padding: 12px; text-align: right; border-bottom: 2px solid #dee2e6;">占比</th>
                    </tr>
                </thead>
                <tbody>
                    {% for level in level_distribution %}
                    <tr>
                        <td style="padding: 12px; border-bottom: 1px solid #dee2e6;">{{ level.name }}</td>
                        <td style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6;">{{ level.level }}</td>
                        <td style="padding: 12px; text-align: right; border-bottom: 1px solid #dee2e6;">{{ level.distributor_count }}</td>
                        <td style="padding: 12px; text-align: right; border-bottom: 1px solid #dee2e6;">
                            {% if total_distributors > 0 %}
                                {{ level.distributor_count|mul:100|div:total_distributors|floatformat:1 }}%
                            {% else %}
                                0%
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="4" style="padding: 20px; text-align: center; color: #6c757d;">暂无数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 最近7天佣金趋势 -->
    <div class="commission-trend" style="margin: 30px 0;">
        <h2>最近7天佣金趋势</h2>
        <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">日期</th>
                        <th style="padding: 12px; text-align: right; border-bottom: 2px solid #dee2e6;">佣金金额</th>
                        <th style="padding: 12px; text-align: right; border-bottom: 2px solid #dee2e6;">订单数量</th>
                    </tr>
                </thead>
                <tbody>
                    {% for commission in recent_commissions %}
                    <tr>
                        <td style="padding: 12px; border-bottom: 1px solid #dee2e6;">{{ commission.day }}</td>
                        <td style="padding: 12px; text-align: right; border-bottom: 1px solid #dee2e6;">¥{{ commission.total|floatformat:2 }}</td>
                        <td style="padding: 12px; text-align: right; border-bottom: 1px solid #dee2e6;">{{ commission.count }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="3" style="padding: 20px; text-align: center; color: #6c757d;">暂无数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 快捷操作 -->
    <div class="quick-actions" style="margin: 30px 0;">
        <h2>快捷操作</h2>
        <div style="display: flex; gap: 15px; flex-wrap: wrap;">
            <a href="{% url 'admin:distribution_distributionrelation_changelist' %}" 
               style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">
                管理分销商
            </a>
            <a href="{% url 'admin:distribution_commissionrecord_changelist' %}" 
               style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">
                佣金记录
            </a>
            <a href="{% url 'admin:distribution_withdrawalrecord_changelist' %}" 
               style="background: #ffc107; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">
                提现管理
            </a>
            <a href="{% url 'admin:distribution_promotioncode_changelist' %}" 
               style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">
                推广码管理
            </a>
            <a href="{% url 'admin:distribution_marketingactivity_changelist' %}" 
               style="background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">
                营销活动
            </a>
        </div>
    </div>
</div>

<style>
.dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.stats-cards {
    margin-bottom: 30px;
}

.stat-card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.quick-actions a:hover {
    opacity: 0.9;
    transform: translateY(-1px);
    transition: all 0.2s ease;
}
</style>
{% endblock %}
