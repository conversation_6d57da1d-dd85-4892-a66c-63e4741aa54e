"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Math) {
  common_vendor.unref(Icon)();
}
const Icon = () => "../../components/common/Icon.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "profile",
  setup(__props) {
    const isLogin = common_vendor.ref(false);
    const userInfo = common_vendor.ref(new UTSJSONObject({
      nickname: "测试用户",
      username: "testuser",
      avatar: "",
      is_distributor: false,
      distributor_level: 0
    }));
    const orderStats = common_vendor.ref(new UTSJSONObject({
      pending: 2,
      paid: 1,
      shipped: 3,
      completed: 5
    }));
    common_vendor.onMounted(() => {
      initPage();
    });
    const initPage = () => {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        common_vendor.index.__f__("log", "at pages/user/profile.uvue:160", "初始化用户中心");
      });
    };
    const handleLogin = () => {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        isLogin.value = true;
        common_vendor.index.showToast({
          title: "登录成功",
          icon: "success"
        });
      });
    };
    const handleLogout = () => {
      common_vendor.index.showModal(new UTSJSONObject({
        title: "提示",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            isLogin.value = false;
            common_vendor.index.showToast({
              title: "已退出登录",
              icon: "success"
            });
          }
        }
      }));
    };
    const goToOrders = (status = null) => {
      const url = status ? `/pages/order/list?status=${status}` : "/pages/order/list";
      common_vendor.index.navigateTo({ url });
    };
    const goToDistribution = () => {
      common_vendor.index.navigateTo({
        url: "/pages/distribution/index"
      });
    };
    const goToCoupons = () => {
      common_vendor.index.navigateTo({
        url: "/pages/coupons/list"
      });
    };
    const goToAddress = () => {
      common_vendor.index.navigateTo({
        url: "/pages/address/list"
      });
    };
    const goToService = () => {
      common_vendor.index.navigateTo({
        url: "/pages/service/index"
      });
    };
    const goToFeedback = () => {
      common_vendor.index.navigateTo({
        url: "/pages/feedback/index"
      });
    };
    const goToAbout = () => {
      common_vendor.index.navigateTo({
        url: "/pages/about/index"
      });
    };
    return (_ctx = null, _cache = null) => {
      const __returned__ = common_vendor.e(new UTSJSONObject({
        a: !isLogin.value
      }), !isLogin.value ? new UTSJSONObject({
        b: common_assets._imports_0,
        c: common_vendor.o(handleLogin)
      }) : common_vendor.e(new UTSJSONObject({
        d: userInfo.value.avatar || "/static/default-avatar.png",
        e: common_vendor.t(userInfo.value.nickname || userInfo.value.username),
        f: userInfo.value.is_distributor
      }), userInfo.value.is_distributor ? new UTSJSONObject({
        g: common_vendor.t(userInfo.value.distributor_level)
      }) : new UTSJSONObject({})), new UTSJSONObject({
        h: common_vendor.p(new UTSJSONObject({
          name: "arrow-right",
          size: "16",
          color: "#999"
        })),
        i: isLogin.value
      }), isLogin.value ? new UTSJSONObject({
        j: common_vendor.p(new UTSJSONObject({
          name: "arrow-right",
          size: "12",
          color: "#999"
        })),
        k: common_vendor.o(goToOrders),
        l: common_vendor.p(new UTSJSONObject({
          name: "clock",
          size: "24",
          color: "#ff9500"
        })),
        m: common_vendor.t(orderStats.value.pending),
        n: common_vendor.o(($event = null) => {
          return goToOrders("pending");
        }),
        o: common_vendor.p(new UTSJSONObject({
          name: "truck",
          size: "24",
          color: "#007aff"
        })),
        p: common_vendor.t(orderStats.value.paid),
        q: common_vendor.o(($event = null) => {
          return goToOrders("paid");
        }),
        r: common_vendor.p(new UTSJSONObject({
          name: "package",
          size: "24",
          color: "#34c759"
        })),
        s: common_vendor.t(orderStats.value.shipped),
        t: common_vendor.o(($event = null) => {
          return goToOrders("shipped");
        }),
        v: common_vendor.p(new UTSJSONObject({
          name: "star",
          size: "24",
          color: "#ff3b30"
        })),
        w: common_vendor.t(orderStats.value.completed),
        x: common_vendor.o(($event = null) => {
          return goToOrders("completed");
        })
      }) : new UTSJSONObject({}), new UTSJSONObject({
        y: isLogin.value
      }), isLogin.value ? common_vendor.e(new UTSJSONObject({
        z: common_vendor.p(new UTSJSONObject({
          name: "team",
          size: "20",
          color: "#007aff"
        })),
        A: userInfo.value.is_distributor
      }), userInfo.value.is_distributor ? new UTSJSONObject({}) : new UTSJSONObject({}), new UTSJSONObject({
        B: common_vendor.p(new UTSJSONObject({
          name: "arrow-right",
          size: "12",
          color: "#999"
        })),
        C: common_vendor.o(goToDistribution)
      })) : new UTSJSONObject({}), new UTSJSONObject({
        D: common_vendor.p(new UTSJSONObject({
          name: "gift",
          size: "20",
          color: "#ff9500"
        })),
        E: common_vendor.p(new UTSJSONObject({
          name: "arrow-right",
          size: "12",
          color: "#999"
        })),
        F: common_vendor.o(goToCoupons),
        G: isLogin.value
      }), isLogin.value ? new UTSJSONObject({
        H: common_vendor.p(new UTSJSONObject({
          name: "location",
          size: "20",
          color: "#34c759"
        })),
        I: common_vendor.p(new UTSJSONObject({
          name: "arrow-right",
          size: "12",
          color: "#999"
        })),
        J: common_vendor.o(goToAddress)
      }) : new UTSJSONObject({}), new UTSJSONObject({
        K: common_vendor.p(new UTSJSONObject({
          name: "service",
          size: "20",
          color: "#5856d6"
        })),
        L: common_vendor.p(new UTSJSONObject({
          name: "arrow-right",
          size: "12",
          color: "#999"
        })),
        M: common_vendor.o(goToService),
        N: common_vendor.p(new UTSJSONObject({
          name: "feedback",
          size: "20",
          color: "#af52de"
        })),
        O: common_vendor.p(new UTSJSONObject({
          name: "arrow-right",
          size: "12",
          color: "#999"
        })),
        P: common_vendor.o(goToFeedback),
        Q: common_vendor.p(new UTSJSONObject({
          name: "info",
          size: "20",
          color: "#8e8e93"
        })),
        R: common_vendor.p(new UTSJSONObject({
          name: "arrow-right",
          size: "12",
          color: "#999"
        })),
        S: common_vendor.o(goToAbout),
        T: isLogin.value
      }), isLogin.value ? new UTSJSONObject({
        U: common_vendor.o(handleLogout)
      }) : new UTSJSONObject({}), new UTSJSONObject({
        V: common_vendor.sei(common_vendor.gei(_ctx, ""), "view")
      }));
      return __returned__;
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0fb36f05"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/profile.js.map
