{"version": 3, "file": "storage.js", "sources": ["utils/storage.ts"], "sourcesContent": ["/**\n * 本地存储工具\n * 封装uni-app的存储API，提供类型安全和错误处理\n */\n\nimport { isDev } from './config'\n\n// 存储键名前缀\nconst STORAGE_PREFIX = 'zmkj_'\n\n/**\n * 获取完整的存储键名\n * @param key 原始键名\n * @returns 带前缀的键名\n */\nfunction getStorageKey(key: string): string {\n  return STORAGE_PREFIX + key\n}\n\n/**\n * 设置存储数据\n * @param key 存储键名\n * @param value 存储值\n * @returns 是否成功\n */\nexport function setStorage(key: string, value: any): boolean {\n  try {\n    const storageKey = getStorageKey(key)\n    uni.setStorageSync(storageKey, value)\n    \n    if (isDev()) {\n      console.log(`Storage Set: ${key} =`, value)\n    }\n    \n    return true\n  } catch (error) {\n    console.error(`设置存储失败 [${key}]:`, error)\n    return false\n  }\n}\n\n/**\n * 获取存储数据\n * @param key 存储键名\n * @param defaultValue 默认值\n * @returns 存储值或默认值\n */\nexport function getStorage<T = any>(key: string, defaultValue: T | null = null): T | null {\n  try {\n    const storageKey = getStorageKey(key)\n    const value = uni.getStorageSync(storageKey)\n    \n    if (isDev()) {\n      console.log(`Storage Get: ${key} =`, value)\n    }\n    \n    return value !== '' ? value : defaultValue\n  } catch (error) {\n    console.error(`获取存储失败 [${key}]:`, error)\n    return defaultValue\n  }\n}\n\n/**\n * 移除存储数据\n * @param key 存储键名\n * @returns 是否成功\n */\nexport function removeStorage(key: string): boolean {\n  try {\n    const storageKey = getStorageKey(key)\n    uni.removeStorageSync(storageKey)\n    \n    if (isDev()) {\n      console.log(`Storage Remove: ${key}`)\n    }\n    \n    return true\n  } catch (error) {\n    console.error(`移除存储失败 [${key}]:`, error)\n    return false\n  }\n}\n\n/**\n * 检查存储是否存在\n * @param key 存储键名\n * @returns 是否存在\n */\nexport function hasStorage(key: string): boolean {\n  try {\n    const storageKey = getStorageKey(key)\n    const value = uni.getStorageSync(storageKey)\n    return value !== ''\n  } catch (error) {\n    console.error(`检查存储失败 [${key}]:`, error)\n    return false\n  }\n}\n\n/**\n * 获取所有存储信息\n * @returns 存储信息\n */\nexport function getStorageInfo(): {\n  keys: string[]\n  currentSize: number\n  limitSize: number\n} {\n  try {\n    const info = uni.getStorageInfoSync()\n    return {\n      keys: info.keys.filter(key => key.startsWith(STORAGE_PREFIX)),\n      currentSize: info.currentSize,\n      limitSize: info.limitSize\n    }\n  } catch (error) {\n    console.error('获取存储信息失败:', error)\n    return {\n      keys: [],\n      currentSize: 0,\n      limitSize: 0\n    }\n  }\n}\n\n/**\n * 清空所有应用存储\n * @returns 是否成功\n */\nexport function clearStorage(): boolean {\n  try {\n    const info = getStorageInfo()\n    info.keys.forEach(key => {\n      uni.removeStorageSync(key)\n    })\n    \n    if (isDev()) {\n      console.log('Storage Cleared')\n    }\n    \n    return true\n  } catch (error) {\n    console.error('清空存储失败:', error)\n    return false\n  }\n}\n\n/**\n * 异步设置存储数据\n * @param key 存储键名\n * @param value 存储值\n * @returns Promise<boolean>\n */\nexport function setStorageAsync(key: string, value: any): Promise<boolean> {\n  return new Promise((resolve) => {\n    try {\n      const storageKey = getStorageKey(key)\n      uni.setStorage({\n        key: storageKey,\n        data: value,\n        success: () => {\n          if (isDev()) {\n            console.log(`Storage Set Async: ${key} =`, value)\n          }\n          resolve(true)\n        },\n        fail: (error) => {\n          console.error(`异步设置存储失败 [${key}]:`, error)\n          resolve(false)\n        }\n      })\n    } catch (error) {\n      console.error(`异步设置存储失败 [${key}]:`, error)\n      resolve(false)\n    }\n  })\n}\n\n/**\n * 异步获取存储数据\n * @param key 存储键名\n * @param defaultValue 默认值\n * @returns Promise<T | null>\n */\nexport function getStorageAsync<T = any>(key: string, defaultValue: T | null = null): Promise<T | null> {\n  return new Promise((resolve) => {\n    try {\n      const storageKey = getStorageKey(key)\n      uni.getStorage({\n        key: storageKey,\n        success: (res) => {\n          if (isDev()) {\n            console.log(`Storage Get Async: ${key} =`, res.data)\n          }\n          resolve(res.data)\n        },\n        fail: () => {\n          resolve(defaultValue)\n        }\n      })\n    } catch (error) {\n      console.error(`异步获取存储失败 [${key}]:`, error)\n      resolve(defaultValue)\n    }\n  })\n}\n\n/**\n * 异步移除存储数据\n * @param key 存储键名\n * @returns Promise<boolean>\n */\nexport function removeStorageAsync(key: string): Promise<boolean> {\n  return new Promise((resolve) => {\n    try {\n      const storageKey = getStorageKey(key)\n      uni.removeStorage({\n        key: storageKey,\n        success: () => {\n          if (isDev()) {\n            console.log(`Storage Remove Async: ${key}`)\n          }\n          resolve(true)\n        },\n        fail: (error) => {\n          console.error(`异步移除存储失败 [${key}]:`, error)\n          resolve(false)\n        }\n      })\n    } catch (error) {\n      console.error(`异步移除存储失败 [${key}]:`, error)\n      resolve(false)\n    }\n  })\n}\n\n/**\n * 存储管理类\n */\nexport class StorageManager {\n  private prefix: string\n\n  constructor(prefix: string = '') {\n    this.prefix = prefix\n  }\n\n  private getKey(key: string): string {\n    return this.prefix ? `${this.prefix}_${key}` : key\n  }\n\n  set(key: string, value: any): boolean {\n    return setStorage(this.getKey(key), value)\n  }\n\n  get<T = any>(key: string, defaultValue: T | null = null): T | null {\n    return getStorage(this.getKey(key), defaultValue)\n  }\n\n  remove(key: string): boolean {\n    return removeStorage(this.getKey(key))\n  }\n\n  has(key: string): boolean {\n    return hasStorage(this.getKey(key))\n  }\n\n  async setAsync(key: string, value: any): Promise<boolean> {\n    return setStorageAsync(this.getKey(key), value)\n  }\n\n  async getAsync<T = any>(key: string, defaultValue: T | null = null): Promise<T | null> {\n    return getStorageAsync(this.getKey(key), defaultValue)\n  }\n\n  async removeAsync(key: string): Promise<boolean> {\n    return removeStorageAsync(this.getKey(key))\n  }\n}\n\n// 创建默认存储管理器实例\nexport const storage = new StorageManager()\n\n// 创建用户相关存储管理器\nexport const userStorage = new StorageManager('user')\n\n// 创建应用设置存储管理器\nexport const appStorage = new StorageManager('app')\n\n// 创建缓存存储管理器\nexport const cacheStorage = new StorageManager('cache')\n\n/**\n * 缓存数据（带过期时间）\n * @param key 缓存键名\n * @param value 缓存值\n * @param expireTime 过期时间（毫秒）\n */\nexport function setCache(key: string, value: any, expireTime: number = 30 * 60 * 1000): void {\n  const cacheData = {\n    value,\n    expireTime: Date.now() + expireTime,\n    createTime: Date.now()\n  }\n  cacheStorage.set(key, cacheData)\n}\n\n/**\n * 获取缓存数据\n * @param key 缓存键名\n * @param defaultValue 默认值\n * @returns 缓存值或默认值\n */\nexport function getCache<T = any>(key: string, defaultValue: T | null = null): T | null {\n  const cacheData = cacheStorage.get(key)\n  \n  if (!cacheData || !cacheData.expireTime) {\n    return defaultValue\n  }\n  \n  // 检查是否过期\n  if (Date.now() > cacheData.expireTime) {\n    cacheStorage.remove(key)\n    return defaultValue\n  }\n  \n  return cacheData.value\n}\n\n/**\n * 移除缓存数据\n * @param key 缓存键名\n */\nexport function removeCache(key: string): boolean {\n  return cacheStorage.remove(key)\n}\n\n/**\n * 清空所有缓存\n */\nexport function clearCache(): void {\n  const info = getStorageInfo()\n  const cachePrefix = getStorageKey('cache_')\n  \n  info.keys.forEach(key => {\n    if (key.startsWith(cachePrefix)) {\n      uni.removeStorageSync(key)\n    }\n  })\n}\n\nexport default {\n  setStorage,\n  getStorage,\n  removeStorage,\n  hasStorage,\n  getStorageInfo,\n  clearStorage,\n  setStorageAsync,\n  getStorageAsync,\n  removeStorageAsync,\n  StorageManager,\n  storage,\n  userStorage,\n  appStorage,\n  cacheStorage,\n  setCache,\n  getCache,\n  removeCache,\n  clearCache\n}\n"], "names": ["uni", "isDev"], "mappings": ";;;AAQA,MAAM,iBAAiB;AAOvB,SAAS,cAAc,KAAW;AAChC,SAAO,iBAAiB;AAC1B;SA8BgB,WAAoB,KAAa,eAAyB,MAAI;AAC5E,MAAI;AACF,UAAM,aAAa,cAAc,GAAG;AACpC,UAAM,QAAQA,cAAAA,MAAI,eAAe,UAAU;AAE3C,QAAIC,aAAK,MAAA,GAAI;AACXD,iEAAY,gBAAgB,GAAG,MAAM,KAAK;AAAA,IAC3C;AAED,WAAO,UAAU,KAAK,QAAQ;AAAA,EAC/B,SAAQ,OAAO;AACdA,wBAAA,MAAA,SAAA,0BAAc,WAAW,GAAG,MAAM,KAAK;AACvC,WAAO;AAAA,EACR;AACH;AAOM,SAAU,cAAc,KAAW;AACvC,MAAI;AACF,UAAM,aAAa,cAAc,GAAG;AACpCA,wBAAI,kBAAkB,UAAU;AAEhC,QAAIC,aAAK,MAAA,GAAI;AACXD,oBAAA,MAAA,MAAA,OAAA,0BAAY,mBAAmB,GAAG,EAAE;AAAA,IACrC;AAED,WAAO;AAAA,EACR,SAAQ,OAAO;AACdA,wBAAA,MAAA,SAAA,0BAAc,WAAW,GAAG,MAAM,KAAK;AACvC,WAAO;AAAA,EACR;AACH;;;"}