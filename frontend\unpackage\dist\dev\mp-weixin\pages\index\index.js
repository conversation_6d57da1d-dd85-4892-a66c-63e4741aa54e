"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_config = require("../../utils/config.js");
const utils_request = require("../../utils/request.js");
if (!Math) {
  common_vendor.unref(Icon)();
}
const Icon = () => "../../components/common/Icon.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const loading = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const banners = common_vendor.ref([]);
    const navItems = common_vendor.ref([
      new UTSJSONObject({ icon: "category", title: "分类", color: "#ff6b35", path: "/pages/products/category" }),
      new UTSJSONObject({ icon: "hot", title: "热销", color: "#ff3b30", path: "/pages/products/list?is_hot=true" }),
      new UTSJSONObject({ icon: "new", title: "新品", color: "#34c759", path: "/pages/products/list?is_new=true" }),
      new UTSJSONObject({ icon: "gift", title: "优惠", color: "#ff9500", path: "/pages/coupons/list" }),
      new UTSJSONObject({ icon: "team", title: "分销", color: "#007aff", path: "/pages/distribution/index" }),
      new UTSJSONObject({ icon: "service", title: "客服", color: "#5856d6", path: "/pages/service/index" }),
      new UTSJSONObject({ icon: "location", title: "门店", color: "#af52de", path: "/pages/stores/list" }),
      new UTSJSONObject({ icon: "more", title: "更多", color: "#8e8e93", path: "/pages/more/index" }),
      new UTSJSONObject({ icon: "settings", title: "测试", color: "#5856d6", path: "/pages/test/components" })
    ]);
    const categories = common_vendor.ref([
      new UTSJSONObject({ id: 1, name: "服装", image: `${utils_config.CONFIG.CDN_BASE_URL}/categories/clothing.jpg` }),
      new UTSJSONObject({ id: 2, name: "数码", image: `${utils_config.CONFIG.CDN_BASE_URL}/categories/digital.jpg` }),
      new UTSJSONObject({ id: 3, name: "家居", image: `${utils_config.CONFIG.CDN_BASE_URL}/categories/home.jpg` }),
      new UTSJSONObject({ id: 4, name: "美妆", image: `${utils_config.CONFIG.CDN_BASE_URL}/categories/beauty.jpg` }),
      new UTSJSONObject({ id: 5, name: "食品", image: `${utils_config.CONFIG.CDN_BASE_URL}/categories/food.jpg` }),
      new UTSJSONObject({ id: 6, name: "运动", image: `${utils_config.CONFIG.CDN_BASE_URL}/categories/sports.jpg` })
    ]);
    const recommendProducts = common_vendor.ref([
      new UTSJSONObject({
        id: 1,
        name: "夏季清爽T恤",
        price: 89,
        originalPrice: 129,
        image: `${utils_config.CONFIG.CDN_BASE_URL}/products/tshirt1.jpg`,
        isHot: true,
        isNew: false
      }),
      new UTSJSONObject({
        id: 2,
        name: "无线蓝牙耳机",
        price: 299,
        originalPrice: null,
        image: `${utils_config.CONFIG.CDN_BASE_URL}/products/earphone1.jpg`,
        isHot: false,
        isNew: true
      }),
      new UTSJSONObject({
        id: 3,
        name: "简约家居摆件",
        price: 59,
        originalPrice: 89,
        image: `${utils_config.CONFIG.CDN_BASE_URL}/products/decoration1.jpg`,
        isHot: true,
        isNew: false
      }),
      new UTSJSONObject({
        id: 4,
        name: "天然护肤套装",
        price: 199,
        originalPrice: 299,
        image: `${utils_config.CONFIG.CDN_BASE_URL}/products/skincare1.jpg`,
        isHot: false,
        isNew: true
      })
    ]);
    common_vendor.onMounted(() => {
      initPage();
    });
    const loadBanners = () => {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        try {
          const response = yield utils_request.http.get("/core/banners/", new UTSJSONObject({
            position: "home"
          }));
          if (response.code === 200) {
            banners.value = response.data;
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/index/index.uvue:180", "获取轮播图失败:", error);
          banners.value = [
            {
              id: 1,
              image: "https://via.placeholder.com/750x300/FF6B6B/FFFFFF?text=夏季新品上市",
              title: "夏季新品上市",
              link_type: "category",
              link_value: "1"
            },
            {
              id: 2,
              image: "https://via.placeholder.com/750x300/4ECDC4/FFFFFF?text=限时特惠活动",
              title: "限时特惠活动",
              link_type: "url",
              link_value: "/pages/products/list?is_hot=true"
            },
            {
              id: 3,
              image: "https://via.placeholder.com/750x300/45B7D1/FFFFFF?text=分销招募中",
              title: "分销招募中",
              link_type: "page",
              link_value: "/pages/distribution/index"
            }
          ];
        }
      });
    };
    const initPage = () => {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        common_vendor.index.__f__("log", "at pages/index/index.uvue:210", "初始化首页");
        yield loadBanners();
      });
    };
    const goToSearch = () => {
      common_vendor.index.navigateTo({
        url: "/pages/products/search"
      });
    };
    const handleScan = () => {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        common_vendor.index.scanCode(new UTSJSONObject({
          success: (res) => {
            common_vendor.index.showToast({
              title: `扫码结果: ${res.result}`,
              icon: "none"
            });
          },
          fail: () => {
            common_vendor.index.showToast({
              title: "扫码失败",
              icon: "error"
            });
          }
        }));
      });
    };
    const handleBannerClick = (banner = null) => {
      common_vendor.index.__f__("log", "at pages/index/index.uvue:238", "点击轮播图:", banner);
      switch (banner.link_type) {
        case "product":
          common_vendor.index.navigateTo({
            url: `/pages/products/detail?id=${banner.link_value}`
          });
          break;
        case "category":
          common_vendor.index.navigateTo({
            url: `/pages/products/list?category=${banner.link_value}`
          });
          break;
        case "url":
          common_vendor.index.navigateTo({
            url: banner.link_value
          });
          break;
        case "page":
          common_vendor.index.navigateTo({
            url: banner.link_value
          });
          break;
      }
    };
    const handleNavClick = (nav = null) => {
      common_vendor.index.navigateTo({
        url: nav.path
      });
    };
    const goToCategory = () => {
      common_vendor.index.navigateTo({
        url: "/pages/products/category"
      });
    };
    const handleCategoryClick = (category = null) => {
      common_vendor.index.navigateTo({
        url: `/pages/products/list?category_id=${category.id}`
      });
    };
    const goToProductList = () => {
      common_vendor.index.navigateTo({
        url: "/pages/products/list?is_recommended=true"
      });
    };
    const handleProductClick = (product = null) => {
      common_vendor.index.navigateTo({
        url: `/pages/products/detail?id=${product.id}`
      });
    };
    return (_ctx = null, _cache = null) => {
      const __returned__ = common_vendor.e(new UTSJSONObject({
        a: common_vendor.p(new UTSJSONObject({
          name: "search",
          size: "16",
          color: "#999"
        })),
        b: common_vendor.o(goToSearch),
        c: common_vendor.p(new UTSJSONObject({
          name: "scan",
          size: "20",
          color: "#333"
        })),
        d: common_vendor.o(handleScan),
        e: common_vendor.f(banners.value, (banner = null, index = null, i0 = null) => {
          return new UTSJSONObject({
            a: banner.image,
            b: common_vendor.o(($event = null) => {
              return handleBannerClick(banner);
            }, index),
            c: index
          });
        }),
        f: common_vendor.f(navItems.value, (nav = null, index = null, i0 = null) => {
          return new UTSJSONObject({
            a: "00a60067-2-" + i0,
            b: common_vendor.p(new UTSJSONObject({
              name: nav.icon,
              size: "24",
              color: nav.color
            })),
            c: common_vendor.t(nav.title),
            d: index,
            e: common_vendor.o(($event = null) => {
              return handleNavClick(nav);
            }, index)
          });
        }),
        g: common_vendor.p(new UTSJSONObject({
          name: "arrow-right",
          size: "12",
          color: "#999"
        })),
        h: common_vendor.o(goToCategory),
        i: common_vendor.f(categories.value, (category = null, index = null, i0 = null) => {
          return new UTSJSONObject({
            a: category.image,
            b: common_vendor.t(category.name),
            c: index,
            d: common_vendor.o(($event = null) => {
              return handleCategoryClick(category);
            }, index)
          });
        }),
        j: common_vendor.p(new UTSJSONObject({
          name: "arrow-right",
          size: "12",
          color: "#999"
        })),
        k: common_vendor.o(goToProductList),
        l: common_vendor.f(recommendProducts.value, (product = null, index = null, i0 = null) => {
          return common_vendor.e(new UTSJSONObject({
            a: product.image,
            b: common_vendor.t(product.name),
            c: common_vendor.t(product.price),
            d: product.originalPrice
          }), product.originalPrice ? new UTSJSONObject({
            e: common_vendor.t(product.originalPrice)
          }) : new UTSJSONObject({}), new UTSJSONObject({
            f: product.isHot
          }), product.isHot ? new UTSJSONObject({}) : new UTSJSONObject({}), new UTSJSONObject({
            g: product.isNew
          }), product.isNew ? new UTSJSONObject({}) : new UTSJSONObject({}), new UTSJSONObject({
            h: index,
            i: common_vendor.o(($event = null) => {
              return handleProductClick(product);
            }, index)
          }));
        }),
        m: hasMore.value
      }), hasMore.value ? common_vendor.e(new UTSJSONObject({
        n: !loading.value
      }), !loading.value ? new UTSJSONObject({}) : new UTSJSONObject({})) : new UTSJSONObject({}), new UTSJSONObject({
        o: common_vendor.sei(common_vendor.gei(_ctx, ""), "view")
      }));
      return __returned__;
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-00a60067"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
