from rest_framework import serializers
from drf_spectacular.utils import extend_schema_field
from drf_spectacular.openapi import OpenApiTypes
from .models import Order, OrderItem, Payment, OrderLog
from apps.users.serializers import UserSerializer
from apps.products.serializers import ProductListSerializer


class OrderItemSerializer(serializers.ModelSerializer):
    """订单商品序列化器"""
    product_image = serializers.SerializerMethodField()
    
    class Meta:
        model = OrderItem
        fields = ['id', 'product', 'product_name', 'product_sku', 'product_price', 'quantity', 'subtotal', 'product_image']
        read_only_fields = ['id', 'subtotal']
    
    @extend_schema_field(OpenApiTypes.STR)
    def get_product_image(self, obj):
        """获取产品主图"""
        if obj.product and obj.product.images.exists():
            primary_image = obj.product.images.filter(is_primary=True).first()
            if primary_image:
                return primary_image.image.url
        return None
    
    def get_subtotal(self, obj):
        """计算小计金额"""
        return obj.subtotal


class PaymentSerializer(serializers.ModelSerializer):
    """支付记录序列化器"""
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    method_display = serializers.CharField(source='get_method_display', read_only=True)
    
    class Meta:
        model = Payment
        fields = ['id', 'payment_no', 'third_party_no', 'payment_method', 'method_display', 'amount', 'status', 'status_display', 'paid_at', 'remark']
        read_only_fields = ['id', 'payment_no', 'paid_at']


class OrderLogSerializer(serializers.ModelSerializer):
    """订单日志序列化器"""
    
    class Meta:
        model = OrderLog
        fields = ['id', 'action_type', 'content', 'created_at']
        read_only_fields = fields


class OrderListSerializer(serializers.ModelSerializer):
    """订单列表序列化器"""
    user_name = serializers.CharField(source='user.username', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    payment_method_display = serializers.CharField(source='get_payment_method_display', read_only=True)
    items_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Order
        fields = [
            'id', 'order_no', 'user', 'user_name', 'total_amount', 'shipping_fee', 
            'discount_amount', 'final_amount', 'status', 'status_display', 
            'payment_method', 'payment_method_display', 'items_count', 
            'recipient_name', 'recipient_phone', 'created_at', 'paid_at'
        ]
        read_only_fields = ['id', 'order_no', 'created_at']
    
    def get_items_count(self, obj) -> int:
        """获取订单商品数量"""
        return obj.items.count()


class OrderDetailSerializer(serializers.ModelSerializer):
    """订单详情序列化器"""
    user = UserSerializer(read_only=True)
    items = OrderItemSerializer(many=True, read_only=True)
    payments = PaymentSerializer(many=True, read_only=True)
    logs = OrderLogSerializer(many=True, read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    payment_method_display = serializers.CharField(source='get_payment_method_display', read_only=True)
    
    class Meta:
        model = Order
        fields = [
            'id', 'order_no', 'user', 'total_amount', 'shipping_fee', 'discount_amount', 
            'final_amount', 'status', 'status_display', 'payment_method', 'payment_method_display',
            'recipient_name', 'recipient_phone', 'shipping_address', 'remark', 'admin_remark',
            'tracking_number', 'created_at', 'paid_at', 'shipped_at', 'delivered_at', 
            'completed_at', 'items', 'payments', 'logs'
        ]
        read_only_fields = ['id', 'order_no', 'user', 'created_at']


class OrderCreateSerializer(serializers.ModelSerializer):
    """订单创建序列化器"""
    items = OrderItemSerializer(many=True)
    
    class Meta:
        model = Order
        fields = [
            'recipient_name', 'recipient_phone', 'shipping_address', 'remark', 
            'payment_method', 'items'
        ]
    
    def create(self, validated_data):
        """创建订单"""
        from decimal import Decimal
        import uuid
        from django.utils import timezone
        
        items_data = validated_data.pop('items')
        user = self.context['request'].user
        
        # 生成订单号
        order_no = f"ZM{timezone.now().strftime('%Y%m%d%H%M%S')}{str(uuid.uuid4().hex[:6]).upper()}"
        
        # 计算订单金额
        total_amount = Decimal('0.00')
        for item_data in items_data:
            product = item_data['product']
            quantity = item_data['quantity']
            product_price = product.price
            total_amount += product_price * quantity
        
        # 创建订单
        order = Order.objects.create(
            order_no=order_no,
            user=user,
            total_amount=total_amount,
            final_amount=total_amount,  # 暂时不考虑优惠和运费
            **validated_data
        )
        
        # 创建订单商品
        for item_data in items_data:
            product = item_data['product']
            quantity = item_data['quantity']
            product_price = product.price
            subtotal = product_price * quantity
            
            OrderItem.objects.create(
                order=order,
                product=product,
                product_name=product.name,
                product_sku=product.sku,
                product_price=product_price,
                quantity=quantity,
                subtotal=subtotal
            )
        
        # 创建订单日志
        OrderLog.objects.create(
            order=order,
            status='pending',
            remark='订单创建成功'
        )
        
        return order


class OrderUpdateSerializer(serializers.ModelSerializer):
    """订单更新序列化器"""
    
    class Meta:
        model = Order
        fields = ['status', 'tracking_number', 'admin_remark', 'shipped_at', 'delivered_at', 'completed_at']
    
    def update(self, instance, validated_data):
        """更新订单状态并记录日志"""
        old_status = instance.status
        new_status = validated_data.get('status', old_status)
        
        # 更新订单
        order = super().update(instance, validated_data)
        
        # 如果状态发生变化，记录日志
        if old_status != new_status:
            OrderLog.objects.create(
                order=order,
                status=new_status,
                remark=f'订单状态从{order.get_status_display()}变更为{dict(Order.STATUS_CHOICES)[new_status]}'
            )
        
        return order


class OrderSearchSerializer(serializers.Serializer):
    """订单搜索序列化器"""
    order_no = serializers.CharField(required=False, help_text="订单号")
    status = serializers.ChoiceField(choices=Order.STATUS_CHOICES, required=False, help_text="订单状态")
    payment_method = serializers.ChoiceField(choices=Order.PAYMENT_METHODS, required=False, help_text="支付方式")
    start_date = serializers.DateTimeField(required=False, help_text="开始日期")
    end_date = serializers.DateTimeField(required=False, help_text="结束日期")
    recipient_name = serializers.CharField(required=False, help_text="收货人姓名")
    recipient_phone = serializers.CharField(required=False, help_text="收货人电话") 