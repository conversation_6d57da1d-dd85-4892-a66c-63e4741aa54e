# -*- coding: utf-8 -*-
"""
智梦科技系统 - AI功能数据模型

AI相关的数据模型定义，包括：
- AI服务配置
- 智能对话记录
- 推荐算法配置
- 用户行为分析
- AI任务管理

创建时间：2025年7月30日
维护人员：智梦科技开发团队
"""

from django.db import models
from django.contrib.auth import get_user_model
from apps.core.models import BaseModel

User = get_user_model()


class AIServiceConfig(BaseModel):
    """AI服务配置模型"""
    
    SERVICE_TYPES = [
        ('chatbot', '智能客服'),
        ('recommendation', '产品推荐'),
        ('analysis', '行为分析'),
        ('content_generation', '内容生成'),
        ('data_mining', '数据挖掘'),
    ]
    
    name = models.CharField('服务名称', max_length=100)
    service_type = models.CharField('服务类型', max_length=50, choices=SERVICE_TYPES)
    api_endpoint = models.URLField('API端点', blank=True)
    api_key = models.CharField('API密钥', max_length=255, blank=True)
    config_data = models.JSONField('配置数据', default=dict, blank=True)
    is_active = models.BooleanField('是否启用', default=True)
    
    class Meta:
        db_table = 'ai_service_config'
        verbose_name = 'AI服务配置'
        verbose_name_plural = 'AI服务配置'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.get_service_type_display()})"


class ChatbotConversation(BaseModel):
    """智能客服对话记录模型"""
    
    SESSION_STATUS = [
        ('active', '进行中'),
        ('closed', '已结束'),
        ('transferred', '已转人工'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户', null=True, blank=True)
    session_id = models.CharField('会话ID', max_length=100, unique=True)
    status = models.CharField('会话状态', max_length=20, choices=SESSION_STATUS, default='active')
    start_time = models.DateTimeField('开始时间', auto_now_add=True)
    end_time = models.DateTimeField('结束时间', null=True, blank=True)
    
    class Meta:
        db_table = 'ai_chatbot_conversation'
        verbose_name = '智能客服对话'
        verbose_name_plural = '智能客服对话'
        ordering = ['-start_time']
    
    def __str__(self):
        return f"对话 {self.session_id} - {self.get_status_display()}"


class ChatbotMessage(BaseModel):
    """智能客服消息记录模型"""
    
    MESSAGE_TYPES = [
        ('user', '用户消息'),
        ('bot', '机器人回复'),
        ('system', '系统消息'),
    ]
    
    conversation = models.ForeignKey(ChatbotConversation, on_delete=models.CASCADE, 
                                   verbose_name='对话', related_name='messages')
    message_type = models.CharField('消息类型', max_length=20, choices=MESSAGE_TYPES)
    content = models.TextField('消息内容')
    metadata = models.JSONField('元数据', default=dict, blank=True)
    
    class Meta:
        db_table = 'ai_chatbot_message'
        verbose_name = '智能客服消息'
        verbose_name_plural = '智能客服消息'
        ordering = ['created_at']
    
    def __str__(self):
        return f"{self.get_message_type_display()}: {self.content[:50]}"


class RecommendationConfig(BaseModel):
    """推荐算法配置模型"""
    
    ALGORITHM_TYPES = [
        ('collaborative', '协同过滤'),
        ('content_based', '基于内容'),
        ('hybrid', '混合推荐'),
        ('popularity', '热门推荐'),
        ('random', '随机推荐'),
    ]
    
    name = models.CharField('配置名称', max_length=100)
    algorithm_type = models.CharField('算法类型', max_length=50, choices=ALGORITHM_TYPES)
    parameters = models.JSONField('算法参数', default=dict)
    weight = models.FloatField('权重', default=1.0)
    is_active = models.BooleanField('是否启用', default=True)
    
    class Meta:
        db_table = 'ai_recommendation_config'
        verbose_name = '推荐算法配置'
        verbose_name_plural = '推荐算法配置'
        ordering = ['-weight', '-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.get_algorithm_type_display()})"


class UserBehaviorLog(BaseModel):
    """用户行为日志模型"""
    
    ACTION_TYPES = [
        ('view', '浏览'),
        ('click', '点击'),
        ('purchase', '购买'),
        ('search', '搜索'),
        ('share', '分享'),
        ('favorite', '收藏'),
        ('comment', '评论'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户', null=True, blank=True)
    session_id = models.CharField('会话ID', max_length=100, blank=True)
    action_type = models.CharField('行为类型', max_length=20, choices=ACTION_TYPES)
    target_type = models.CharField('目标类型', max_length=50)  # product, article, etc.
    target_id = models.CharField('目标ID', max_length=100)
    context_data = models.JSONField('上下文数据', default=dict, blank=True)
    ip_address = models.GenericIPAddressField('IP地址', null=True, blank=True)
    user_agent = models.TextField('用户代理', blank=True)
    
    class Meta:
        db_table = 'ai_user_behavior_log'
        verbose_name = '用户行为日志'
        verbose_name_plural = '用户行为日志'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'action_type']),
            models.Index(fields=['target_type', 'target_id']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.user or '匿名用户'} - {self.get_action_type_display()}"


class AITask(BaseModel):
    """AI任务管理模型"""
    
    TASK_TYPES = [
        ('recommendation_training', '推荐模型训练'),
        ('behavior_analysis', '行为分析'),
        ('content_generation', '内容生成'),
        ('data_mining', '数据挖掘'),
        ('model_evaluation', '模型评估'),
    ]
    
    TASK_STATUS = [
        ('pending', '待执行'),
        ('running', '执行中'),
        ('completed', '已完成'),
        ('failed', '执行失败'),
        ('cancelled', '已取消'),
    ]
    
    name = models.CharField('任务名称', max_length=200)
    task_type = models.CharField('任务类型', max_length=50, choices=TASK_TYPES)
    status = models.CharField('任务状态', max_length=20, choices=TASK_STATUS, default='pending')
    parameters = models.JSONField('任务参数', default=dict, blank=True)
    result_data = models.JSONField('结果数据', default=dict, blank=True)
    error_message = models.TextField('错误信息', blank=True)
    start_time = models.DateTimeField('开始时间', null=True, blank=True)
    end_time = models.DateTimeField('结束时间', null=True, blank=True)
    progress = models.IntegerField('进度百分比', default=0)
    
    class Meta:
        db_table = 'ai_task'
        verbose_name = 'AI任务'
        verbose_name_plural = 'AI任务'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} - {self.get_status_display()}"
    
    @property
    def duration(self):
        """计算任务执行时长"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None
