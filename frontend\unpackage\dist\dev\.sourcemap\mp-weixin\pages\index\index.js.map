{"version": 3, "file": "index.js", "sources": ["pages/index/index.uvue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudXZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 顶部搜索栏 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"search-bar\" @click=\"goToSearch\">\r\n\t\t\t\t<Icon name=\"search\" size=\"16\" color=\"#999\" />\r\n\t\t\t\t<text class=\"search-placeholder\">搜索商品</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"scan-btn\" @click=\"handleScan\">\r\n\t\t\t\t<Icon name=\"scan\" size=\"20\" color=\"#333\" />\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 轮播图 -->\r\n\t\t<view class=\"banner-section\">\r\n\t\t\t<swiper class=\"banner-swiper\" :indicator-dots=\"true\" :autoplay=\"true\" :interval=\"3000\" :duration=\"500\">\r\n\t\t\t\t<swiper-item v-for=\"(banner, index) in banners\" :key=\"index\">\r\n\t\t\t\t\t<image class=\"banner-image\" :src=\"banner.image\" mode=\"aspectFill\" @click=\"handleBannerClick(banner)\" />\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\r\n\t\t<!-- 功能导航 -->\r\n\t\t<view class=\"nav-section\">\r\n\t\t\t<view class=\"nav-grid\">\r\n\t\t\t\t<view class=\"nav-item\" v-for=\"(nav, index) in navItems\" :key=\"index\" @click=\"handleNavClick(nav)\">\r\n\t\t\t\t\t<view class=\"nav-icon\">\r\n\t\t\t\t\t\t<Icon :name=\"nav.icon\" size=\"24\" :color=\"nav.color\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"nav-text\">{{ nav.title }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 商品分类 -->\r\n\t\t<view class=\"category-section\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">商品分类</text>\r\n\t\t\t\t<view class=\"more-btn\" @click=\"goToCategory\">\r\n\t\t\t\t\t<text class=\"more-text\">更多</text>\r\n\t\t\t\t\t<Icon name=\"arrow-right\" size=\"12\" color=\"#999\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<scroll-view class=\"category-scroll\" scroll-x=\"true\" show-scrollbar=\"false\">\r\n\t\t\t\t<view class=\"category-list\">\r\n\t\t\t\t\t<view class=\"category-item\" v-for=\"(category, index) in categories\" :key=\"index\" @click=\"handleCategoryClick(category)\">\r\n\t\t\t\t\t\t<image class=\"category-image\" :src=\"category.image\" mode=\"aspectFill\" />\r\n\t\t\t\t\t\t<text class=\"category-name\">{{ category.name }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 推荐商品 -->\r\n\t\t<view class=\"product-section\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">推荐商品</text>\r\n\t\t\t\t<view class=\"more-btn\" @click=\"goToProductList\">\r\n\t\t\t\t\t<text class=\"more-text\">更多</text>\r\n\t\t\t\t\t<Icon name=\"arrow-right\" size=\"12\" color=\"#999\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"product-grid\">\r\n\t\t\t\t<view class=\"product-item\" v-for=\"(product, index) in recommendProducts\" :key=\"index\" @click=\"handleProductClick(product)\">\r\n\t\t\t\t\t<image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\" />\r\n\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t<text class=\"product-name\">{{ product.name }}</text>\r\n\t\t\t\t\t\t<view class=\"product-price\">\r\n\t\t\t\t\t\t\t<text class=\"price-current\">￥{{ product.price }}</text>\r\n\t\t\t\t\t\t\t<text class=\"price-original\" v-if=\"product.originalPrice\">￥{{ product.originalPrice }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"product-tags\">\r\n\t\t\t\t\t\t\t<text class=\"tag hot\" v-if=\"product.isHot\">热销</text>\r\n\t\t\t\t\t\t\t<text class=\"tag new\" v-if=\"product.isNew\">新品</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 加载更多 -->\r\n\t\t<view class=\"load-more\" v-if=\"hasMore\">\r\n\t\t\t<text class=\"load-text\" v-if=\"!loading\">上拉加载更多</text>\r\n\t\t\t<text class=\"load-text\" v-else>加载中...</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup lang=\"uts\">\r\nimport { ref, onMounted } from 'vue'\r\nimport Icon from '@/components/common/Icon.uvue'\r\nimport { CONFIG } from '@/utils/config'\r\nimport { http } from '@/utils/request'\r\n\r\n// 响应式数据\r\nconst loading = ref(false)\r\nconst hasMore = ref(true)\r\n\r\n// 轮播图数据\r\nconst banners = ref([])\r\n\r\n// 导航菜单数据\r\nconst navItems = ref([\r\n\t{ icon: 'category', title: '分类', color: '#ff6b35', path: '/pages/products/category' },\r\n\t{ icon: 'hot', title: '热销', color: '#ff3b30', path: '/pages/products/list?is_hot=true' },\r\n\t{ icon: 'new', title: '新品', color: '#34c759', path: '/pages/products/list?is_new=true' },\r\n\t{ icon: 'gift', title: '优惠', color: '#ff9500', path: '/pages/coupons/list' },\r\n\t{ icon: 'team', title: '分销', color: '#007aff', path: '/pages/distribution/index' },\r\n\t{ icon: 'service', title: '客服', color: '#5856d6', path: '/pages/service/index' },\r\n\t{ icon: 'location', title: '门店', color: '#af52de', path: '/pages/stores/list' },\r\n\t{ icon: 'more', title: '更多', color: '#8e8e93', path: '/pages/more/index' },\r\n\t{ icon: 'settings', title: '测试', color: '#5856d6', path: '/pages/test/components' }\r\n])\r\n\r\n// 商品分类数据\r\nconst categories = ref([\r\n\t{ id: 1, name: '服装', image: `${CONFIG.CDN_BASE_URL}/categories/clothing.jpg` },\r\n\t{ id: 2, name: '数码', image: `${CONFIG.CDN_BASE_URL}/categories/digital.jpg` },\r\n\t{ id: 3, name: '家居', image: `${CONFIG.CDN_BASE_URL}/categories/home.jpg` },\r\n\t{ id: 4, name: '美妆', image: `${CONFIG.CDN_BASE_URL}/categories/beauty.jpg` },\r\n\t{ id: 5, name: '食品', image: `${CONFIG.CDN_BASE_URL}/categories/food.jpg` },\r\n\t{ id: 6, name: '运动', image: `${CONFIG.CDN_BASE_URL}/categories/sports.jpg` }\r\n])\r\n\r\n// 推荐商品数据\r\nconst recommendProducts = ref([\r\n\t{\r\n\t\tid: 1,\r\n\t\tname: '夏季清爽T恤',\r\n\t\tprice: 89.00,\r\n\t\toriginalPrice: 129.00,\r\n\t\timage: `${CONFIG.CDN_BASE_URL}/products/tshirt1.jpg`,\r\n\t\tisHot: true,\r\n\t\tisNew: false\r\n\t},\r\n\t{\r\n\t\tid: 2,\r\n\t\tname: '无线蓝牙耳机',\r\n\t\tprice: 299.00,\r\n\t\toriginalPrice: null,\r\n\t\timage: `${CONFIG.CDN_BASE_URL}/products/earphone1.jpg`,\r\n\t\tisHot: false,\r\n\t\tisNew: true\r\n\t},\r\n\t{\r\n\t\tid: 3,\r\n\t\tname: '简约家居摆件',\r\n\t\tprice: 59.00,\r\n\t\toriginalPrice: 89.00,\r\n\t\timage: `${CONFIG.CDN_BASE_URL}/products/decoration1.jpg`,\r\n\t\tisHot: true,\r\n\t\tisNew: false\r\n\t},\r\n\t{\r\n\t\tid: 4,\r\n\t\tname: '天然护肤套装',\r\n\t\tprice: 199.00,\r\n\t\toriginalPrice: 299.00,\r\n\t\timage: `${CONFIG.CDN_BASE_URL}/products/skincare1.jpg`,\r\n\t\tisHot: false,\r\n\t\tisNew: true\r\n\t}\r\n])\r\n\r\n// 生命周期\r\nonMounted(() => {\r\n\tinitPage()\r\n})\r\n\r\n// 获取轮播图数据\r\nconst loadBanners = async () => {\r\n\ttry {\r\n\t\tconst response = await http.get('/core/banners/', {\r\n\t\t\tposition: 'home'\r\n\t\t})\r\n\t\tif (response.code === 200) {\r\n\t\t\tbanners.value = response.data\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tuni.__f__('error','at pages/index/index.uvue:180','获取轮播图失败:', error)\r\n\t\t// 使用默认数据\r\n\t\tbanners.value = [\r\n\t\t\t{\r\n\t\t\t\tid: 1,\r\n\t\t\t\timage: 'https://via.placeholder.com/750x300/FF6B6B/FFFFFF?text=夏季新品上市',\r\n\t\t\t\ttitle: '夏季新品上市',\r\n\t\t\t\tlink_type: 'category',\r\n\t\t\t\tlink_value: '1'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tid: 2,\r\n\t\t\t\timage: 'https://via.placeholder.com/750x300/4ECDC4/FFFFFF?text=限时特惠活动',\r\n\t\t\t\ttitle: '限时特惠活动',\r\n\t\t\t\tlink_type: 'url',\r\n\t\t\t\tlink_value: '/pages/products/list?is_hot=true'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tid: 3,\r\n\t\t\t\timage: 'https://via.placeholder.com/750x300/45B7D1/FFFFFF?text=分销招募中',\r\n\t\t\t\ttitle: '分销招募中',\r\n\t\t\t\tlink_type: 'page',\r\n\t\t\t\tlink_value: '/pages/distribution/index'\r\n\t\t\t}\r\n\t\t]\r\n\t}\r\n}\r\n\r\n// 方法\r\nconst initPage = async () => {\r\n\tuni.__f__('log','at pages/index/index.uvue:210','初始化首页')\r\n\tawait loadBanners()\r\n}\r\n\r\nconst goToSearch = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/products/search'\r\n\t})\r\n}\r\n\r\nconst handleScan = async () => {\r\n\tuni.scanCode({\r\n\t\tsuccess: (res) => {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: `扫码结果: ${res.result}`,\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t},\r\n\t\tfail: () => {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '扫码失败',\r\n\t\t\t\ticon: 'error'\r\n\t\t\t})\r\n\t\t}\r\n\t})\r\n}\r\n\r\nconst handleBannerClick = (banner: any) => {\r\n\tuni.__f__('log','at pages/index/index.uvue:238','点击轮播图:', banner)\r\n\r\n\t// 根据链接类型处理跳转\r\n\tswitch (banner.link_type) {\r\n\t\tcase 'product':\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/products/detail?id=${banner.link_value}`\r\n\t\t\t})\r\n\t\t\tbreak\r\n\t\tcase 'category':\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/products/list?category=${banner.link_value}`\r\n\t\t\t})\r\n\t\t\tbreak\r\n\t\tcase 'url':\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: banner.link_value\r\n\t\t\t})\r\n\t\t\tbreak\r\n\t\tcase 'page':\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: banner.link_value\r\n\t\t\t})\r\n\t\t\tbreak\r\n\t\tcase 'none':\r\n\t\tdefault:\r\n\t\t\t// 无链接，不做任何操作\r\n\t\t\tbreak\r\n\t}\r\n}\r\n\r\nconst handleNavClick = (nav: any) => {\r\n\tuni.navigateTo({\r\n\t\turl: nav.path\r\n\t})\r\n}\r\n\r\nconst goToCategory = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/products/category'\r\n\t})\r\n}\r\n\r\nconst handleCategoryClick = (category: any) => {\r\n\tuni.navigateTo({\r\n\t\turl: `/pages/products/list?category_id=${category.id}`\r\n\t})\r\n}\r\n\r\nconst goToProductList = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/products/list?is_recommended=true'\r\n\t})\r\n}\r\n\r\nconst handleProductClick = (product: any) => {\r\n\tuni.navigateTo({\r\n\t\turl: `/pages/products/detail?id=${product.id}`\r\n\t})\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n// 顶部搜索栏\r\n.header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tbackground-color: #fff;\r\n\tborder-bottom: 1px solid #eee;\r\n\r\n\t.search-bar {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\theight: 36px;\r\n\t\tpadding: 0 12px;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder-radius: 18px;\r\n\t\tmargin-right: 10px;\r\n\r\n\t\t.search-placeholder {\r\n\t\t\tmargin-left: 8px;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\t}\r\n\r\n\t.scan-btn {\r\n\t\twidth: 36px;\r\n\t\theight: 36px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n}\r\n\r\n// 轮播图\r\n.banner-section {\r\n\tmargin: 10px 15px;\r\n\r\n\t.banner-swiper {\r\n\t\theight: 180px;\r\n\t\tborder-radius: 8px;\r\n\t\toverflow: hidden;\r\n\r\n\t\t.banner-image {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 功能导航\r\n.nav-section {\r\n\tmargin: 10px 15px;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 8px;\r\n\tpadding: 20px 0;\r\n\r\n\t.nav-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\r\n\t\t.nav-item {\r\n\t\t\twidth: 25%;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 20px;\r\n\r\n\t\t\t&:nth-child(n+5) {\r\n\t\t\t\tmargin-bottom: 0;\r\n\t\t\t}\r\n\r\n\t\t\t.nav-icon {\r\n\t\t\t\twidth: 44px;\r\n\t\t\t\theight: 44px;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tbackground-color: #f8f8f8;\r\n\t\t\t\tborder-radius: 22px;\r\n\t\t\t\tmargin-bottom: 8px;\r\n\t\t\t}\r\n\r\n\t\t\t.nav-text {\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 通用区块样式\r\n.section-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tpadding: 0 15px;\r\n\tmargin-bottom: 15px;\r\n\r\n\t.section-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.more-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\t.more-text {\r\n\t\t\tfont-size: 12px;\r\n\t\t\tcolor: #999;\r\n\t\t\tmargin-right: 4px;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 商品分类\r\n.category-section {\r\n\tmargin: 10px 0;\r\n\tbackground-color: #fff;\r\n\tpadding: 15px 0;\r\n\r\n\t.category-scroll {\r\n\t\twhite-space: nowrap;\r\n\r\n\t\t.category-list {\r\n\t\t\tdisplay: flex;\r\n\t\t\tpadding: 0 15px;\r\n\r\n\t\t\t.category-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tmargin-right: 20px;\r\n\t\t\t\tmin-width: 60px;\r\n\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tmargin-right: 15px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.category-image {\r\n\t\t\t\t\twidth: 50px;\r\n\t\t\t\t\theight: 50px;\r\n\t\t\t\t\tborder-radius: 25px;\r\n\t\t\t\t\tmargin-bottom: 8px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.category-name {\r\n\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 推荐商品\r\n.product-section {\r\n\tmargin: 10px 0;\r\n\tbackground-color: #fff;\r\n\tpadding: 15px 0;\r\n\r\n\t.product-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 0 15px;\r\n\r\n\t\t.product-item {\r\n\t\t\twidth: calc(50% - 5px);\r\n\t\t\tmargin-right: 10px;\r\n\t\t\tmargin-bottom: 15px;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 8px;\r\n\t\t\toverflow: hidden;\r\n\t\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n\t\t\t&:nth-child(2n) {\r\n\t\t\t\tmargin-right: 0;\r\n\t\t\t}\r\n\r\n\t\t\t.product-image {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 140px;\r\n\t\t\t}\r\n\r\n\t\t\t.product-info {\r\n\t\t\t\tpadding: 12px;\r\n\r\n\t\t\t\t.product-name {\r\n\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tline-height: 1.4;\r\n\t\t\t\t\tmargin-bottom: 8px;\r\n\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t-webkit-line-clamp: 2;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.product-price {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tmargin-bottom: 8px;\r\n\r\n\t\t\t\t\t.price-current {\r\n\t\t\t\t\t\tfont-size: 16px;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\tcolor: #ff3b30;\r\n\t\t\t\t\t\tmargin-right: 8px;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.price-original {\r\n\t\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\ttext-decoration: line-through;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.product-tags {\r\n\t\t\t\t\tdisplay: flex;\r\n\r\n\t\t\t\t\t.tag {\r\n\t\t\t\t\t\tfont-size: 10px;\r\n\t\t\t\t\t\tpadding: 2px 6px;\r\n\t\t\t\t\t\tborder-radius: 2px;\r\n\t\t\t\t\t\tmargin-right: 4px;\r\n\r\n\t\t\t\t\t\t&.hot {\r\n\t\t\t\t\t\t\tbackground-color: #ff3b30;\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t&.new {\r\n\t\t\t\t\t\t\tbackground-color: #34c759;\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 加载更多\r\n.load-more {\r\n\tpadding: 20px;\r\n\ttext-align: center;\r\n\r\n\t.load-text {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #999;\r\n\t}\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'F:/zmkj-system/frontend/pages/index/index.uvue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "CONFIG", "onMounted", "__awaiter", "http", "uni"], "mappings": ";;;;;;;AA0FA,MAAO,OAAU,MAAA;;;;AAKjB,UAAM,UAAUA,kBAAI,KAAK;AACzB,UAAM,UAAUA,kBAAI,IAAI;AAGxB,UAAM,UAAUA,kBAAI,CAAA,CAAE;AAGtB,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACpB,IAAA,cAAA,EAAE,MAAM,YAAY,OAAO,MAAM,OAAO,WAAW,MAAM,4BAA4B;AAAA,MACrF,IAAA,cAAA,EAAE,MAAM,OAAO,OAAO,MAAM,OAAO,WAAW,MAAM,oCAAoC;AAAA,MACxF,IAAA,cAAA,EAAE,MAAM,OAAO,OAAO,MAAM,OAAO,WAAW,MAAM,oCAAoC;AAAA,MACxF,IAAA,cAAA,EAAE,MAAM,QAAQ,OAAO,MAAM,OAAO,WAAW,MAAM,uBAAuB;AAAA,MAC5E,IAAA,cAAA,EAAE,MAAM,QAAQ,OAAO,MAAM,OAAO,WAAW,MAAM,6BAA6B;AAAA,MAClF,IAAA,cAAA,EAAE,MAAM,WAAW,OAAO,MAAM,OAAO,WAAW,MAAM,wBAAwB;AAAA,MAChF,IAAA,cAAA,EAAE,MAAM,YAAY,OAAO,MAAM,OAAO,WAAW,MAAM,sBAAsB;AAAA,MAC/E,IAAA,cAAA,EAAE,MAAM,QAAQ,OAAO,MAAM,OAAO,WAAW,MAAM,qBAAqB;AAAA,MAC1E,IAAA,cAAA,EAAE,MAAM,YAAY,OAAO,MAAM,OAAO,WAAW,MAAM,0BAA0B;AAAA,IACnF,CAAA;AAGD,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACtB,IAAA,cAAA,EAAE,IAAI,GAAG,MAAM,MAAM,OAAO,GAAGC,aAAM,OAAC,YAAY,2BAA0B,CAAE;AAAA,MAC9E,IAAA,cAAA,EAAE,IAAI,GAAG,MAAM,MAAM,OAAO,GAAGA,aAAM,OAAC,YAAY,0BAAyB,CAAE;AAAA,MAC7E,IAAA,cAAA,EAAE,IAAI,GAAG,MAAM,MAAM,OAAO,GAAGA,aAAM,OAAC,YAAY,uBAAsB,CAAE;AAAA,MAC1E,IAAA,cAAA,EAAE,IAAI,GAAG,MAAM,MAAM,OAAO,GAAGA,aAAM,OAAC,YAAY,yBAAwB,CAAE;AAAA,MAC5E,IAAA,cAAA,EAAE,IAAI,GAAG,MAAM,MAAM,OAAO,GAAGA,aAAM,OAAC,YAAY,uBAAsB,CAAE;AAAA,MAC1E,IAAA,cAAA,EAAE,IAAI,GAAG,MAAM,MAAM,OAAO,GAAGA,aAAM,OAAC,YAAY,yBAAwB,CAAE;AAAA,IAC5E,CAAA;AAGD,UAAM,oBAAoBD,cAAAA,IAAI;AAAA,MAC7B,IAAA,cAAA;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,OAAO,GAAGC,oBAAO,YAAY;AAAA,QAC7B,OAAO;AAAA,QACP,OAAO;AAAA,MACP,CAAA;AAAA,MACD,IAAA,cAAA;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,OAAO,GAAGA,oBAAO,YAAY;AAAA,QAC7B,OAAO;AAAA,QACP,OAAO;AAAA,MACP,CAAA;AAAA,MACD,IAAA,cAAA;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,OAAO,GAAGA,oBAAO,YAAY;AAAA,QAC7B,OAAO;AAAA,QACP,OAAO;AAAA,MACP,CAAA;AAAA,MACD,IAAA,cAAA;AAAA,QACC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,OAAO,GAAGA,oBAAO,YAAY;AAAA,QAC7B,OAAO;AAAA,QACP,OAAO;AAAA,MACP,CAAA;AAAA,IACD,CAAA;AAGDC,kBAAAA,UAAU,MAAA;AACT;IACD,CAAC;AAGD,UAAM,cAAc,MAAA;AAAA,aAAAC,cAAA,UAAA,MAAA,QAAA,QAAA,aAAA;AACnB,YAAI;AACH,gBAAM,WAAW,MAAMC,cAAI,KAAC,IAAI,kBAAkB,IAAA,cAAA;AAAA,YACjD,UAAU;AAAA,UACV,CAAA,CAAA;AACD,cAAI,SAAS,SAAS,KAAK;AAC1B,oBAAQ,QAAQ,SAAS;AAAA,UACzB;AAAA,QACD,SAAQ,OAAO;AACfC,wBAAG,MAAC,MAAM,SAAQ,iCAAgC,YAAY,KAAK;AAEnE,kBAAQ,QAAQ;AAAA,YACf;AAAA,cACC,IAAI;AAAA,cACJ,OAAO;AAAA,cACP,OAAO;AAAA,cACP,WAAW;AAAA,cACX,YAAY;AAAA,YACZ;AAAA,YACD;AAAA,cACC,IAAI;AAAA,cACJ,OAAO;AAAA,cACP,OAAO;AAAA,cACP,WAAW;AAAA,cACX,YAAY;AAAA,YACZ;AAAA,YACD;AAAA,cACC,IAAI;AAAA,cACJ,OAAO;AAAA,cACP,OAAO;AAAA,cACP,WAAW;AAAA,cACX,YAAY;AAAA,YACZ;AAAA;QAEF;AAAA,MACD,CAAA;AAAA;AAGD,UAAM,WAAW,MAAA;AAAA,aAAAF,cAAA,UAAA,MAAA,QAAA,QAAA,aAAA;AAChBE,sBAAAA,MAAI,MAAM,OAAM,iCAAgC,OAAO;AACvD,cAAM,YAAW;AAAA,MACjB,CAAA;AAAA;AAED,UAAM,aAAa,MAAA;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACF;AAEA,UAAM,aAAa,MAAA;AAAA,aAAAF,cAAA,UAAA,MAAA,QAAA,QAAA,aAAA;AAClBE,4BAAI,SAAS,IAAA,cAAA;AAAA,UACZ,SAAS,CAAC,QAAG;AACZA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,SAAS,IAAI,MAAM;AAAA,cAC1B,MAAM;AAAA,YACN,CAAA;AAAA,UACD;AAAA,UACD,MAAM,MAAA;AACLA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACN,CAAA;AAAA,UACD;AAAA,QACD,CAAA,CAAA;AAAA,MACD,CAAA;AAAA;AAED,UAAM,oBAAoB,CAAC,SAAW,SAAA;AACrCA,oBAAG,MAAC,MAAM,OAAM,iCAAgC,UAAU,MAAM;AAGhE,cAAQ,OAAO,WAAS;AAAA,QACvB,KAAK;AACJA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK,6BAA6B,OAAO,UAAU;AAAA,UACnD,CAAA;AACD;AAAA,QACD,KAAK;AACJA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK,iCAAiC,OAAO,UAAU;AAAA,UACvD,CAAA;AACD;AAAA,QACD,KAAK;AACJA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK,OAAO;AAAA,UACZ,CAAA;AACD;AAAA,QACD,KAAK;AACJA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK,OAAO;AAAA,UACZ,CAAA;AACD;AAAA,MAKD;AAAA,IACF;AAEA,UAAM,iBAAiB,CAAC,MAAQ,SAAA;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,IAAI;AAAA,MACT,CAAA;AAAA,IACF;AAEA,UAAM,eAAe,MAAA;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACF;AAEA,UAAM,sBAAsB,CAAC,WAAa,SAAA;AACzCA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,oCAAoC,SAAS,EAAE;AAAA,MACpD,CAAA;AAAA,IACF;AAEA,UAAM,kBAAkB,MAAA;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACF;AAEA,UAAM,qBAAqB,CAAC,UAAY,SAAA;AACvCA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,6BAA6B,QAAQ,EAAE;AAAA,MAC5C,CAAA;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvSA,GAAG,WAAW,eAAe;"}