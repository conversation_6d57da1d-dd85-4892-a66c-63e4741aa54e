"""
微信应用视图
"""
import json
import hashlib
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.conf import settings
from django.utils import timezone
from drf_yasg.utils import swagger_auto_schema

from .models import WechatUser, WechatMessage, WechatMenu, WechatTemplate, WechatTemplateMessage, WechatConfig
from .serializers import (
    WechatUserSerializer, WechatMessageSerializer,
    WechatMenuSerializer, WechatTemplateSerializer
)
from .docs import (
    wechat_user_list_docs, wechat_user_detail_docs,
    wechat_config_list_docs, wechat_config_platforms_docs,
    wechat_payment_create_order_docs, wechat_payment_query_order_docs,
    wechat_stats_user_docs, wechat_stats_message_docs
)
from .permissions import (
    WechatUserPermission, WechatConfigPermission, WechatPaymentPermission,
    WechatStatsPermission, WechatCallbackPermission
)
from .auth import WechatAuth, WechatSignature
from .official import WechatOfficial
from .work import WechatWork
from .miniprogram import WechatMiniprogram
from .payment import WechatPayment


class WechatUserViewSet(viewsets.ModelViewSet):
    """微信用户视图集"""
    queryset = WechatUser.objects.all()
    serializer_class = WechatUserSerializer
    permission_classes = [WechatUserPermission]

    @wechat_user_list_docs
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @wechat_user_detail_docs
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    def get_queryset(self):
        queryset = super().get_queryset()
        # 可以添加过滤条件
        subscribe = self.request.query_params.get('subscribe')
        if subscribe is not None:
            queryset = queryset.filter(subscribe=subscribe.lower() == 'true')
        return queryset
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """获取微信用户统计"""
        total_users = self.get_queryset().count()
        subscribed_users = self.get_queryset().filter(subscribe=True).count()
        
        return Response({
            'total_users': total_users,
            'subscribed_users': subscribed_users,
            'unsubscribed_users': total_users - subscribed_users
        })


class WechatMessageViewSet(viewsets.ReadOnlyModelViewSet):
    """微信消息视图集"""
    queryset = WechatMessage.objects.all()
    serializer_class = WechatMessageSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        # 可以添加过滤条件
        msg_type = self.request.query_params.get('msg_type')
        if msg_type:
            queryset = queryset.filter(msg_type=msg_type)
        
        direction = self.request.query_params.get('direction')
        if direction:
            queryset = queryset.filter(direction=direction)
        
        return queryset


class WechatMenuViewSet(viewsets.ModelViewSet):
    """微信菜单视图集"""
    queryset = WechatMenu.objects.all()
    serializer_class = WechatMenuSerializer
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['post'])
    def create_menu(self, request):
        """创建微信菜单"""
        try:
            official = WechatOfficial()
            result = official.create_menu()
            return Response({'success': True, 'data': result})
        except Exception as e:
            return Response({'success': False, 'error': str(e)}, 
                          status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def delete_menu(self, request):
        """删除微信菜单"""
        try:
            official = WechatOfficial()
            result = official.delete_menu()
            return Response({'success': True, 'data': result})
        except Exception as e:
            return Response({'success': False, 'error': str(e)}, 
                          status=status.HTTP_400_BAD_REQUEST)


class WechatTemplateViewSet(viewsets.ModelViewSet):
    """微信模板消息视图集"""
    queryset = WechatTemplate.objects.all()
    serializer_class = WechatTemplateSerializer
    permission_classes = [IsAuthenticated]


@method_decorator(csrf_exempt, name='dispatch')
class WechatOfficialVerifyView(View):
    """微信公众号验证视图"""
    
    def get(self, request):
        """验证服务器地址的有效性"""
        signature = request.GET.get('signature', '')
        timestamp = request.GET.get('timestamp', '')
        nonce = request.GET.get('nonce', '')
        echostr = request.GET.get('echostr', '')
        
        config = WechatConfig.get_config('official')
        token = config.get('token', '')
        
        if WechatSignature.check_signature(signature, timestamp, nonce, token):
            return HttpResponse(echostr)
        else:
            return HttpResponse('验证失败', status=403)


@method_decorator(csrf_exempt, name='dispatch')
class WechatOfficialCallbackView(View):
    """微信公众号消息回调视图"""
    
    def post(self, request):
        """处理微信消息"""
        try:
            # 验证签名
            signature = request.GET.get('signature', '')
            timestamp = request.GET.get('timestamp', '')
            nonce = request.GET.get('nonce', '')
            config = WechatConfig.get_config('official')
            token = config.get('token', '')
            
            if not WechatSignature.check_signature(signature, timestamp, nonce, token):
                return HttpResponse('签名验证失败', status=403)
            
            # 处理消息
            xml_data = request.body.decode('utf-8')
            official = WechatOfficial()
            response_xml = official.handle_message(xml_data)
            
            return HttpResponse(response_xml, content_type='application/xml')
            
        except Exception as e:
            print(f"处理微信消息失败: {e}")
            return HttpResponse('success')


class WechatMenuCreateView(View):
    """创建微信菜单视图"""
    
    def post(self, request):
        try:
            official = WechatOfficial()
            result = official.create_menu()
            return JsonResponse({'success': True, 'data': result})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=400)


class WechatMenuDeleteView(View):
    """删除微信菜单视图"""
    
    def post(self, request):
        try:
            official = WechatOfficial()
            result = official.delete_menu()
            return JsonResponse({'success': True, 'data': result})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=400)


@method_decorator(csrf_exempt, name='dispatch')
class WechatWorkCallbackView(View):
    """企业微信回调视图"""
    
    def post(self, request):
        """处理企业微信回调"""
        try:
            # 这里可以处理企业微信的各种回调事件
            # 比如用户变更、部门变更等
            return HttpResponse('success')
        except Exception as e:
            print(f"处理企业微信回调失败: {e}")
            return HttpResponse('success')


class WechatWorkContactsSyncView(View):
    """企业微信通讯录同步视图"""
    
    def post(self, request):
        try:
            work = WechatWork()
            result = work.sync_contacts()
            return JsonResponse(result)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=400)


class MiniprogramAuthView(View):
    """小程序登录视图"""
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            js_code = data.get('code')
            encrypted_data = data.get('encryptedData')
            iv = data.get('iv')
            
            if not js_code:
                return JsonResponse({'success': False, 'error': '缺少登录凭证'}, status=400)
            
            miniprogram = WechatMiniprogram()
            result = miniprogram.login_or_create_user(js_code, encrypted_data, iv)
            
            return JsonResponse({
                'success': True,
                'data': {
                    'user_id': result['user'].id,
                    'openid': result['openid'],
                    'unionid': result['unionid'],
                    'nickname': result['wechat_user'].nickname,
                }
            })
            
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=400)


class MiniprogramDecryptView(View):
    """小程序数据解密视图"""
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            encrypted_data = data.get('encryptedData')
            iv = data.get('iv')
            session_key = data.get('sessionKey')
            
            if not all([encrypted_data, iv, session_key]):
                return JsonResponse({'success': False, 'error': '缺少必要参数'}, status=400)
            
            miniprogram = WechatMiniprogram()
            result = miniprogram.decrypt_data(encrypted_data, iv, session_key)
            
            return JsonResponse({'success': True, 'data': result})
            
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=400)


class TemplatePushView(View):
    """模板消息推送视图"""
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            openid = data.get('openid')
            template_id = data.get('template_id')
            template_data = data.get('data')
            url = data.get('url')
            
            if not all([openid, template_id, template_data]):
                return JsonResponse({'success': False, 'error': '缺少必要参数'}, status=400)
            
            # 这里可以实现模板消息推送逻辑
            # 根据不同平台选择不同的推送方式
            
            return JsonResponse({'success': True, 'message': '推送成功'})
            
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=400)


class CustomPushView(View):
    """自定义消息推送视图"""
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            platform = data.get('platform')  # official, work, miniprogram
            target = data.get('target')  # 目标用户或群组
            content = data.get('content')
            msg_type = data.get('type', 'text')
            
            if not all([platform, target, content]):
                return JsonResponse({'success': False, 'error': '缺少必要参数'}, status=400)
            
            # 根据平台选择推送方式
            if platform == 'work':
                work = WechatWork()
                result = work.send_text_message(target, content)
            else:
                return JsonResponse({'success': False, 'error': '暂不支持该平台'}, status=400)
            
            return JsonResponse({'success': True, 'data': result})
            
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=400)


class WechatConfigViewSet(viewsets.ModelViewSet):
    """微信配置管理视图集"""
    queryset = WechatConfig.objects.all()
    permission_classes = [WechatConfigPermission]

    def get_serializer_class(self):
        from .serializers import WechatConfigSerializer
        return WechatConfigSerializer

    @wechat_config_list_docs
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @wechat_config_platforms_docs
    @action(detail=False, methods=['get'])
    def platforms(self, request):
        """获取所有平台配置"""
        try:
            configs = {}
            for platform, _ in WechatConfig.PLATFORM_CHOICES:
                configs[platform] = WechatConfig.get_platform_configs(platform)

            return Response({
                'success': True,
                'data': configs
            })
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def platform_config(self, request):
        """获取指定平台配置"""
        platform = request.query_params.get('platform')
        name = request.query_params.get('name', 'default')

        if not platform:
            return Response({
                'success': False,
                'error': '请指定平台类型'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            config = WechatConfig.get_config(platform, name)
            return Response({
                'success': True,
                'data': config
            })
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class WechatPaymentViewSet(viewsets.ViewSet):
    """微信支付视图集"""
    permission_classes = [WechatPaymentPermission]

    @wechat_payment_create_order_docs
    @action(detail=False, methods=['post'])
    def create_order(self, request):
        """创建支付订单"""
        try:
            data = request.data
            required_fields = ['out_trade_no', 'total_fee', 'body']

            for field in required_fields:
                if field not in data:
                    return Response({
                        'success': False,
                        'error': f'缺少必要参数: {field}'
                    }, status=status.HTTP_400_BAD_REQUEST)

            payment = WechatPayment()
            result = payment.unified_order(
                out_trade_no=data['out_trade_no'],
                total_fee=data['total_fee'],
                body=data['body'],
                notify_url=data.get('notify_url', ''),
                trade_type=data.get('trade_type', 'JSAPI'),
                openid=data.get('openid', '')
            )

            return Response({
                'success': True,
                'data': result
            })
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

    @wechat_payment_query_order_docs
    @action(detail=False, methods=['post'])
    def query_order(self, request):
        """查询订单状态"""
        try:
            out_trade_no = request.data.get('out_trade_no')
            transaction_id = request.data.get('transaction_id')

            if not out_trade_no and not transaction_id:
                return Response({
                    'success': False,
                    'error': '请提供订单号或微信交易号'
                }, status=status.HTTP_400_BAD_REQUEST)

            payment = WechatPayment()
            # 使用订单号或交易号查询
            order_no = out_trade_no or transaction_id
            result = payment.query_order(order_no)

            return Response({
                'success': True,
                'data': result
            })
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def refund_order(self, request):
        """申请退款"""
        try:
            data = request.data
            required_fields = ['out_refund_no', 'total_fee', 'refund_fee']

            for field in required_fields:
                if field not in data:
                    return Response({
                        'success': False,
                        'error': f'缺少必要参数: {field}'
                    }, status=status.HTTP_400_BAD_REQUEST)

            payment = WechatPayment()
            result = payment.refund(
                out_trade_no=data.get('out_trade_no'),
                transaction_id=data.get('transaction_id'),
                out_refund_no=data['out_refund_no'],
                total_fee=data['total_fee'],
                refund_fee=data['refund_fee']
            )

            return Response({
                'success': True,
                'data': result
            })
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class WechatStatsViewSet(viewsets.ViewSet):
    """微信统计视图集"""
    permission_classes = [WechatStatsPermission]

    @wechat_stats_user_docs
    @action(detail=False, methods=['get'])
    def user_stats(self, request):
        """用户统计"""
        try:
            total_users = WechatUser.objects.count()
            subscribed_users = WechatUser.objects.filter(subscribe=True).count()
            today_new_users = WechatUser.objects.filter(
                created_at__date=timezone.now().date()
            ).count()

            return Response({
                'success': True,
                'data': {
                    'total_users': total_users,
                    'subscribed_users': subscribed_users,
                    'today_new_users': today_new_users,
                    'subscribe_rate': round(subscribed_users / total_users * 100, 2) if total_users > 0 else 0
                }
            })
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

    @wechat_stats_message_docs
    @action(detail=False, methods=['get'])
    def message_stats(self, request):
        """消息统计"""
        try:
            from django.utils import timezone
            from datetime import timedelta

            today = timezone.now().date()
            week_ago = today - timedelta(days=7)

            total_messages = WechatMessage.objects.count()
            today_messages = WechatMessage.objects.filter(
                created_at__date=today
            ).count()
            week_messages = WechatMessage.objects.filter(
                created_at__date__gte=week_ago
            ).count()

            return Response({
                'success': True,
                'data': {
                    'total_messages': total_messages,
                    'today_messages': today_messages,
                    'week_messages': week_messages
                }
            })
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
