from django.test import TestCase
from apps.core.models import BaseModel

class BaseModelTest(TestCase):
    """基础模型测试"""
    
    def test_base_model_creation(self):
        """测试基础模型创建"""
        # BaseModel是抽象类，不能直接实例化
        # 这里只是测试其功能是否正确导入
        self.assertTrue(hasattr(BaseModel, 'created_at'))
        self.assertTrue(hasattr(BaseModel, 'updated_at'))
        self.assertTrue(hasattr(BaseModel, 'is_deleted'))
    
    def test_base_model_meta_abstract(self):
        """测试基础模型是抽象类"""
        self.assertTrue(BaseModel._meta.abstract)
