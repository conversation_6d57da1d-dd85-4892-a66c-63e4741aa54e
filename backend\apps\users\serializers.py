from rest_framework import serializers
from .models import User, User<PERSON><PERSON><PERSON>, UserLoginLog

class UserSerializer(serializers.ModelSerializer):
    """用户模型序列化器"""
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'phone', 'avatar', 'nickname', 'gender', 'birth_date', 'is_verified', 'date_joined']
        read_only_fields = ['id', 'date_joined']

    def validate_phone(self, value):
        """验证手机号码格式"""
        if value and len(value) != 11:
            raise serializers.ValidationError("手机号码必须是11位数字")
        return value


class UserRegisterSerializer(serializers.ModelSerializer):
    """用户注册序列化器"""
    password = serializers.CharField(write_only=True, min_length=8, style={'input_type': 'password'})
    password_confirm = serializers.CharField(write_only=True, style={'input_type': 'password'})

    class Meta:
        model = User
        fields = ['username', 'email', 'phone', 'password', 'password_confirm', 'nickname']

    def validate(self, data):
        """验证两次密码是否一致"""
        if data.get('password') != data.get('password_confirm'):
            raise serializers.ValidationError({"password_confirm": "两次密码输入不一致"})
        return data

    def create(self, validated_data):
        """创建用户并加密密码"""
        validated_data.pop('password_confirm')
        user = User.objects.create_user(**validated_data)
        return user


class UserLoginSerializer(serializers.Serializer):
    """用户登录序列化器"""
    username = serializers.CharField(required=False)
    phone = serializers.CharField(required=False)
    email = serializers.EmailField(required=False)
    password = serializers.CharField(style={'input_type': 'password'})

    def validate(self, data):
        """验证至少提供一种登录方式"""
        if not data.get('username') and not data.get('phone') and not data.get('email'):
            raise serializers.ValidationError("必须提供用户名、手机号码或邮箱中的一种")
        return data


class UserAddressSerializer(serializers.ModelSerializer):
    """用户地址序列化器"""
    class Meta:
        model = UserAddress
        fields = ['id', 'recipient_name', 'recipient_phone', 'province', 'city', 'district', 'detailed_address', 'postal_code', 'is_default']

    def create(self, validated_data):
        """创建地址时，如果设为默认地址，则更新用户其他地址为非默认"""
        user = self.context['request'].user
        if validated_data.get('is_default', False):
            UserAddress.objects.filter(user=user, is_default=True).update(is_default=False)
        return super().create(validated_data)


class UserLoginLogSerializer(serializers.ModelSerializer):
    """用户登录日志序列化器"""
    class Meta:
        model = UserLoginLog
        fields = ['id', 'ip_address', 'login_time', 'logout_time', 'device_info', 'browser_info']
        read_only_fields = fields


class UserProfileUpdateSerializer(serializers.ModelSerializer):
    """用户资料更新序列化器"""
    class Meta:
        model = User
        fields = ['nickname', 'gender', 'birth_date', 'avatar']