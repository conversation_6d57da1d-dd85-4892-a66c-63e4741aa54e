"""
微信认证测试
"""
import hashlib
from django.test import TestCase
from unittest.mock import patch, Mock
from apps.wechat.auth import WechatAuth, WechatSignature
from apps.wechat.models import WechatConfig, WechatUser
from django.contrib.auth import get_user_model

User = get_user_model()


class WechatSignatureTest(TestCase):
    """微信签名验证测试"""
    
    def test_check_signature_success(self):
        """测试签名验证成功"""
        token = 'test_token'
        timestamp = '1234567890'
        nonce = 'test_nonce'
        
        # 计算正确的签名
        tmp_list = [token, timestamp, nonce]
        tmp_list.sort()
        tmp_str = ''.join(tmp_list)
        signature = hashlib.sha1(tmp_str.encode()).hexdigest()
        
        result = WechatSignature.check_signature(signature, timestamp, nonce, token)
        self.assertTrue(result)
    
    def test_check_signature_fail(self):
        """测试签名验证失败"""
        result = WechatSignature.check_signature('wrong_signature', '123', 'nonce', 'token')
        self.assertFalse(result)


class WechatAuthTest(TestCase):
    """微信认证测试"""
    
    def setUp(self):
        # 创建微信配置
        WechatConfig.objects.create(
            platform='official',
            name='default',
            app_id='test_app_id',
            app_secret='test_app_secret'
        )
        
        self.wechat_auth = WechatAuth()
    
    @patch('apps.wechat.auth.requests.get')
    def test_get_access_token_success(self, mock_get):
        """测试获取access_token成功"""
        mock_response = Mock()
        mock_response.json.return_value = {
            'access_token': 'test_access_token',
            'expires_in': 7200
        }
        mock_get.return_value = mock_response
        
        token = self.wechat_auth.get_access_token()
        
        self.assertEqual(token, 'test_access_token')
        mock_get.assert_called_once()
    
    @patch('apps.wechat.auth.requests.get')
    def test_get_access_token_error(self, mock_get):
        """测试获取access_token失败"""
        mock_response = Mock()
        mock_response.json.return_value = {
            'errcode': 40013,
            'errmsg': 'invalid appid'
        }
        mock_get.return_value = mock_response
        
        token = self.wechat_auth.get_access_token()
        
        self.assertIsNone(token)
    
    @patch('apps.wechat.auth.requests.get')
    def test_get_user_info_success(self, mock_get):
        """测试获取用户信息成功"""
        mock_response = Mock()
        mock_response.json.return_value = {
            'openid': 'test_openid',
            'nickname': '测试用户',
            'sex': 1,
            'city': '北京',
            'province': '北京',
            'country': '中国',
            'headimgurl': 'http://example.com/avatar.jpg',
            'subscribe': 1
        }
        mock_get.return_value = mock_response
        
        with patch.object(self.wechat_auth, 'get_access_token', return_value='test_token'):
            user_info = self.wechat_auth.get_user_info('test_openid')
        
        self.assertEqual(user_info['openid'], 'test_openid')
        self.assertEqual(user_info['nickname'], '测试用户')
    
    @patch('apps.wechat.auth.requests.get')
    def test_oauth_get_user_info(self, mock_get):
        """测试OAuth获取用户信息"""
        # 模拟获取access_token
        mock_response1 = Mock()
        mock_response1.json.return_value = {
            'access_token': 'oauth_access_token',
            'expires_in': 7200,
            'refresh_token': 'refresh_token',
            'openid': 'test_openid',
            'scope': 'snsapi_userinfo'
        }
        
        # 模拟获取用户信息
        mock_response2 = Mock()
        mock_response2.json.return_value = {
            'openid': 'test_openid',
            'nickname': '测试用户',
            'sex': 1,
            'city': '北京',
            'province': '北京',
            'country': '中国',
            'headimgurl': 'http://example.com/avatar.jpg'
        }
        
        mock_get.side_effect = [mock_response1, mock_response2]
        
        user_info = self.wechat_auth.oauth_get_user_info('test_code')
        
        self.assertEqual(user_info['openid'], 'test_openid')
        self.assertEqual(user_info['nickname'], '测试用户')
        self.assertEqual(mock_get.call_count, 2)
    
    def test_create_or_update_user(self):
        """测试创建或更新用户"""
        user_info = {
            'openid': 'test_openid',
            'nickname': '测试用户',
            'sex': 1,
            'city': '北京',
            'province': '北京',
            'country': '中国',
            'headimgurl': 'http://example.com/avatar.jpg',
            'subscribe': 1
        }
        
        # 第一次创建用户
        wechat_user = self.wechat_auth.create_or_update_user(user_info)
        
        self.assertEqual(wechat_user.openid, 'test_openid')
        self.assertEqual(wechat_user.nickname, '测试用户')
        self.assertEqual(wechat_user.gender, 'male')
        self.assertTrue(wechat_user.subscribe)
        
        # 更新用户信息
        user_info['nickname'] = '更新用户'
        user_info['city'] = '上海'
        
        updated_user = self.wechat_auth.create_or_update_user(user_info)
        
        self.assertEqual(updated_user.id, wechat_user.id)  # 同一个用户
        self.assertEqual(updated_user.nickname, '更新用户')
        self.assertEqual(updated_user.city, '上海')
    
    def test_create_or_update_user_with_existing_django_user(self):
        """测试使用已存在的Django用户创建微信用户"""
        # 先创建Django用户
        django_user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>'
        )
        
        user_info = {
            'openid': 'test_openid',
            'nickname': '测试用户',
            'sex': 1,
            'city': '北京',
            'province': '北京',
            'country': '中国',
            'headimgurl': 'http://example.com/avatar.jpg',
            'subscribe': 1
        }
        
        # 创建微信用户时关联已存在的Django用户
        with patch('apps.wechat.auth.User.objects.create_user') as mock_create:
            mock_create.return_value = django_user
            wechat_user = self.wechat_auth.create_or_update_user(user_info)
        
        self.assertEqual(wechat_user.user, django_user)
        self.assertEqual(wechat_user.openid, 'test_openid')
    
    def test_gender_mapping(self):
        """测试性别映射"""
        # 测试男性
        user_info_male = {
            'openid': 'test_openid_male',
            'nickname': '男性用户',
            'sex': 1,
            'subscribe': 1
        }
        
        wechat_user_male = self.wechat_auth.create_or_update_user(user_info_male)
        self.assertEqual(wechat_user_male.gender, 'male')
        
        # 测试女性
        user_info_female = {
            'openid': 'test_openid_female',
            'nickname': '女性用户',
            'sex': 2,
            'subscribe': 1
        }
        
        wechat_user_female = self.wechat_auth.create_or_update_user(user_info_female)
        self.assertEqual(wechat_user_female.gender, 'female')
        
        # 测试未知性别
        user_info_unknown = {
            'openid': 'test_openid_unknown',
            'nickname': '未知用户',
            'sex': 0,
            'subscribe': 1
        }
        
        wechat_user_unknown = self.wechat_auth.create_or_update_user(user_info_unknown)
        self.assertEqual(wechat_user_unknown.gender, 'unknown')
    
    def test_subscribe_status(self):
        """测试关注状态"""
        # 测试已关注用户
        user_info_subscribed = {
            'openid': 'test_openid_sub',
            'nickname': '关注用户',
            'subscribe': 1
        }
        
        wechat_user_sub = self.wechat_auth.create_or_update_user(user_info_subscribed)
        self.assertTrue(wechat_user_sub.subscribe)
        
        # 测试未关注用户
        user_info_unsubscribed = {
            'openid': 'test_openid_unsub',
            'nickname': '未关注用户',
            'subscribe': 0
        }
        
        wechat_user_unsub = self.wechat_auth.create_or_update_user(user_info_unsubscribed)
        self.assertFalse(wechat_user_unsub.subscribe)
