from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import OrderViewSet, OrderItemViewSet, PaymentViewSet, OrderLogViewSet

app_name = 'orders'

# 创建路由器
router = DefaultRouter()
router.register(r'orders', OrderViewSet, basename='order')
router.register(r'items', OrderItemViewSet, basename='orderitem')
router.register(r'payments', PaymentViewSet, basename='payment')
router.register(r'logs', OrderLogViewSet, basename='orderlog')

urlpatterns = [
    path('', include(router.urls)),
] 