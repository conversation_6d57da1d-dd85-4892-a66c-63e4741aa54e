"""
微信模块模型测试
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from apps.wechat.models import WechatUser, WechatMessage, WechatMenu, WechatTemplate

User = get_user_model()


class WechatUserModelTest(TestCase):
    """微信用户模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.wechat_user = WechatUser.objects.create(
            user=self.user,
            openid='test_openid_123',
            unionid='test_unionid_123',
            nickname='测试用户',
            gender=1,
            city='深圳',
            province='广东',
            country='中国'
        )
    
    def test_wechat_user_creation(self):
        """测试微信用户创建"""
        self.assertEqual(self.wechat_user.openid, 'test_openid_123')
        self.assertEqual(self.wechat_user.unionid, 'test_unionid_123')
        self.assertEqual(self.wechat_user.nickname, '测试用户')
        self.assertEqual(self.wechat_user.gender, 1)
        self.assertEqual(self.wechat_user.city, '深圳')
        self.assertEqual(self.wechat_user.province, '广东')
        self.assertEqual(self.wechat_user.country, '中国')
        self.assertFalse(self.wechat_user.subscribe)
    
    def test_wechat_user_str_representation(self):
        """测试微信用户字符串表示"""
        expected = f"{self.wechat_user.nickname} - {self.user.username}"
        self.assertEqual(str(self.wechat_user), expected)
    
    def test_wechat_user_unique_openid(self):
        """测试微信用户OpenID唯一性"""
        with self.assertRaises(Exception):
            WechatUser.objects.create(
                user=self.user,
                openid='test_openid_123',  # 重复的openid
                nickname='另一个用户'
            )


class WechatMessageModelTest(TestCase):
    """微信消息模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.wechat_user = WechatUser.objects.create(
            user=self.user,
            openid='test_openid_123',
            nickname='测试用户'
        )
        
        self.message = WechatMessage.objects.create(
            wechat_user=self.wechat_user,
            msg_id='test_msg_123',
            msg_type='text',
            direction='in',
            content='这是一条测试消息'
        )
    
    def test_message_creation(self):
        """测试消息创建"""
        self.assertEqual(self.message.wechat_user, self.wechat_user)
        self.assertEqual(self.message.msg_id, 'test_msg_123')
        self.assertEqual(self.message.msg_type, 'text')
        self.assertEqual(self.message.direction, 'in')
        self.assertEqual(self.message.content, '这是一条测试消息')
    
    def test_message_str_representation(self):
        """测试消息字符串表示"""
        expected = f"{self.wechat_user.nickname} - 文本消息 - {self.message.created_at}"
        self.assertEqual(str(self.message), expected)


class WechatMenuModelTest(TestCase):
    """微信菜单模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.parent_menu = WechatMenu.objects.create(
            name='主菜单',
            sort_order=1
        )
        
        self.child_menu = WechatMenu.objects.create(
            name='子菜单',
            type='click',
            key='MENU_CLICK',
            parent=self.parent_menu,
            sort_order=1
        )
    
    def test_menu_creation(self):
        """测试菜单创建"""
        self.assertEqual(self.parent_menu.name, '主菜单')
        self.assertEqual(self.parent_menu.sort_order, 1)
        self.assertTrue(self.parent_menu.is_active)
        
        self.assertEqual(self.child_menu.name, '子菜单')
        self.assertEqual(self.child_menu.type, 'click')
        self.assertEqual(self.child_menu.key, 'MENU_CLICK')
        self.assertEqual(self.child_menu.parent, self.parent_menu)
    
    def test_menu_hierarchy(self):
        """测试菜单层级关系"""
        self.assertEqual(self.child_menu.parent, self.parent_menu)
        self.assertIn(self.child_menu, self.parent_menu.children.all())
    
    def test_menu_str_representation(self):
        """测试菜单字符串表示"""
        self.assertEqual(str(self.parent_menu), '主菜单')
        self.assertEqual(str(self.child_menu), '子菜单')


class WechatTemplateModelTest(TestCase):
    """微信模板消息模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.template = WechatTemplate.objects.create(
            template_id='test_template_123',
            title='订单通知',
            primary_industry='IT科技',
            deputy_industry='互联网|电子商务',
            content='您的订单{{order_no.DATA}}已确认，金额{{amount.DATA}}',
            example='您的订单12345已确认，金额100.00元'
        )
    
    def test_template_creation(self):
        """测试模板创建"""
        self.assertEqual(self.template.template_id, 'test_template_123')
        self.assertEqual(self.template.title, '订单通知')
        self.assertEqual(self.template.primary_industry, 'IT科技')
        self.assertEqual(self.template.deputy_industry, '互联网|电子商务')
        self.assertTrue(self.template.is_active)
    
    def test_template_str_representation(self):
        """测试模板字符串表示"""
        self.assertEqual(str(self.template), '订单通知')
    
    def test_template_unique_template_id(self):
        """测试模板ID唯一性"""
        with self.assertRaises(Exception):
            WechatTemplate.objects.create(
                template_id='test_template_123',  # 重复的template_id
                title='另一个模板'
            )
