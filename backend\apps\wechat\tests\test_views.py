"""
微信视图测试
"""
import json
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch, Mock

from apps.wechat.models import WechatUser, WechatConfig, WechatMessage, WechatMenu

User = get_user_model()


class WechatConfigViewSetTest(APITestCase):
    """微信配置视图集测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # 创建权限组并分配给用户
        config_group, _ = Group.objects.get_or_create(name='微信管理员')
        self.user.groups.add(config_group)

        self.client.force_authenticate(user=self.user)
        
        # 创建测试配置
        self.config = WechatConfig.objects.create(
            platform='official',
            name='test',
            app_id='test_app_id',
            app_secret='test_app_secret',
            token='test_token'
        )
    
    def test_list_configs(self):
        """测试获取配置列表"""
        url = reverse('wechat:wechat-configs-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
    
    def test_get_platform_configs(self):
        """测试获取平台配置"""
        url = reverse('wechat:wechat-configs-platforms')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('official', response.data['data'])
    
    def test_get_specific_platform_config(self):
        """测试获取指定平台配置"""
        url = reverse('wechat:wechat-configs-platform-config')
        response = self.client.get(url, {'platform': 'official', 'name': 'test'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['app_id'], 'test_app_id')


class WechatPaymentViewSetTest(APITestCase):
    """微信支付视图集测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # 创建权限组并分配给用户
        payment_group, _ = Group.objects.get_or_create(name='支付管理员')
        self.user.groups.add(payment_group)

        self.client.force_authenticate(user=self.user)
        
        # 创建支付配置
        WechatConfig.objects.create(
            platform='payment',
            name='default',
            app_id='test_app_id',
            mch_id='test_mch_id',
            mch_key='test_mch_key'
        )
    
    @patch('apps.wechat.payment.WechatPayment.unified_order')
    def test_create_order(self, mock_unified_order):
        """测试创建支付订单"""
        mock_unified_order.return_value = {
            'return_code': 'SUCCESS',
            'prepay_id': 'test_prepay_id'
        }
        
        url = reverse('wechat:wechat-payment-create-order')
        data = {
            'out_trade_no': 'test_order_123',
            'total_fee': 100,
            'body': '测试商品',
            'trade_type': 'JSAPI',
            'openid': 'test_openid'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        mock_unified_order.assert_called_once()
    
    def test_create_order_missing_params(self):
        """测试创建订单缺少参数"""
        url = reverse('wechat:wechat-payment-create-order')
        data = {
            'out_trade_no': 'test_order_123',
            # 缺少 total_fee 和 body
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
    
    @patch('apps.wechat.payment.WechatPayment.query_order')
    def test_query_order(self, mock_query_order):
        """测试查询订单"""
        mock_query_order.return_value = {
            'return_code': 'SUCCESS',
            'trade_state': 'SUCCESS'
        }
        
        url = reverse('wechat:wechat-payment-query-order')
        data = {'out_trade_no': 'test_order_123'}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        mock_query_order.assert_called_once()


class WechatStatsViewSetTest(APITestCase):
    """微信统计视图集测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # 创建权限组并分配给用户
        stats_group, _ = Group.objects.get_or_create(name='统计查看员')
        self.user.groups.add(stats_group)

        self.client.force_authenticate(user=self.user)
        
        # 创建测试数据
        test_user = User.objects.create_user(
            username='wechat_user',
            email='<EMAIL>'
        )
        
        self.wechat_user = WechatUser.objects.create(
            user=test_user,
            openid='test_openid',
            nickname='测试用户',
            subscribe=True
        )
        
        WechatMessage.objects.create(
            wechat_user=self.wechat_user,
            msg_type='text',
            content='测试消息'
        )
    
    def test_user_stats(self):
        """测试用户统计"""
        url = reverse('wechat:wechat-stats-user-stats')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['total_users'], 1)
        self.assertEqual(response.data['data']['subscribed_users'], 1)
    
    def test_message_stats(self):
        """测试消息统计"""
        url = reverse('wechat:wechat-stats-message-stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['total_messages'], 1)


class WechatOfficialCallbackTest(TestCase):
    """微信公众号回调测试"""
    
    def setUp(self):
        # 创建公众号配置
        WechatConfig.objects.create(
            platform='official',
            name='default',
            app_id='test_app_id',
            app_secret='test_app_secret',
            token='test_token'
        )
    
    def test_verify_callback(self):
        """测试验证回调"""
        url = reverse('wechat:official-verify')
        params = {
            'signature': 'test_signature',
            'timestamp': '1234567890',
            'nonce': 'test_nonce',
            'echostr': 'test_echo'
        }
        
        with patch('apps.wechat.auth.WechatSignature.check_signature') as mock_check:
            mock_check.return_value = True
            response = self.client.get(url, params)
            
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.content.decode(), 'test_echo')
    
    @patch('apps.wechat.official.WechatOfficial.parse_message')
    @patch('apps.wechat.official.WechatOfficial.handle_message')
    def test_message_callback(self, mock_handle, mock_parse):
        """测试消息回调"""
        mock_parse.return_value = {
            'ToUserName': 'test_to_user',
            'FromUserName': 'test_from_user',
            'CreateTime': '1234567890',
            'MsgType': 'text',
            'Content': '测试消息'
        }
        mock_handle.return_value = '感谢您的消息！'
        
        url = reverse('wechat:official-callback')
        xml_data = '''<xml>
            <ToUserName><![CDATA[test_to_user]]></ToUserName>
            <FromUserName><![CDATA[test_from_user]]></FromUserName>
            <CreateTime>1234567890</CreateTime>
            <MsgType><![CDATA[text]]></MsgType>
            <Content><![CDATA[测试消息]]></Content>
        </xml>'''
        
        with patch('apps.wechat.auth.WechatSignature.check_signature') as mock_check:
            mock_check.return_value = True
            response = self.client.post(
                url + '?signature=test&timestamp=123&nonce=test',
                data=xml_data,
                content_type='application/xml'
            )
            
            self.assertEqual(response.status_code, 200)
            mock_parse.assert_called_once()
            mock_handle.assert_called_once()


class WechatMiniprogramTest(APITestCase):
    """微信小程序测试"""
    
    def setUp(self):
        # 创建小程序配置
        WechatConfig.objects.create(
            platform='miniprogram',
            name='default',
            app_id='test_app_id',
            app_secret='test_app_secret'
        )
    
    @patch('apps.wechat.miniprogram.WechatMiniprogram.code2session')
    def test_miniprogram_auth(self, mock_code2session):
        """测试小程序授权"""
        mock_code2session.return_value = {
            'openid': 'test_openid',
            'session_key': 'test_session_key'
        }
        
        url = reverse('wechat:miniprogram-auth')
        data = {'code': 'test_code'}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mock_code2session.assert_called_once_with('test_code')
