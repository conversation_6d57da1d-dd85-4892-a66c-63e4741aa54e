<view id="{{o}}" change:eS="{{uV.sS}}" eS="{{$eS[o]}}" change:eA="{{uV.sA}}" eA="{{$eA[o]}}" class="{{['container', 'data-v-00a60067', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}"><view class="header data-v-00a60067"><view class="search-bar data-v-00a60067" bindtap="{{b}}"><icon class="data-v-00a60067" virtualHostClass="data-v-00a60067" u-i="00a60067-0" bind:__l="__l" u-p="{{a||''}}"/><text class="search-placeholder data-v-00a60067">搜索商品</text></view><view class="scan-btn data-v-00a60067" bindtap="{{d}}"><icon class="data-v-00a60067" virtualHostClass="data-v-00a60067" u-i="00a60067-1" bind:__l="__l" u-p="{{c||''}}"/></view></view><view class="banner-section data-v-00a60067"><swiper class="banner-swiper data-v-00a60067" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}"><swiper-item wx:for="{{e}}" wx:for-item="banner" wx:key="c" class="data-v-00a60067"><image class="banner-image data-v-00a60067" src="{{banner.a}}" mode="aspectFill" bindtap="{{banner.b}}"/></swiper-item></swiper></view><view class="nav-section data-v-00a60067"><view class="nav-grid data-v-00a60067"><view wx:for="{{f}}" wx:for-item="nav" wx:key="d" class="nav-item data-v-00a60067" bindtap="{{nav.e}}"><view class="nav-icon data-v-00a60067"><icon class="data-v-00a60067" virtualHostClass="data-v-00a60067" u-i="{{nav.a}}" bind:__l="__l" u-p="{{nav.b||''}}"/></view><text class="nav-text data-v-00a60067">{{nav.c}}</text></view></view></view><view class="category-section data-v-00a60067"><view class="section-header data-v-00a60067"><text class="section-title data-v-00a60067">商品分类</text><view class="more-btn data-v-00a60067" bindtap="{{h}}"><text class="more-text data-v-00a60067">更多</text><icon class="data-v-00a60067" virtualHostClass="data-v-00a60067" u-i="00a60067-3" bind:__l="__l" u-p="{{g||''}}"/></view></view><scroll-view class="category-scroll data-v-00a60067" scroll-x="true" show-scrollbar="false" enable-flex="true" enhanced="true"><view class="category-list data-v-00a60067"><view wx:for="{{i}}" wx:for-item="category" wx:key="c" class="category-item data-v-00a60067" bindtap="{{category.d}}"><image class="category-image data-v-00a60067" src="{{category.a}}" mode="aspectFill"/><text class="category-name data-v-00a60067">{{category.b}}</text></view></view></scroll-view></view><view class="product-section data-v-00a60067"><view class="section-header data-v-00a60067"><text class="section-title data-v-00a60067">推荐商品</text><view class="more-btn data-v-00a60067" bindtap="{{k}}"><text class="more-text data-v-00a60067">更多</text><icon class="data-v-00a60067" virtualHostClass="data-v-00a60067" u-i="00a60067-4" bind:__l="__l" u-p="{{j||''}}"/></view></view><view class="product-grid data-v-00a60067"><view wx:for="{{l}}" wx:for-item="product" wx:key="h" class="product-item data-v-00a60067" bindtap="{{product.i}}"><image class="product-image data-v-00a60067" src="{{product.a}}" mode="aspectFill"/><view class="product-info data-v-00a60067"><text class="product-name data-v-00a60067">{{product.b}}</text><view class="product-price data-v-00a60067"><text class="price-current data-v-00a60067">￥{{product.c}}</text><text wx:if="{{product.d}}" class="price-original data-v-00a60067">￥{{product.e}}</text></view><view class="product-tags data-v-00a60067"><text wx:if="{{product.f}}" class="tag hot data-v-00a60067">热销</text><text wx:if="{{product.g}}" class="tag new data-v-00a60067">新品</text></view></view></view></view></view><view wx:if="{{m}}" class="load-more data-v-00a60067"><text wx:if="{{n}}" class="load-text data-v-00a60067">上拉加载更多</text><text wx:else class="load-text data-v-00a60067">加载中...</text></view></view><wxs src="/common/uniView.wxs" module="uV"/>
