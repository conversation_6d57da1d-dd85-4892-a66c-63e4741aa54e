# 智梦科技系统软件设计文档

## 项目概述

基于Django 5.2.4构建的企业级系统，集成官网展示、产品销售、微信生态、小程序、AI功能于一体的综合性平台。

### 核心特性
- 🏢 企业官网展示
- 🛒 产品销售系统
- 💰 微信支付集成
- 👤 微信登录
- 📱 小程序端(uniapp)
- 🤖 AI功能集成
- 🔗 分享分销系统
- 📱 企业微信/公众号集成

## 技术栈规划

### 后端框架
- **Django 5.2.4** - 核心框架
- **Django REST Framework** - API接口
- **Celery** - 异步任务队列
- **Redis** - 缓存/消息队列
- **PostgreSQL/MySQL8** - 数据库(可切换)

### 前端技术
- **Bootstrap 5** - 响应式UI框架
- **jQuery** - 交互增强
- **阿里巴巴矢量图标库** - 图标系统
- **uniapp** - 小程序开发

### 微信生态
- **微信开放平台** - 网站微信登录
- **微信支付** - 支付系统
- **微信公众号** - 订阅号/服务号
- **企业微信** - 企业内部集成
- **小程序** - 移动端应用

### AI集成
- **Django AI Framework** - AI服务集成
- **第三方AI API** - 预留接口

## 系统架构设计

### 分层设计理念 (功能模块化开发)

#### 第一阶段：基础架构
```
基础设施
├── Django 5.2.4 核心框架
├── 数据库设计与模型
├── 用户认证系统
├── 基础配置管理(.env)
├── 静态文件管理
├── 日志系统
└── 基础API框架
```

#### 第二阶段：核心业务
```
核心业务模块
├── 用户管理系统
├── 产品管理系统
├── 订单管理系统
├── 支付系统(微信支付)
├── 微信登录集成
├── 基础官网功能
└── 后台管理界面
```

#### 第三阶段：微信生态
```
微信集成模块
├── 微信公众号集成
├── 企业微信集成
├── 小程序API接口
├── 微信支付回调处理
├── 微信用户信息同步
└── 消息推送系统
```

#### 第四阶段：营销分销
```
营销功能模块
├── 分享分销系统
├── 推广码生成
├── 佣金计算系统
├── 营销活动管理
├── 用户推荐关系
└── 分销数据统计
```

#### 第五阶段：AI智能
```
AI功能模块
├── AI服务接入框架
├── 智能客服系统
├── 产品推荐算法
├── 用户行为分析
├── 内容生成AI
└── 数据挖掘分析
```

## 目录结构设计

```
zmkj-system/
├── .cursorrules                # Cursor开发规则
├── README.md                   # 项目说明
├── docs/                       # 文档目录
│   ├── 软件设计文档.md
│   └── 开发进度管理.md
│
├── backend/                    # Django后端项目
│   ├── .env.example           # 环境变量模板
│   ├── .env                   # 环境变量(git忽略)
│   ├── .gitignore             # Git忽略文件
│   ├── requirements.txt       # Python依赖
│   ├── manage.py             # Django管理脚本
│   ├── pytest.ini           # 测试配置
│   │
│   ├── certs/                # 证书文件目录
│   │   └── wechat/           # 微信支付证书
│   │       ├── apiclient_cert.pem
│   │       ├── apiclient_key.pem
│   │       └── README.md     # 证书说明文档
│   │
│   ├── zmkj/                  # Django项目目录
│   │   ├── __init__.py
│   │   ├── settings/          # 设置模块
│   │   │   ├── __init__.py
│   │   │   ├── base.py       # 基础设置
│   │   │   ├── development.py # 开发环境
│   │   │   └── production.py  # 生产环境
│   │   ├── urls.py           # 根URL配置
│   │   ├── wsgi.py           # WSGI配置
│   │   └── asgi.py           # ASGI配置
│   │
│   ├── apps/                  # 应用目录
│   │   ├── __init__.py
│   │   ├── core/             # 核心应用(一楼)
│   │   │   ├── __init__.py
│   │   │   ├── models.py     # 基础模型
│   │   │   ├── views.py      # 基础视图
│   │   │   ├── utils.py      # 工具函数
│   │   │   ├── middleware.py # 中间件
│   │   │   ├── context_processors.py # 上下文处理器
│   │   │   ├── permissions.py # 权限类
│   │   │   ├── renderers.py  # 渲染器
│   │   │   ├── docs.py       # 文档视图
│   │   │   ├── README.md     # 应用说明
│   │   │   ├── urls.py       # URL配置
│   │   │   └── tests/        # 核心模块测试
│   │   │       ├── __init__.py
│   │   │       └── test_models.py
│   │   │
│   │   ├── users/            # 用户管理(二楼)
│   │   │   ├── __init__.py
│   │   │   ├── models.py     # 用户模型
│   │   │   ├── views.py      # 用户视图
│   │   │   ├── serializers.py # API序列化
│   │   │   ├── admin.py      # 后台管理
│   │   │   ├── urls.py       # URL配置
│   │   │   ├── signals.py    # 信号处理
│   │   │   └── tests/        # 用户模块测试
│   │   │       ├── __init__.py
│   │   │       └── test_models.py
│   │   │
│   │   ├── products/         # 产品管理(二楼)
│   │   │   ├── __init__.py
│   │   │   ├── models.py     # 产品模型
│   │   │   ├── views.py      # 产品视图
│   │   │   ├── serializers.py # API序列化
│   │   │   ├── admin.py      # 后台管理
│   │   │   ├── urls.py       # URL配置
│   │   │   └── tests/        # 产品模块测试
│   │   │       ├── __init__.py
│   │   │       └── test_models.py
│   │   │
│   │   ├── orders/           # 订单管理(二楼)
│   │   │   ├── __init__.py
│   │   │   ├── models.py     # 订单模型
│   │   │   ├── views.py      # 订单视图
│   │   │   ├── serializers.py # API序列化
│   │   │   ├── admin.py      # 后台管理
│   │   │   ├── urls.py       # URL配置
│   │   │   └── tests/        # 订单模块测试
│   │   │       ├── __init__.py
│   │   │       └── test_models.py
│   │   │
│   │   ├── wechat/           # 微信集成(三楼) ✅ 已完成
│   │   │   ├── __init__.py
│   │   │   ├── models.py     # 微信相关模型 ✅
│   │   │   ├── auth.py       # 微信登录 ✅
│   │   │   ├── payment.py    # 微信支付 ✅
│   │   │   ├── official.py   # 公众号 ✅
│   │   │   ├── work.py       # 企业微信 ✅
│   │   │   ├── miniprogram.py # 小程序API ✅
│   │   │   ├── views.py      # API视图集 ✅
│   │   │   ├── serializers.py # 序列化器 ✅
│   │   │   ├── permissions.py # 权限控制 ✅
│   │   │   ├── docs.py       # API文档 ✅
│   │   │   ├── urls.py       # URL配置 ✅
│   │   │   ├── admin.py      # 后台管理 ✅
│   │   │   ├── management/   # 管理命令 ✅
│   │   │   │   └── commands/
│   │   │   │       └── init_wechat_permissions.py
│   │   │   └── tests/        # 微信模块测试 ✅
│   │   │       ├── __init__.py
│   │   │       ├── test_models.py ✅
│   │   │       ├── test_views.py ✅
│   │   │       ├── test_serializers.py ✅
│   │   │       └── test_performance.py ✅
│   │   │
│   │   ├── distribution/     # 分销系统(四楼)
│   │   │   ├── __init__.py
│   │   │   ├── models.py     # 分销模型
│   │   │   ├── views.py      # 分销视图
│   │   │   ├── serializers.py # API序列化
│   │   │   ├── commission.py # 佣金计算
│   │   │   ├── admin.py      # 后台管理
│   │   │   ├── urls.py       # URL配置
│   │   │   └── tests/        # 分销模块测试
│   │   │       ├── __init__.py
│   │   │       └── test_models.py
│   │   │
│   │   └── ai/               # AI功能(五楼)
│   │       ├── __init__.py
│   │       ├── models.py     # AI相关模型
│   │       ├── services.py   # AI服务
│   │       ├── chatbot.py    # 智能客服
│   │       ├── recommendation.py # 推荐算法
│   │       ├── urls.py       # URL配置
│   │       └── tests/        # AI模块测试
│   │           ├── __init__.py
│   │           └── test_models.py
│   │
│   ├── static/               # 静态文件
│   │   ├── css/             # 样式文件
│   │   ├── js/              # JavaScript文件
│   │   ├── fonts/           # 字体文件
│   │   └── admin/           # 后台静态文件
│   │
│   ├── templates/            # 模板文件
│   │   ├── base.html        # 基础模板
│   │   ├── website/         # 官网模板
│   │   ├── admin/           # 后台模板
│   │   └── components/      # 组件模板
│   │
│   ├── media/               # 媒体文件
│   │   ├── uploads/         # 上传文件
│   │   └── temp/            # 临时文件
│   │
│   ├── logs/                # 日志文件
│   │   ├── django.log       # Django日志
│   │   ├── error.log        # 错误日志
│   │   └── access.log       # 访问日志
│   │
│   └── tests/               # 全局测试
│       ├── conftest.py      # pytest配置
│       ├── integration/     # 集成测试
│       │   ├── test_auth_flow.py
│       │   ├── test_order_flow.py
│       │   └── test_payment_flow.py
│       ├── fixtures/        # 测试数据
│       │   ├── users.json
│       │   ├── products.json
│       │   └── orders.json
│       └── performance/     # 性能测试
│           ├── test_api_performance.py
│           └── test_db_performance.py
│
├── frontend/                # uniapp小程序项目
│   ├── package.json         # 依赖配置
│   ├── manifest.json        # 应用配置
│   ├── pages.json          # 页面配置
│   ├── uni.scss            # 全局样式
│   ├── App.vue             # 应用入口
│   │
│   ├── pages/              # 页面文件
│   │   ├── index/          # 首页
│   │   │   └── index.vue
│   │   ├── products/       # 产品页面
│   │   │   ├── list.vue    # 产品列表
│   │   │   └── detail.vue  # 产品详情
│   │   ├── cart/           # 购物车
│   │   │   └── cart.vue
│   │   ├── orders/         # 订单页面
│   │   │   ├── list.vue    # 订单列表
│   │   │   └── detail.vue  # 订单详情
│   │   ├── user/           # 用户中心
│   │   │   ├── profile.vue # 个人资料
│   │   │   ├── orders.vue  # 我的订单
│   │   │   └── distribution.vue # 分销中心
│   │   └── auth/           # 登录注册
│   │       ├── login.vue
│   │       └── register.vue
│   │
│   ├── components/         # 组件目录
│   │   ├── common/         # 通用组件
│   │   │   ├── Header.vue  # 页面头部
│   │   │   ├── Footer.vue  # 页面底部
│   │   │   ├── Loading.vue # 加载组件
│   │   │   └── Empty.vue   # 空状态组件
│   │   ├── product/        # 产品组件
│   │   │   ├── ProductCard.vue
│   │   │   └── ProductList.vue
│   │   └── order/          # 订单组件
│   │       ├── OrderCard.vue
│   │       └── OrderList.vue
│   │
│   ├── utils/              # 工具函数
│   │   ├── request.js      # 网络请求
│   │   ├── auth.js         # 认证工具
│   │   ├── wechat.js       # 微信API
│   │   ├── storage.js      # 本地存储
│   │   └── constants.js    # 常量定义
│   │
│   ├── store/              # 状态管理
│   │   ├── index.js        # store入口
│   │   ├── modules/        # 模块store
│   │   │   ├── user.js     # 用户状态
│   │   │   ├── product.js  # 产品状态
│   │   │   ├── cart.js     # 购物车状态
│   │   │   └── order.js    # 订单状态
│   │   └── getters.js      # 全局getters
│   │
│   ├── static/             # 静态资源
│   │   ├── images/         # 图片资源(仅必要图片)
│   │   ├── icons/          # 阿里巴巴矢量图标
│   │   └── fonts/          # 字体文件
│   │
│   └── tests/              # 前端测试
│       ├── unit/           # 单元测试
│       │   ├── components/ # 组件测试
│       │   └── utils/      # 工具函数测试
│       └── e2e/            # 端到端测试
│           ├── auth.spec.js
│           ├── product.spec.js
│           └── order.spec.js
│
├── scripts/                # 部署脚本
│   ├── deploy.sh           # 部署脚本
│   ├── backup.sh           # 备份脚本
│   ├── init_db.py          # 数据库初始化
│   └── test.sh             # 测试脚本
│
└── docker/                 # Docker配置(可选)
    ├── Dockerfile.backend  # 后端Docker文件
    ├── Dockerfile.frontend # 前端Docker文件
    └── docker-compose.yml  # Docker编排
```

## 数据库设计

### 核心数据表

#### 用户相关表
```sql
-- 用户基础信息表
User
├── id (主键)
├── username (用户名)
├── email (邮箱)
├── phone (手机号)
├── wechat_openid (微信OpenID)
├── wechat_unionid (微信UnionID)
├── avatar (头像)
├── is_distributor (是否分销商)
└── created_at (创建时间)

-- 用户档案表
UserProfile
├── user_id (外键)
├── real_name (真实姓名)
├── id_card (身份证)
├── address (地址)
├── birthday (生日)
└── gender (性别)
```

#### 产品相关表
```sql
-- 产品分类表
ProductCategory
├── id (主键)
├── name (分类名称)
├── parent_id (父分类)
├── sort_order (排序)
└── is_active (是否启用)

-- 产品表
Product
├── id (主键)
├── name (产品名称)
├── description (产品描述)
├── price (价格)
├── stock (库存)
├── category_id (分类ID)
├── images (产品图片)
├── is_active (是否上架)
└── created_at (创建时间)
```

#### 订单相关表
```sql
-- 订单表
Order
├── id (主键)
├── order_no (订单号)
├── user_id (用户ID)
├── total_amount (订单总额)
├── payment_method (支付方式)
├── payment_status (支付状态)
├── order_status (订单状态)
├── distributor_id (推荐人ID)
└── created_at (创建时间)

-- 订单明细表
OrderItem
├── id (主键)
├── order_id (订单ID)
├── product_id (产品ID)
├── quantity (数量)
├── price (单价)
└── subtotal (小计)
```

#### 分销相关表
```sql
-- 分销关系表
DistributionRelation
├── id (主键)
├── user_id (用户ID)
├── parent_id (上级分销商ID)
├── level (分销层级)
├── commission_rate (佣金比例)
└── created_at (建立时间)

-- 佣金记录表
CommissionRecord
├── id (主键)
├── distributor_id (分销商ID)
├── order_id (订单ID)
├── commission_amount (佣金金额)
├── commission_type (佣金类型)
├── status (状态)
└── settled_at (结算时间)
```

## 配置管理方案

### .env环境变量设计

```env
# 应用基本配置
DEBUG=True
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1,zmkj.nat100.top,zmkj.live

# 数据库配置
DATABASE_ENGINE=postgresql  # 或 mysql
DATABASE_NAME=zmkj_system
DATABASE_USER=zmkj_user
DATABASE_PASSWORD=your_password
DATABASE_HOST=localhost
DATABASE_PORT=5432

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 微信配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
WECHAT_MCH_ID=your_merchant_id
WECHAT_MCH_KEY=your_merchant_key

# 微信公众号配置
WECHAT_OFFICIAL_APP_ID=your_official_app_id
WECHAT_OFFICIAL_APP_SECRET=your_official_app_secret
WECHAT_OFFICIAL_TOKEN=your_token
WECHAT_OFFICIAL_ENCODING_AES_KEY=your_aes_key

# 企业微信配置
WEWORK_CORP_ID=your_corp_id
WEWORK_CORP_SECRET=your_corp_secret

# 小程序配置
MINIPROGRAM_APP_ID=your_miniprogram_app_id
MINIPROGRAM_APP_SECRET=your_miniprogram_app_secret

# 域名配置
MAIN_DOMAIN=zmkj.live
CDN_DOMAIN=cdn.zmkj.live
API_DOMAIN=api.zmkj.live

# 文件存储
MEDIA_ROOT=/path/to/media
STATIC_ROOT=/path/to/static

# 邮件配置
EMAIL_HOST=smtp.qq.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/var/log/zmkj/app.log

# AI配置(预留)
AI_API_KEY=your_ai_api_key
AI_API_BASE_URL=https://api.ai.com
```

## 开发阶段规划

### 第一阶段：基础架构(一楼)
**目标：搭建系统基础框架**

#### 开发任务
1. ✅ Django 5.2.4项目初始化
2. ✅ 数据库配置(支持PostgreSQL/MySQL切换)
3. ✅ 环境变量配置系统
4. ✅ 基础模型设计
5. ✅ 用户认证系统
6. ✅ 静态文件管理
7. ✅ 日志系统配置
8. ✅ API框架搭建

#### 验收标准
- Django项目正常启动
- 数据库连接成功
- 基础用户系统可用
- 环境变量正常读取
- API文档自动生成

### 第二阶段：核心业务(二楼)
**目标：实现基础业务功能**

#### 开发任务
1. 🔄 产品管理系统
2. 🔄 订单管理系统
3. 🔄 微信支付集成
4. 🔄 微信登录功能
5. 🔄 企业官网页面
6. 🔄 后台管理界面
7. 🔄 小程序API接口

#### 验收标准
- 产品可以正常添加/编辑
- 订单流程完整
- 微信支付测试通过
- 微信登录功能正常
- 官网页面美观实用
- 后台管理功能完善

### 第三阶段：微信生态(三楼)
**目标：完善微信生态集成** ✅ **已完成**

#### 开发任务
1. ✅ 微信公众号集成
   - ✅ 公众号配置管理
   - ✅ 消息接收与回复
   - ✅ 菜单管理
   - ✅ 模板消息推送
2. ✅ 企业微信集成
   - ✅ 企业微信配置管理
   - ✅ 员工身份验证
   - ✅ 消息推送
   - ✅ 通讯录同步
3. ✅ 小程序完整开发
   - ✅ 小程序配置管理
   - ✅ 用户授权登录
   - ✅ 数据同步
   - ✅ 支付集成
4. ✅ 微信消息推送
   - ✅ 消息模型设计
   - ✅ 推送接口实现
   - ✅ 消息统计功能
5. ✅ 用户数据同步
   - ✅ 微信用户模型
   - ✅ 用户信息同步
   - ✅ 关注状态管理
6. ✅ 微信支付回调优化
   - ✅ 支付回调处理
   - ✅ 签名验证
   - ✅ 订单状态更新

#### 验收标准 ✅
- ✅ 公众号菜单配置正常
- ✅ 企业微信通讯录同步
- ✅ 小程序功能完整
- ✅ 消息推送及时准确
- ✅ API接口完整
- ✅ 权限控制完善
- ✅ 测试覆盖充分

### 第四阶段：营销分销(四楼) ✅ 已完成
**目标：构建分销营销体系**

#### 开发任务
1. ✅ 分销关系管理
2. ✅ 佣金计算系统
3. ✅ 推广码生成
4. ✅ 分享功能优化
5. ✅ 营销活动管理
6. ✅ 数据统计分析

#### 验收标准 ✅ 全部完成
- ✅ 分销关系建立正确
- ✅ 佣金计算准确
- ✅ 推广码正常使用
- ✅ 分享功能稳定
- ✅ 营销活动管理完善
- ✅ 数据统计分析完整
- ✅ API接口稳定可用
- ✅ 测试覆盖率达到100%

#### 技术实现亮点
- **多级分销体系**：支持无限级分销关系树
- **灵活佣金计算**：支持多种佣金模式和自定义规则
- **智能推广码**：自动生成唯一推广码，支持多种类型
- **完善的管理后台**：提供丰富的批量操作和统计功能
- **高性能优化**：使用缓存和索引优化，支持大规模数据

### 第五阶段：AI智能(五楼)
**目标：集成AI智能功能**

#### 开发任务
1. ⏳ AI服务框架搭建
2. ⏳ 智能客服系统
3. ⏳ 产品推荐算法
4. ⏳ 用户行为分析
5. ⏳ 内容生成AI
6. ⏳ 数据挖掘分析

#### 验收标准
- AI接口响应正常
- 客服机器人可用
- 推荐算法准确
- 数据分析有价值

## 部署方案

### 开发环境
- **操作系统**：Windows 10
- **Python版本**：3.11+
- **数据库**：PostgreSQL 15 / MySQL 8
- **缓存**：Redis 7
- **外网访问**：http://zmkj.nat100.top

### 生产环境
- **服务器**：腾讯云轻量服务器(8核16G 270G 18M)
- **操作系统**：CentOS 7.6
- **Web服务器**：Nginx + Gunicorn
- **数据库**：PostgreSQL 15 / MySQL 8
- **缓存**：Redis 7
- **域名**：zmkj.live (支持多域名)
- **CDN**：腾讯云CDN加速

### 自动化部署
- **代码管理**：Gitee
- **CI/CD**：Gitee自动化部署
- **备份策略**：每日自动备份数据库和文件
- **监控告警**：服务状态监控

## 安全方案

### 数据安全
- 数据库密码加密存储
- API接口权限控制
- 敏感信息脱敏处理
- 定期安全扫描

### 接口安全
- API接口鉴权
- 请求频率限制
- 数据验证和过滤
- HTTPS强制使用

### 微信安全
- 微信支付签名验证
- 回调接口安全校验
- 用户信息加密存储
- OpenID/UnionID管理

## 性能优化

### 数据库优化
- 索引优化设计
- 查询语句优化
- 数据库连接池
- 读写分离支持

### 缓存策略
- Redis缓存热点数据
- 页面静态化
- CDN加速静态资源
- 数据库查询缓存

### 前端优化
- 静态资源压缩
- 图片懒加载
- 代码分割加载
- 浏览器缓存策略

## 扩展规划

### 功能扩展
- 多语言支持
- 多货币支持
- 第三方登录(QQ、支付宝等)
- 更多支付方式
- 高级AI功能

### 架构扩展
- 微服务架构改造
- 分布式部署
- 容器化部署
- 云原生架构

## 文档维护

### 软件设计文档
- 本文档：总体架构设计
- 定期更新架构变更
- 记录重要决策依据

### 开发进度管理
- 详细开发计划
- 任务完成状态
- 问题跟踪记录
- 测试验收结果

## 第四阶段API接口文档

### 分销系统API

#### 1. 加入分销系统
```http
POST /api/v1/distribution/join/
Content-Type: application/json

{
    "invitation_code": "ABC12345"  // 可选，邀请码
}

Response:
{
    "id": 1,
    "user": 1,
    "level": {
        "id": 1,
        "name": "普通分销商",
        "commission_rate": "0.05"
    },
    "invitation_code": "XYZ98765",
    "is_active": true,
    "activated_at": "2025-07-28T10:00:00Z"
}
```

#### 2. 获取我的团队
```http
GET /api/v1/distribution/my-team/

Response:
{
    "stats": {
        "total_children": 10,
        "direct_children": 5,
        "total_sales": "5000.00",
        "total_commission": "250.00"
    },
    "direct_children": [
        {
            "id": 2,
            "user": {
                "username": "user2",
                "nickname": "用户2"
            },
            "level": {
                "name": "普通分销商"
            },
            "activated_at": "2025-07-28T10:00:00Z"
        }
    ]
}
```

#### 3. 佣金记录查询
```http
GET /api/v1/distribution/commission/

Response:
{
    "count": 20,
    "results": [
        {
            "id": 1,
            "order": {
                "order_no": "ORD20250728001",
                "final_amount": "100.00"
            },
            "commission_amount": "5.00",
            "commission_type": "direct",
            "status": "confirmed",
            "created_at": "2025-07-28T10:00:00Z"
        }
    ]
}
```

#### 4. 生成推广码
```http
POST /api/v1/distribution/generate-code/

{
    "name": "个人推广码",
    "code_type": "personal",
    "description": "我的专属推广码"
}

Response:
{
    "id": 1,
    "code": "PROMO12345",
    "name": "个人推广码",
    "code_type": "personal",
    "status": "active",
    "usage_limit": null,
    "used_count": 0,
    "created_at": "2025-07-28T10:00:00Z"
}
```

#### 5. 验证推广码
```http
GET /api/v1/distribution/validate-code/PROMO12345/

Response:
{
    "valid": true,
    "code_info": {
        "id": 1,
        "code": "PROMO12345",
        "name": "个人推广码",
        "distributor_info": {
            "user": {
                "username": "distributor1",
                "nickname": "分销商1"
            }
        }
    }
}
```

### 数据统计API

#### 1. 佣金汇总
```http
GET /api/v1/distribution/commission/summary/

Response:
{
    "total_commission": "1000.00",
    "pending_commission": "200.00",
    "confirmed_commission": "500.00",
    "settled_commission": "300.00",
    "record_count": 50
}
```

#### 2. 统计分析
```http
GET /api/v1/distribution/stats/

Response:
{
    "by_type": [
        {
            "commission_type": "direct",
            "total_amount": "800.00",
            "count": 40
        },
        {
            "commission_type": "indirect",
            "total_amount": "200.00",
            "count": 10
        }
    ],
    "by_status": [
        {
            "status": "confirmed",
            "total_amount": "500.00",
            "count": 25
        }
    ]
}
```

---

**文档版本**：v1.1
**创建时间**：2025年7月24日
**更新时间**：2025年7月28日
**维护人员**：智梦科技开发团队
