# -*- coding: utf-8 -*-
"""
智梦科技系统 - AI功能模型测试

AI功能模块的模型测试用例
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from ..models import (
    AIServiceConfig, ChatbotConversation, ChatbotMessage,
    RecommendationConfig, UserBehaviorLog, AITask
)

User = get_user_model()


class AIServiceConfigModelTest(TestCase):
    """AI服务配置模型测试"""
    
    def setUp(self):
        self.service_config = AIServiceConfig.objects.create(
            name='测试智能客服',
            service_type='chatbot',
            api_endpoint='https://api.example.com/chatbot',
            api_key='test_api_key',
            config_data={'model': 'gpt-3.5', 'temperature': 0.7},
            is_active=True
        )
    
    def test_service_config_creation(self):
        """测试服务配置创建"""
        self.assertEqual(self.service_config.name, '测试智能客服')
        self.assertEqual(self.service_config.service_type, 'chatbot')
        self.assertTrue(self.service_config.is_active)
        self.assertIsNotNone(self.service_config.created_at)
    
    def test_service_config_str(self):
        """测试字符串表示"""
        expected = "测试智能客服 (智能客服)"
        self.assertEqual(str(self.service_config), expected)
    
    def test_service_config_meta(self):
        """测试模型元数据"""
        self.assertEqual(self.service_config._meta.db_table, 'ai_service_config')
        self.assertEqual(self.service_config._meta.verbose_name, 'AI服务配置')


class ChatbotConversationModelTest(TestCase):
    """智能客服对话模型测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.conversation = ChatbotConversation.objects.create(
            user=self.user,
            session_id='test_session_123',
            status='active'
        )
    
    def test_conversation_creation(self):
        """测试对话创建"""
        self.assertEqual(self.conversation.user, self.user)
        self.assertEqual(self.conversation.session_id, 'test_session_123')
        self.assertEqual(self.conversation.status, 'active')
        self.assertIsNotNone(self.conversation.start_time)
    
    def test_conversation_str(self):
        """测试字符串表示"""
        expected = "对话 test_session_123 - 进行中"
        self.assertEqual(str(self.conversation), expected)
    
    def test_conversation_without_user(self):
        """测试匿名用户对话"""
        anonymous_conversation = ChatbotConversation.objects.create(
            session_id='anonymous_session_456',
            status='active'
        )
        self.assertIsNone(anonymous_conversation.user)
        self.assertEqual(anonymous_conversation.session_id, 'anonymous_session_456')


class ChatbotMessageModelTest(TestCase):
    """智能客服消息模型测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.conversation = ChatbotConversation.objects.create(
            user=self.user,
            session_id='test_session_123',
            status='active'
        )
        self.message = ChatbotMessage.objects.create(
            conversation=self.conversation,
            message_type='user',
            content='你好，我想咨询产品信息',
            metadata={'intent': 'product_inquiry'}
        )
    
    def test_message_creation(self):
        """测试消息创建"""
        self.assertEqual(self.message.conversation, self.conversation)
        self.assertEqual(self.message.message_type, 'user')
        self.assertEqual(self.message.content, '你好，我想咨询产品信息')
        self.assertEqual(self.message.metadata['intent'], 'product_inquiry')
    
    def test_message_str(self):
        """测试字符串表示"""
        expected = "用户消息: 你好，我想咨询产品信息"
        self.assertEqual(str(self.message), expected)
    
    def test_message_str_truncation(self):
        """测试长消息的字符串表示截断"""
        long_message = ChatbotMessage.objects.create(
            conversation=self.conversation,
            message_type='bot',
            content='这是一条非常长的消息内容，用来测试字符串表示的截断功能是否正常工作，应该只显示前50个字符'
        )
        self.assertTrue(len(str(long_message)) <= 60)  # 包含类型前缀


class RecommendationConfigModelTest(TestCase):
    """推荐算法配置模型测试"""
    
    def setUp(self):
        self.recommendation_config = RecommendationConfig.objects.create(
            name='协同过滤推荐',
            algorithm_type='collaborative',
            parameters={'min_common_items': 3, 'max_neighbors': 50},
            weight=1.0,
            is_active=True
        )
    
    def test_recommendation_config_creation(self):
        """测试推荐配置创建"""
        self.assertEqual(self.recommendation_config.name, '协同过滤推荐')
        self.assertEqual(self.recommendation_config.algorithm_type, 'collaborative')
        self.assertEqual(self.recommendation_config.weight, 1.0)
        self.assertTrue(self.recommendation_config.is_active)
    
    def test_recommendation_config_str(self):
        """测试字符串表示"""
        expected = "协同过滤推荐 (协同过滤)"
        self.assertEqual(str(self.recommendation_config), expected)


class UserBehaviorLogModelTest(TestCase):
    """用户行为日志模型测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.behavior_log = UserBehaviorLog.objects.create(
            user=self.user,
            session_id='session_123',
            action_type='view',
            target_type='product',
            target_id='product_001',
            context_data={'page': 'product_detail', 'referrer': 'search'},
            ip_address='***********',
            user_agent='Mozilla/5.0 Test Browser'
        )
    
    def test_behavior_log_creation(self):
        """测试行为日志创建"""
        self.assertEqual(self.behavior_log.user, self.user)
        self.assertEqual(self.behavior_log.action_type, 'view')
        self.assertEqual(self.behavior_log.target_type, 'product')
        self.assertEqual(self.behavior_log.target_id, 'product_001')
        self.assertEqual(self.behavior_log.ip_address, '***********')
    
    def test_behavior_log_str(self):
        """测试字符串表示"""
        expected = "testuser - 浏览"
        self.assertEqual(str(self.behavior_log), expected)
    
    def test_anonymous_behavior_log(self):
        """测试匿名用户行为日志"""
        anonymous_log = UserBehaviorLog.objects.create(
            session_id='anonymous_session_456',
            action_type='click',
            target_type='product',
            target_id='product_002',
            ip_address='***********'
        )
        self.assertIsNone(anonymous_log.user)
        expected = "匿名用户 - 点击"
        self.assertEqual(str(anonymous_log), expected)


class AITaskModelTest(TestCase):
    """AI任务模型测试"""
    
    def setUp(self):
        self.task = AITask.objects.create(
            name='推荐模型训练任务',
            task_type='recommendation_training',
            status='pending',
            parameters={'dataset': 'user_behavior', 'epochs': 100},
            progress=0
        )
    
    def test_task_creation(self):
        """测试任务创建"""
        self.assertEqual(self.task.name, '推荐模型训练任务')
        self.assertEqual(self.task.task_type, 'recommendation_training')
        self.assertEqual(self.task.status, 'pending')
        self.assertEqual(self.task.progress, 0)
        self.assertIsNotNone(self.task.created_at)
    
    def test_task_str(self):
        """测试字符串表示"""
        expected = "推荐模型训练任务 - 待执行"
        self.assertEqual(str(self.task), expected)
    
    def test_task_duration_property(self):
        """测试任务时长属性"""
        # 未开始的任务
        self.assertIsNone(self.task.duration)
        
        # 设置开始和结束时间
        start_time = timezone.now()
        end_time = start_time + timedelta(minutes=30)
        
        self.task.start_time = start_time
        self.task.end_time = end_time
        self.task.save()
        
        duration = self.task.duration
        self.assertIsNotNone(duration)
        self.assertEqual(duration.total_seconds(), 1800)  # 30分钟 = 1800秒
    
    def test_task_status_update(self):
        """测试任务状态更新"""
        # 开始任务
        self.task.status = 'running'
        self.task.start_time = timezone.now()
        self.task.progress = 50
        self.task.save()
        
        self.assertEqual(self.task.status, 'running')
        self.assertEqual(self.task.progress, 50)
        self.assertIsNotNone(self.task.start_time)
        
        # 完成任务
        self.task.status = 'completed'
        self.task.end_time = timezone.now()
        self.task.progress = 100
        self.task.result_data = {'accuracy': 0.95, 'loss': 0.05}
        self.task.save()
        
        self.assertEqual(self.task.status, 'completed')
        self.assertEqual(self.task.progress, 100)
        self.assertIsNotNone(self.task.end_time)
        self.assertEqual(self.task.result_data['accuracy'], 0.95)
