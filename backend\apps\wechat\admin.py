from django.contrib import admin
from .models import WechatUser, WechatMessage, WechatMenu, WechatTemplate, WechatTemplateMessage, WechatConfig


@admin.register(WechatUser)
class WechatUserAdmin(admin.ModelAdmin):
    """微信用户管理"""
    list_display = ('id', 'nickname', 'openid', 'user', 'subscribe', 'city', 'province', 'created_at')
    list_filter = ('subscribe', 'gender', 'city', 'province', 'created_at')
    search_fields = ('nickname', 'openid', 'unionid', 'user__username')
    readonly_fields = ('openid', 'unionid', 'created_at', 'updated_at')
    raw_id_fields = ('user',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'openid', 'unionid', 'nickname', 'avatar_url')
        }),
        ('个人资料', {
            'fields': ('gender', 'city', 'province', 'country', 'language')
        }),
        ('关注信息', {
            'fields': ('subscribe', 'subscribe_time')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(WechatMessage)
class WechatMessageAdmin(admin.ModelAdmin):
    """微信消息管理"""
    list_display = ('id', 'wechat_user', 'msg_type', 'direction', 'title', 'created_at')
    list_filter = ('msg_type', 'direction', 'created_at')
    search_fields = ('wechat_user__nickname', 'content', 'title')
    readonly_fields = ('msg_id', 'created_at', 'updated_at')
    raw_id_fields = ('wechat_user',)
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('wechat_user', 'msg_id', 'msg_type', 'direction')
        }),
        ('消息内容', {
            'fields': ('content', 'title', 'description', 'url')
        }),
        ('媒体信息', {
            'fields': ('media_id', 'pic_url'),
            'classes': ('collapse',)
        }),
        ('位置信息', {
            'fields': ('location_x', 'location_y', 'scale', 'label'),
            'classes': ('collapse',)
        }),
        ('事件信息', {
            'fields': ('event', 'event_key'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(WechatMenu)
class WechatMenuAdmin(admin.ModelAdmin):
    """微信菜单管理"""
    list_display = ('id', 'name', 'type', 'parent', 'sort_order', 'is_active')
    list_filter = ('type', 'is_active', 'parent')
    search_fields = ('name', 'key', 'url')
    list_editable = ('sort_order', 'is_active')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'type', 'parent', 'sort_order', 'is_active')
        }),
        ('菜单配置', {
            'fields': ('key', 'url', 'media_id')
        }),
    )


@admin.register(WechatTemplate)
class WechatTemplateAdmin(admin.ModelAdmin):
    """微信模板消息管理"""
    list_display = ('id', 'title', 'template_id', 'primary_industry', 'deputy_industry', 'is_active')
    list_filter = ('primary_industry', 'deputy_industry', 'is_active')
    search_fields = ('title', 'template_id', 'content')
    list_editable = ('is_active',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('template_id', 'title', 'is_active')
        }),
        ('行业信息', {
            'fields': ('primary_industry', 'deputy_industry')
        }),
        ('模板内容', {
            'fields': ('content', 'example')
        }),
    )


@admin.register(WechatTemplateMessage)
class WechatTemplateMessageAdmin(admin.ModelAdmin):
    """模板消息记录管理"""
    list_display = ('id', 'wechat_user', 'template', 'status', 'sent_at', 'created_at')
    list_filter = ('status', 'sent_at', 'created_at')
    search_fields = ('wechat_user__nickname', 'template__title', 'msg_id')
    readonly_fields = ('msg_id', 'sent_at', 'created_at', 'updated_at')
    raw_id_fields = ('wechat_user', 'template')
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('wechat_user', 'template', 'status', 'msg_id')
        }),
        ('消息内容', {
            'fields': ('data', 'url', 'miniprogram')
        }),
        ('发送信息', {
            'fields': ('sent_at', 'error_msg')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


class WechatConfigAdmin(admin.ModelAdmin):
    """微信配置管理"""
    list_display = ('id', 'platform', 'name', 'app_id', 'is_active', 'created_at')
    list_filter = ('platform', 'is_active', 'created_at')
    search_fields = ('name', 'app_id', 'description')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('基本信息', {
            'fields': ('platform', 'name', 'description', 'is_active')
        }),
        ('应用配置', {
            'fields': ('app_id', 'app_secret', 'token', 'encoding_aes_key'),
            'classes': ('collapse',)
        }),
        ('企业微信配置', {
            'fields': ('corp_id', 'corp_secret', 'agent_id'),
            'classes': ('collapse',)
        }),
        ('微信支付配置', {
            'fields': ('mch_id', 'mch_key', 'cert_path', 'key_path'),
            'classes': ('collapse',)
        }),
        ('额外配置', {
            'fields': ('extra_config',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        # 根据平台类型动态显示字段
        if obj:
            if obj.platform == 'work':
                # 企业微信显示企业相关字段
                pass
            elif obj.platform == 'payment':
                # 微信支付显示支付相关字段
                pass
        return form


# 手动注册模型
admin.site.register(WechatConfig, WechatConfigAdmin)
