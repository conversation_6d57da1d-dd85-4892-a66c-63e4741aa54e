[INFO] 2025-07-25 17:21:50,256 autoreload 17548 21812 Watching for file changes with StatReloader
[WARNING] 2025-07-25 17:21:53,244 log 17548 12000 Not Found: /
[WARNING] 2025-07-25 17:21:53,245 basehttp 17548 12000 "GET / HTTP/1.1" 404 12645
[INFO] 2025-07-25 17:21:53,278 basehttp 17548 21720 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
[INFO] 2025-07-25 17:21:53,281 basehttp 17548 20784 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
[INFO] 2025-07-25 17:21:53,347 basehttp 17548 20784 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
[INFO] 2025-07-25 17:21:53,385 basehttp 17548 20784 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
[WARNING] 2025-07-25 17:21:53,454 log 17548 20784 Not Found: /favicon.ico
[WARNING] 2025-07-25 17:21:53,455 basehttp 17548 20784 "GET /favicon.ico HTTP/1.1" 404 12706
[INFO] 2025-07-25 17:22:03,943 basehttp 17548 9424 "GET /admin/ HTTP/1.1" 302 0
[INFO] 2025-07-25 17:22:04,145 basehttp 17548 9424 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 13067
[INFO] 2025-07-25 17:22:04,155 basehttp 17548 9416 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
[INFO] 2025-07-25 17:22:04,155 basehttp 17548 22144 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
[INFO] 2025-07-25 17:22:04,155 basehttp 17548 9424 "GET /static/admin/css/base.css HTTP/1.1" 304 0
[INFO] 2025-07-25 17:22:04,223 basehttp 17548 9424 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
[INFO] 2025-07-25 17:22:04,223 basehttp 17548 9416 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
[INFO] 2025-07-25 17:22:04,223 basehttp 17548 22144 "GET /static/admin/css/login.css HTTP/1.1" 304 0
[INFO] 2025-07-25 17:22:04,231 basehttp 17548 9416 "GET /static/admin/css/themes.css HTTP/1.1" 304 0
[INFO] 2025-07-25 17:22:04,232 basehttp 17548 9416 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
[INFO] 2025-07-25 17:22:10,133 basehttp 17548 9416 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
[ERROR] 2025-07-25 17:22:10,403 log 17548 9416 Internal Server Error: /admin/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'switch_theme' not found. 'switch_theme' is not a valid view function or pattern name.
[ERROR] 2025-07-25 17:22:10,482 basehttp 17548 9416 "GET /admin/ HTTP/1.1" 500 469720
[INFO] 2025-07-25 17:23:02,153 basehttp 17548 9416 "GET /api/docs HTTP/1.1" 301 0
[INFO] 2025-07-25 17:23:02,317 basehttp 17548 22144 "GET /api/docs/ HTTP/1.1" 200 13842
[INFO] 2025-07-25 17:23:06,615 basehttp 17548 22144 "GET /api/schema/ HTTP/1.1" 200 123614
[INFO] 2025-07-25 17:27:04,113 autoreload 21884 22304 Watching for file changes with StatReloader
[WARNING] 2025-07-25 17:27:05,969 log 21884 18856 Not Found: /
[WARNING] 2025-07-25 17:27:05,970 basehttp 21884 18856 "GET / HTTP/1.1" 404 12643
[ERROR] 2025-07-25 17:27:19,023 log 21884 18856 Internal Server Error: /admin/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'switch_theme' not found. 'switch_theme' is not a valid view function or pattern name.
[ERROR] 2025-07-25 17:27:19,140 basehttp 21884 18856 "GET /admin/ HTTP/1.1" 500 469642
[WARNING] 2025-07-25 17:27:20,765 log 21884 18856 Not Found: /redoc/
[WARNING] 2025-07-25 17:27:20,765 basehttp 21884 18856 "GET /redoc/ HTTP/1.1" 404 12686
[INFO] 2025-07-25 17:39:05,745 autoreload 21796 14588 Watching for file changes with StatReloader
[WARNING] 2025-07-25 17:39:08,468 log 21796 10620 Not Found: /
[WARNING] 2025-07-25 17:39:08,469 basehttp 21796 10620 "GET / HTTP/1.1" 404 13111
[ERROR] 2025-07-25 17:39:13,258 log 21796 10620 Internal Server Error: /admin/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'switch_theme' not found. 'switch_theme' is not a valid view function or pattern name.
[ERROR] 2025-07-25 17:39:13,374 basehttp 21796 10620 "GET /admin/ HTTP/1.1" 500 469642
[ERROR] 2025-07-25 17:39:13,843 log 21796 10620 Internal Server Error: /admin/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'switch_theme' not found. 'switch_theme' is not a valid view function or pattern name.
[ERROR] 2025-07-25 17:39:13,918 basehttp 21796 10620 "GET /admin/ HTTP/1.1" 500 469368
[INFO] 2025-07-25 17:44:37,838 autoreload 22192 15684 Watching for file changes with StatReloader
[INFO] 2025-07-25 17:44:40,839 basehttp 22192 9252 "GET /admin/ HTTP/1.1" 200 25279
[INFO] 2025-07-25 17:44:40,872 basehttp 22192 9252 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
[INFO] 2025-07-25 17:44:40,885 basehttp 22192 9252 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
[INFO] 2025-07-25 17:44:40,886 basehttp 22192 14060 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
[INFO] 2025-07-25 17:44:40,886 basehttp 22192 18684 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
[INFO] 2025-07-25 17:46:34,772 autoreload 5468 15480 Watching for file changes with StatReloader
[INFO] 2025-07-25 17:46:37,762 basehttp 5468 22120 "GET /admin/switch_theme/blue/ HTTP/1.1" 302 0
[INFO] 2025-07-25 17:46:38,007 basehttp 5468 22120 "GET /admin/ HTTP/1.1" 200 25283
[INFO] 2025-07-25 17:46:40,884 basehttp 5468 22120 "GET /admin/switch_theme/dark/ HTTP/1.1" 302 0
[INFO] 2025-07-25 17:46:41,126 basehttp 5468 22120 "GET /admin/ HTTP/1.1" 200 25283
[INFO] 2025-07-25 17:46:42,955 basehttp 5468 22120 "GET /admin/switch_theme/green/ HTTP/1.1" 302 0
[INFO] 2025-07-25 17:46:43,155 basehttp 5468 22120 "GET /admin/ HTTP/1.1" 200 25284
[INFO] 2025-07-25 17:46:45,869 basehttp 5468 22120 "GET /admin/switch_theme/default/ HTTP/1.1" 302 0
[INFO] 2025-07-25 17:46:46,051 basehttp 5468 22120 "GET /admin/ HTTP/1.1" 200 25286
[INFO] 2025-07-25 17:47:46,588 autoreload 19632 21988 Watching for file changes with StatReloader
[WARNING] 2025-07-25 17:47:48,113 log 19632 18144 Not Found: /redoc/
[WARNING] 2025-07-25 17:47:48,113 basehttp 19632 18144 "GET /redoc/ HTTP/1.1" 404 13153
[INFO] 2025-07-25 17:48:09,588 autoreload 20220 11644 Watching for file changes with StatReloader
[WARNING] 2025-07-25 17:50:14,537 log 20220 11180 Not Found: /redoc/
[WARNING] 2025-07-25 17:50:14,538 basehttp 20220 11180 "GET /redoc/ HTTP/1.1" 404 13153
[INFO] 2025-07-25 17:50:26,994 basehttp 20220 11180 "GET /api/docs/ HTTP/1.1" 200 13850
[ERROR] 2025-07-25 17:50:27,364 log 20220 11180 Internal Server Error: /api/v1/core/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `unit_price` is not valid for model `OrderItem` in `apps.orders.serializers.OrderItemSerializer`.
[ERROR] 2025-07-25 17:50:27,412 basehttp 20220 11180 "GET /api/v1/core/schema/ HTTP/1.1" 500 201621
[ERROR] 2025-07-25 17:50:27,566 log 20220 11180 Internal Server Error: /api/v1/core/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `unit_price` is not valid for model `OrderItem` in `apps.orders.serializers.OrderItemSerializer`.
[ERROR] 2025-07-25 17:50:27,655 basehttp 20220 11180 "GET /api/v1/core/schema/ HTTP/1.1" 500 201621
[INFO] 2025-07-25 17:50:27,979 basehttp 20220 11180 "GET /__debug__/history_sidebar/?store_id=7923fef1ce6040b3b0e7b0bbfd51e400 HTTP/1.1" 200 9495
[INFO] 2025-07-25 17:50:40,525 basehttp 20220 11180 "GET /admin/ HTTP/1.1" 200 25286
[INFO] 2025-07-25 17:50:48,497 basehttp 20220 11180 "GET /admin/products/product/ HTTP/1.1" 200 27110
[INFO] 2025-07-25 17:50:48,527 basehttp 20220 22132 "GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
[INFO] 2025-07-25 17:50:48,527 basehttp 20220 5048 "GET /static/admin/js/core.js HTTP/1.1" 200 6208
[INFO] 2025-07-25 17:50:48,532 basehttp 20220 22132 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 9777
[INFO] 2025-07-25 17:50:48,532 basehttp 20220 9792 "GET /static/admin/css/forms.css HTTP/1.1" 200 8525
[INFO] 2025-07-25 17:50:48,533 basehttp 20220 11180 "GET /static/admin/css/changelists.css HTTP/1.1" 200 6878
[INFO] 2025-07-25 17:50:48,535 basehttp 20220 5048 "GET /static/admin/js/actions.js HTTP/1.1" 200 8076
[INFO] 2025-07-25 17:50:48,540 basehttp 20220 9792 "GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
[INFO] 2025-07-25 17:50:48,540 basehttp 20220 11180 "GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
[INFO] 2025-07-25 17:50:48,547 basehttp 20220 18168 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 285314
[INFO] 2025-07-25 17:50:48,552 basehttp 20220 22132 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 325171
[INFO] 2025-07-25 17:50:48,555 basehttp 20220 22132 "GET /static/admin/img/search.svg HTTP/1.1" 200 458
[INFO] 2025-07-25 17:50:48,682 basehttp 20220 5048 "GET /static/admin/css/widgets.css HTTP/1.1" 200 11991
[INFO] 2025-07-25 17:50:48,684 basehttp 20220 5048 "GET /static/admin/js/filters.js HTTP/1.1" 200 978
[INFO] 2025-07-25 17:50:48,857 basehttp 20220 21692 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 17:50:48,880 basehttp 20220 21692 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
[INFO] 2025-07-25 17:50:54,143 basehttp 20220 21692 "GET /admin/products/product/add/ HTTP/1.1" 200 68160
[INFO] 2025-07-25 17:50:54,164 basehttp 20220 22132 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
[INFO] 2025-07-25 17:50:54,165 basehttp 20220 5048 "GET /static/admin/js/inlines.js HTTP/1.1" 200 15628
[INFO] 2025-07-25 17:50:54,174 basehttp 20220 5048 "GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 200 392
[INFO] 2025-07-25 17:50:54,177 basehttp 20220 5048 "GET /static/admin/img/icon-unknown.svg HTTP/1.1" 200 655
[INFO] 2025-07-25 17:50:54,180 basehttp 20220 5048 "GET /static/admin/js/change_form.js HTTP/1.1" 200 606
[INFO] 2025-07-25 17:50:54,398 basehttp 20220 21692 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 17:50:54,479 basehttp 20220 21692 "GET /static/admin/img/inline-delete.svg HTTP/1.1" 200 537
[INFO] 2025-07-25 17:51:00,794 basehttp 20220 21692 "GET /admin/products/productcategory/ HTTP/1.1" 200 26711
[INFO] 2025-07-25 17:51:00,974 basehttp 20220 21692 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 17:51:02,158 basehttp 20220 21692 "GET /admin/products/productcategory/add/ HTTP/1.1" 200 31048
[INFO] 2025-07-25 17:51:02,340 basehttp 20220 21692 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 17:51:09,815 basehttp 20220 21692 "GET /api/docs/ HTTP/1.1" 200 13850
[ERROR] 2025-07-25 17:51:10,016 log 20220 21692 Internal Server Error: /api/v1/core/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `unit_price` is not valid for model `OrderItem` in `apps.orders.serializers.OrderItemSerializer`.
[ERROR] 2025-07-25 17:51:10,160 basehttp 20220 21692 "GET /api/v1/core/schema/ HTTP/1.1" 500 201347
[ERROR] 2025-07-25 17:51:10,318 log 20220 21692 Internal Server Error: /api/v1/core/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `unit_price` is not valid for model `OrderItem` in `apps.orders.serializers.OrderItemSerializer`.
[ERROR] 2025-07-25 17:51:10,362 basehttp 20220 21692 "GET /api/v1/core/schema/ HTTP/1.1" 500 201347
[INFO] 2025-07-25 17:51:10,740 basehttp 20220 21692 "GET /__debug__/history_sidebar/?store_id=84abfd872ae7451483c7724410ee5b3a HTTP/1.1" 200 9495
[INFO] 2025-07-25 17:56:20,066 autoreload 18012 16232 Watching for file changes with StatReloader
[INFO] 2025-07-25 17:56:24,335 basehttp 18012 10636 "GET /api/docs/ HTTP/1.1" 200 13842
[ERROR] 2025-07-25 17:56:24,561 log 18012 10636 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `unit_price` is not valid for model `OrderItem` in `apps.orders.serializers.OrderItemSerializer`.
[ERROR] 2025-07-25 17:56:24,645 basehttp 18012 10636 "GET /api/schema/ HTTP/1.1" 500 196972
[ERROR] 2025-07-25 17:56:24,774 log 18012 10636 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `unit_price` is not valid for model `OrderItem` in `apps.orders.serializers.OrderItemSerializer`.
[ERROR] 2025-07-25 17:56:24,817 basehttp 18012 10636 "GET /api/schema/ HTTP/1.1" 500 196972
[INFO] 2025-07-25 17:56:25,137 basehttp 18012 10636 "GET /__debug__/history_sidebar/?store_id=d0fdfbd438bd4ad2977cf68151b8d036 HTTP/1.1" 200 9487
[INFO] 2025-07-25 17:56:30,519 basehttp 18012 10636 "GET /admin/products/productcategory/add/ HTTP/1.1" 200 31048
[INFO] 2025-07-25 17:56:30,748 basehttp 18012 10636 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 17:56:33,410 basehttp 18012 10636 "GET /admin/ HTTP/1.1" 200 25286
[INFO] 2025-07-25 17:57:43,257 basehttp 18012 10636 "GET /api/docs/ HTTP/1.1" 200 13842
[ERROR] 2025-07-25 17:57:43,445 log 18012 10636 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `unit_price` is not valid for model `OrderItem` in `apps.orders.serializers.OrderItemSerializer`.
[ERROR] 2025-07-25 17:57:43,493 basehttp 18012 10636 "GET /api/schema/ HTTP/1.1" 500 196972
[ERROR] 2025-07-25 17:57:43,620 log 18012 10636 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `unit_price` is not valid for model `OrderItem` in `apps.orders.serializers.OrderItemSerializer`.
[ERROR] 2025-07-25 17:57:43,663 basehttp 18012 10636 "GET /api/schema/ HTTP/1.1" 500 196972
[INFO] 2025-07-25 17:57:43,981 basehttp 18012 10636 "GET /__debug__/history_sidebar/?store_id=b8b95c71c5414220b6d88a7dcbe25b52 HTTP/1.1" 200 9487
[INFO] 2025-07-25 18:00:31,017 autoreload 17480 5004 Watching for file changes with StatReloader
[INFO] 2025-07-25 18:00:33,312 basehttp 17480 15184 "GET /api/docs/ HTTP/1.1" 200 13842
[ERROR] 2025-07-25 18:00:33,607 log 17480 15184 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `unit_price` is not valid for model `OrderItem` in `apps.orders.serializers.OrderItemSerializer`.
[ERROR] 2025-07-25 18:00:33,696 basehttp 17480 15184 "GET /api/schema/ HTTP/1.1" 500 196972
[ERROR] 2025-07-25 18:00:33,831 log 17480 15184 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `unit_price` is not valid for model `OrderItem` in `apps.orders.serializers.OrderItemSerializer`.
[ERROR] 2025-07-25 18:00:33,876 basehttp 17480 15184 "GET /api/schema/ HTTP/1.1" 500 196972
[INFO] 2025-07-25 18:00:34,194 basehttp 17480 15184 "GET /__debug__/history_sidebar/?store_id=5f2c7a3a803a4c14bb2a7004ba856da1 HTTP/1.1" 200 9487
[INFO] 2025-07-25 19:07:03,567 autoreload 21660 21396 Watching for file changes with StatReloader
[WARNING] 2025-07-25 19:07:06,416 log 21660 19740 Not Found: /
[WARNING] 2025-07-25 19:07:06,417 basehttp 21660 19740 "GET / HTTP/1.1" 404 13111
[WARNING] 2025-07-25 19:07:06,781 log 21660 20644 Not Found: /favicon.ico
[WARNING] 2025-07-25 19:07:06,782 basehttp 21660 20644 "GET /favicon.ico HTTP/1.1" 404 13174
[INFO] 2025-07-25 19:07:12,095 basehttp 21660 21256 "GET /admin/ HTTP/1.1" 200 25286
[INFO] 2025-07-25 19:07:16,037 basehttp 21660 21256 "GET /admin/products/productattribute/ HTTP/1.1" 200 26413
[INFO] 2025-07-25 19:07:16,200 basehttp 21660 21256 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:07:17,481 basehttp 21660 21256 "GET /admin/users/user/ HTTP/1.1" 200 30078
[INFO] 2025-07-25 19:07:17,502 basehttp 21660 12052 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
[INFO] 2025-07-25 19:07:17,502 basehttp 21660 8796 "GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
[INFO] 2025-07-25 19:07:17,664 basehttp 21660 21256 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:07:19,272 basehttp 21660 21256 "GET /admin/users/useraddress/ HTTP/1.1" 200 25820
[INFO] 2025-07-25 19:07:19,431 basehttp 21660 21256 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:07:38,861 basehttp 21660 21256 "GET /admin/users/user/ HTTP/1.1" 200 30078
[INFO] 2025-07-25 19:07:39,057 basehttp 21660 21256 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:07:42,940 basehttp 21660 21256 "GET /admin/users/user/1/change/ HTTP/1.1" 200 44352
[INFO] 2025-07-25 19:07:42,961 basehttp 21660 12052 "GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
[INFO] 2025-07-25 19:07:42,962 basehttp 21660 8796 "GET /static/admin/js/calendar.js HTTP/1.1" 200 9141
[INFO] 2025-07-25 19:07:43,078 basehttp 21660 21256 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:07:43,134 basehttp 21660 21256 "GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
[INFO] 2025-07-25 19:08:03,189 basehttp 21660 21256 "GET /admin/orders/payment/ HTTP/1.1" 200 27411
[INFO] 2025-07-25 19:08:03,383 basehttp 21660 21256 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:08:04,018 basehttp 21660 21256 "GET /admin/orders/order/ HTTP/1.1" 200 28153
[INFO] 2025-07-25 19:08:04,160 basehttp 21660 21256 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:08:05,106 basehttp 21660 21256 "GET /admin/orders/orderitem/ HTTP/1.1" 200 25936
[INFO] 2025-07-25 19:08:05,278 basehttp 21660 21256 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:08:06,478 basehttp 21660 21256 "GET /admin/products/productattributevalue/ HTTP/1.1" 200 26004
[INFO] 2025-07-25 19:08:06,699 basehttp 21660 21256 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:08:07,567 basehttp 21660 21256 "GET /admin/products/productattribute/ HTTP/1.1" 200 26413
[INFO] 2025-07-25 19:08:07,708 basehttp 21660 21256 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:08:08,429 basehttp 21660 21256 "GET /admin/products/productimage/ HTTP/1.1" 200 26703
[INFO] 2025-07-25 19:08:08,591 basehttp 21660 21256 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:08:09,105 basehttp 21660 21256 "GET /admin/products/productcategory/ HTTP/1.1" 200 26710
[INFO] 2025-07-25 19:08:09,442 basehttp 21660 21256 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:08:15,765 basehttp 21660 21256 "GET /api/docs/ HTTP/1.1" 200 13842
[ERROR] 2025-07-25 19:08:16,045 log 21660 21256 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 91, in get_operation
    parameters = self._get_parameters()
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 259, in _get_parameters
    **dict_helper(self._get_filter_parameters()),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 545, in _get_filter_parameters
    parameters += filter_extension.get_schema_operation_parameters(self)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\contrib\django_filters.py", line 58, in get_schema_operation_parameters
    filterset_class = self.target.get_filterset_class(auto_schema.view, get_manager(model).none())
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\rest_framework\backends.py", line 49, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\filterset.py", line 82, in __new__
    new_class.base_filters = new_class.get_filters()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\filterset.py", line 371, in get_filters
    raise TypeError(
TypeError: 'Meta.fields' must not contain non-model field names: status
[ERROR] 2025-07-25 19:08:16,096 basehttp 21660 21256 "GET /api/schema/ HTTP/1.1" 500 173765
[ERROR] 2025-07-25 19:08:16,223 log 21660 21256 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 91, in get_operation
    parameters = self._get_parameters()
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 259, in _get_parameters
    **dict_helper(self._get_filter_parameters()),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 545, in _get_filter_parameters
    parameters += filter_extension.get_schema_operation_parameters(self)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\contrib\django_filters.py", line 58, in get_schema_operation_parameters
    filterset_class = self.target.get_filterset_class(auto_schema.view, get_manager(model).none())
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\rest_framework\backends.py", line 49, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\filterset.py", line 82, in __new__
    new_class.base_filters = new_class.get_filters()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\filterset.py", line 371, in get_filters
    raise TypeError(
TypeError: 'Meta.fields' must not contain non-model field names: status
[ERROR] 2025-07-25 19:08:16,267 basehttp 21660 21256 "GET /api/schema/ HTTP/1.1" 500 173765
[INFO] 2025-07-25 19:08:16,681 basehttp 21660 21256 "GET /__debug__/history_sidebar/?store_id=ded4726ef4ab4d6694a121fdfee6ea7b HTTP/1.1" 200 9487
[INFO] 2025-07-25 19:09:15,461 autoreload 21660 21396 F:\zmkj-system\backend\apps\core\views.py changed, reloading.
[INFO] 2025-07-25 19:09:16,884 autoreload 22476 20196 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:09:27,862 autoreload 22476 20196 F:\zmkj-system\backend\apps\orders\serializers.py changed, reloading.
[INFO] 2025-07-25 19:09:28,283 autoreload 21484 15848 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:09:40,104 autoreload 21484 15848 F:\zmkj-system\backend\apps\orders\views.py changed, reloading.
[INFO] 2025-07-25 19:09:40,587 autoreload 3312 22160 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:09:58,602 autoreload 3312 22160 F:\zmkj-system\backend\apps\orders\models.py changed, reloading.
[INFO] 2025-07-25 19:09:59,017 autoreload 9632 9816 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:10:10,839 autoreload 9632 9816 F:\zmkj-system\backend\apps\orders\models.py changed, reloading.
[INFO] 2025-07-25 19:10:11,255 autoreload 10104 6364 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:10:26,178 autoreload 10104 6364 F:\zmkj-system\backend\apps\orders\models.py changed, reloading.
[INFO] 2025-07-25 19:10:26,574 autoreload 20580 20564 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:10:42,527 autoreload 20580 20564 F:\zmkj-system\backend\apps\products\models.py changed, reloading.
[INFO] 2025-07-25 19:10:42,937 autoreload 22028 21252 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:11:26,844 autoreload 22028 21252 F:\zmkj-system\backend\apps\core\views.py changed, reloading.
[INFO] 2025-07-25 19:11:27,278 autoreload 10596 21712 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:12:18,208 basehttp 10596 21584 "GET /api/docs/ HTTP/1.1" 200 13842
[ERROR] 2025-07-25 19:12:18,461 log 10596 21584 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `status` is not valid for model `OrderLog` in `apps.orders.serializers.OrderLogSerializer`.
[ERROR] 2025-07-25 19:12:18,546 basehttp 10596 21584 "GET /api/schema/ HTTP/1.1" 500 203484
[ERROR] 2025-07-25 19:12:18,683 log 10596 21584 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `status` is not valid for model `OrderLog` in `apps.orders.serializers.OrderLogSerializer`.
[ERROR] 2025-07-25 19:12:18,728 basehttp 10596 21584 "GET /api/schema/ HTTP/1.1" 500 203484
[INFO] 2025-07-25 19:12:19,046 basehttp 10596 21584 "GET /__debug__/history_sidebar/?store_id=ecec391ca56d45dc800f86a96b0ddfd4 HTTP/1.1" 200 9487
[INFO] 2025-07-25 19:12:25,954 autoreload 16344 4364 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:12:29,299 basehttp 16344 5284 "GET /api/docs/ HTTP/1.1" 200 13842
[ERROR] 2025-07-25 19:12:29,538 log 16344 5284 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `status` is not valid for model `OrderLog` in `apps.orders.serializers.OrderLogSerializer`.
[ERROR] 2025-07-25 19:12:29,624 basehttp 16344 5284 "GET /api/schema/ HTTP/1.1" 500 203484
[ERROR] 2025-07-25 19:12:29,783 log 16344 5284 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `status` is not valid for model `OrderLog` in `apps.orders.serializers.OrderLogSerializer`.
[ERROR] 2025-07-25 19:12:29,829 basehttp 16344 5284 "GET /api/schema/ HTTP/1.1" 500 203484
[INFO] 2025-07-25 19:12:30,162 basehttp 16344 5284 "GET /__debug__/history_sidebar/?store_id=9316694ce1f942d090786b6d34a571cf HTTP/1.1" 200 9487
[INFO] 2025-07-25 19:13:01,240 basehttp 16344 5284 "GET /admin/switch_theme/dark/ HTTP/1.1" 302 0
[INFO] 2025-07-25 19:13:01,582 basehttp 16344 5284 "GET /admin/products/productcategory/ HTTP/1.1" 200 26707
[INFO] 2025-07-25 19:13:01,759 basehttp 16344 5284 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:13:04,792 basehttp 16344 5284 "GET /admin/switch_theme/blue/ HTTP/1.1" 302 0
[INFO] 2025-07-25 19:13:05,092 basehttp 16344 5284 "GET /admin/products/productcategory/ HTTP/1.1" 200 26707
[INFO] 2025-07-25 19:13:05,273 basehttp 16344 5284 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:13:07,942 basehttp 16344 5284 "GET /admin/switch_theme/green/ HTTP/1.1" 302 0
[INFO] 2025-07-25 19:13:08,152 basehttp 16344 5284 "GET /admin/products/productcategory/ HTTP/1.1" 200 26708
[INFO] 2025-07-25 19:13:08,311 basehttp 16344 5284 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:13:17,376 basehttp 16344 5284 "GET /admin/switch_theme/default/ HTTP/1.1" 302 0
[INFO] 2025-07-25 19:13:17,620 basehttp 16344 5284 "GET /admin/products/productcategory/ HTTP/1.1" 200 26710
[INFO] 2025-07-25 19:13:17,787 basehttp 16344 5284 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:13:38,705 autoreload 16344 4364 F:\zmkj-system\backend\apps\orders\serializers.py changed, reloading.
[INFO] 2025-07-25 19:13:39,524 autoreload 6188 11636 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:13:54,440 autoreload 6188 11636 F:\zmkj-system\backend\apps\core\views.py changed, reloading.
[INFO] 2025-07-25 19:13:54,869 autoreload 20392 2392 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:14:01,316 autoreload 20392 2392 F:\zmkj-system\backend\apps\core\urls.py changed, reloading.
[INFO] 2025-07-25 19:14:01,756 autoreload 2288 14480 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:14:24,956 autoreload 2288 14480 F:\zmkj-system\backend\apps\core\views.py changed, reloading.
[INFO] 2025-07-25 19:14:25,420 autoreload 15236 14516 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:14:30,381 basehttp 15236 6028 "GET /api/docs/ HTTP/1.1" 200 13842
[ERROR] 2025-07-25 19:14:30,673 log 15236 6028 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1026, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 667, in _map_serializer_field
    schema = self._unwrap_list_serializer(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1399, in _unwrap_list_serializer
    result = self._unwrap_list_serializer(serializer.child, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1396, in _unwrap_list_serializer
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `transaction_no` is not valid for model `Payment` in `apps.orders.serializers.PaymentSerializer`.
[ERROR] 2025-07-25 19:14:30,756 basehttp 15236 6028 "GET /api/schema/ HTTP/1.1" 500 236701
[ERROR] 2025-07-25 19:14:30,939 log 15236 6028 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1026, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 667, in _map_serializer_field
    schema = self._unwrap_list_serializer(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1399, in _unwrap_list_serializer
    result = self._unwrap_list_serializer(serializer.child, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1396, in _unwrap_list_serializer
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `transaction_no` is not valid for model `Payment` in `apps.orders.serializers.PaymentSerializer`.
[ERROR] 2025-07-25 19:14:30,984 basehttp 15236 6028 "GET /api/schema/ HTTP/1.1" 500 236701
[INFO] 2025-07-25 19:14:31,325 basehttp 15236 6028 "GET /__debug__/history_sidebar/?store_id=7910756706ff4707b291a5fd22adeb08 HTTP/1.1" 200 9487
[INFO] 2025-07-25 19:15:39,390 basehttp 15236 6028 "GET /admin/users/user/ HTTP/1.1" 200 30079
[INFO] 2025-07-25 19:15:39,631 basehttp 15236 6028 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:15:42,174 basehttp 15236 6028 "GET /admin/users/user/add/ HTTP/1.1" 200 43991
[INFO] 2025-07-25 19:15:42,361 basehttp 15236 6028 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-25 19:15:51,933 autoreload 15236 14516 F:\zmkj-system\backend\apps\core\views.py changed, reloading.
[INFO] 2025-07-25 19:15:52,621 autoreload 20096 16000 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:15:59,272 autoreload 20096 16000 F:\zmkj-system\backend\apps\orders\views.py changed, reloading.
[INFO] 2025-07-25 19:15:59,726 autoreload 16560 9928 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:16:19,829 autoreload 16560 9928 F:\zmkj-system\backend\apps\orders\views.py changed, reloading.
[INFO] 2025-07-25 19:16:20,269 autoreload 1280 18228 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:16:35,718 basehttp 1280 21160 "GET /api/docs/ HTTP/1.1" 200 13842
[ERROR] 2025-07-25 19:16:36,005 log 1280 21160 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1026, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 667, in _map_serializer_field
    schema = self._unwrap_list_serializer(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1399, in _unwrap_list_serializer
    result = self._unwrap_list_serializer(serializer.child, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1396, in _unwrap_list_serializer
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `transaction_no` is not valid for model `Payment` in `apps.orders.serializers.PaymentSerializer`.
[ERROR] 2025-07-25 19:16:36,090 basehttp 1280 21160 "GET /api/schema/ HTTP/1.1" 500 236701
[ERROR] 2025-07-25 19:16:36,308 log 1280 21160 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1026, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 667, in _map_serializer_field
    schema = self._unwrap_list_serializer(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1399, in _unwrap_list_serializer
    result = self._unwrap_list_serializer(serializer.child, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1396, in _unwrap_list_serializer
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `transaction_no` is not valid for model `Payment` in `apps.orders.serializers.PaymentSerializer`.
[ERROR] 2025-07-25 19:16:36,355 basehttp 1280 21160 "GET /api/schema/ HTTP/1.1" 500 236701
[INFO] 2025-07-25 19:16:36,751 basehttp 1280 21160 "GET /__debug__/history_sidebar/?store_id=21018ff7520f4028bebf7252c8c402b2 HTTP/1.1" 200 9487
[WARNING] 2025-07-25 19:16:42,946 log 1280 21160 Not Found: /api/
[WARNING] 2025-07-25 19:16:42,947 basehttp 1280 21160 "GET /api/ HTTP/1.1" 404 13145
[WARNING] 2025-07-25 19:16:57,900 log 1280 21160 Not Found: /__debug__/
[WARNING] 2025-07-25 19:16:57,901 basehttp 1280 21160 "GET /__debug__/ HTTP/1.1" 404 14965
[INFO] 2025-07-25 19:17:18,756 basehttp 1280 21160 "GET /api/docs/ HTTP/1.1" 200 13842
[ERROR] 2025-07-25 19:17:19,050 log 1280 21160 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1026, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 667, in _map_serializer_field
    schema = self._unwrap_list_serializer(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1399, in _unwrap_list_serializer
    result = self._unwrap_list_serializer(serializer.child, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1396, in _unwrap_list_serializer
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `transaction_no` is not valid for model `Payment` in `apps.orders.serializers.PaymentSerializer`.
[ERROR] 2025-07-25 19:17:19,097 basehttp 1280 21160 "GET /api/schema/ HTTP/1.1" 500 236975
[ERROR] 2025-07-25 19:17:19,280 log 1280 21160 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1026, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 667, in _map_serializer_field
    schema = self._unwrap_list_serializer(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1399, in _unwrap_list_serializer
    result = self._unwrap_list_serializer(serializer.child, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1396, in _unwrap_list_serializer
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `transaction_no` is not valid for model `Payment` in `apps.orders.serializers.PaymentSerializer`.
[ERROR] 2025-07-25 19:17:19,381 basehttp 1280 21160 "GET /api/schema/ HTTP/1.1" 500 236975
[INFO] 2025-07-25 19:17:20,321 basehttp 1280 21160 "GET /__debug__/history_sidebar/?store_id=945fe638ab0f44c0ab5e6faf8a7a1c8a HTTP/1.1" 200 9487
[INFO] 2025-07-25 19:17:29,092 basehttp 1280 21160 "GET /api/redoc HTTP/1.1" 301 0
[INFO] 2025-07-25 19:17:29,262 basehttp 1280 17624 "GET /api/redoc/ HTTP/1.1" 200 10112
[ERROR] 2025-07-25 19:17:30,528 log 1280 17624 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1026, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 667, in _map_serializer_field
    schema = self._unwrap_list_serializer(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1399, in _unwrap_list_serializer
    result = self._unwrap_list_serializer(serializer.child, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1396, in _unwrap_list_serializer
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `transaction_no` is not valid for model `Payment` in `apps.orders.serializers.PaymentSerializer`.
[ERROR] 2025-07-25 19:17:30,577 basehttp 1280 17624 "GET /api/schema/ HTTP/1.1" 500 236685
[INFO] 2025-07-25 19:18:22,378 basehttp 1280 17624 "GET /api/redoc/ HTTP/1.1" 200 10113
[ERROR] 2025-07-25 19:18:22,652 log 1280 17624 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1026, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 667, in _map_serializer_field
    schema = self._unwrap_list_serializer(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1399, in _unwrap_list_serializer
    result = self._unwrap_list_serializer(serializer.child, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1396, in _unwrap_list_serializer
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `transaction_no` is not valid for model `Payment` in `apps.orders.serializers.PaymentSerializer`.
[ERROR] 2025-07-25 19:18:22,781 basehttp 1280 17624 "GET /api/schema/ HTTP/1.1" 500 236685
[INFO] 2025-07-25 19:19:19,383 autoreload 1280 18228 F:\zmkj-system\backend\apps\orders\serializers.py changed, reloading.
[INFO] 2025-07-25 19:19:20,591 autoreload 6708 21160 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:19:26,237 autoreload 6708 21160 F:\zmkj-system\backend\apps\orders\serializers.py changed, reloading.
[INFO] 2025-07-25 19:19:26,681 autoreload 9276 10548 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:20:47,516 autoreload 14788 19228 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:22:29,058 autoreload 14788 19228 F:\zmkj-system\backend\apps\orders\serializers.py changed, reloading.
[INFO] 2025-07-25 19:22:29,478 autoreload 21368 9512 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:22:35,068 autoreload 21368 9512 F:\zmkj-system\backend\apps\orders\serializers.py changed, reloading.
[INFO] 2025-07-25 19:22:35,479 autoreload 21868 21956 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:22:39,118 basehttp 21868 21012 "GET /api/redoc/ HTTP/1.1" 200 10112
[ERROR] 2025-07-25 19:22:39,430 log 21868 21012 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1026, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 667, in _map_serializer_field
    schema = self._unwrap_list_serializer(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1399, in _unwrap_list_serializer
    result = self._unwrap_list_serializer(serializer.child, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1396, in _unwrap_list_serializer
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `transaction_no` is not valid for model `Payment` in `apps.orders.serializers.PaymentSerializer`.
[ERROR] 2025-07-25 19:22:39,516 basehttp 21868 21012 "GET /api/schema/ HTTP/1.1" 500 236685
[INFO] 2025-07-25 19:22:44,436 basehttp 21868 21012 "GET /__debug__/history_sidebar/?store_id=545f3d4e729744709a2d22b1e14ea21b HTTP/1.1" 200 9487
[INFO] 2025-07-25 19:22:45,825 basehttp 21868 21012 "GET /api/docs/ HTTP/1.1" 200 13842
[ERROR] 2025-07-25 19:22:46,054 log 21868 21012 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1026, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 667, in _map_serializer_field
    schema = self._unwrap_list_serializer(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1399, in _unwrap_list_serializer
    result = self._unwrap_list_serializer(serializer.child, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1396, in _unwrap_list_serializer
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `transaction_no` is not valid for model `Payment` in `apps.orders.serializers.PaymentSerializer`.
[ERROR] 2025-07-25 19:22:46,142 basehttp 21868 21012 "GET /api/schema/ HTTP/1.1" 500 236701
[ERROR] 2025-07-25 19:22:46,323 log 21868 21012 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1026, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 667, in _map_serializer_field
    schema = self._unwrap_list_serializer(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1399, in _unwrap_list_serializer
    result = self._unwrap_list_serializer(serializer.child, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1396, in _unwrap_list_serializer
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `transaction_no` is not valid for model `Payment` in `apps.orders.serializers.PaymentSerializer`.
[ERROR] 2025-07-25 19:22:46,368 basehttp 21868 21012 "GET /api/schema/ HTTP/1.1" 500 236701
[INFO] 2025-07-25 19:22:46,695 basehttp 21868 21012 "GET /__debug__/history_sidebar/?store_id=57c80a7f3a734911a97d7c4bd26f4018 HTTP/1.1" 200 9487
[INFO] 2025-07-25 19:23:01,730 autoreload 21004 892 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:23:04,952 basehttp 21004 16804 "GET /api/docs/ HTTP/1.1" 200 13842
[ERROR] 2025-07-25 19:23:05,231 log 21004 16804 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1026, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 667, in _map_serializer_field
    schema = self._unwrap_list_serializer(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1399, in _unwrap_list_serializer
    result = self._unwrap_list_serializer(serializer.child, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1396, in _unwrap_list_serializer
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `transaction_no` is not valid for model `Payment` in `apps.orders.serializers.PaymentSerializer`.
[ERROR] 2025-07-25 19:23:05,326 basehttp 21004 16804 "GET /api/schema/ HTTP/1.1" 500 236701
[ERROR] 2025-07-25 19:23:05,517 log 21004 16804 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1026, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 667, in _map_serializer_field
    schema = self._unwrap_list_serializer(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1399, in _unwrap_list_serializer
    result = self._unwrap_list_serializer(serializer.child, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1396, in _unwrap_list_serializer
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `transaction_no` is not valid for model `Payment` in `apps.orders.serializers.PaymentSerializer`.
[ERROR] 2025-07-25 19:23:05,564 basehttp 21004 16804 "GET /api/schema/ HTTP/1.1" 500 236701
[INFO] 2025-07-25 19:23:05,852 basehttp 21004 16804 "GET /__debug__/history_sidebar/?store_id=8fc04d74693e40739f2e8743d821f019 HTTP/1.1" 200 9487
[INFO] 2025-07-25 19:23:11,556 basehttp 21004 16804 "GET /api/redoc/ HTTP/1.1" 200 10112
[ERROR] 2025-07-25 19:23:11,775 log 21004 16804 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 111, in get_operation
    operation['responses'] = self._get_response_bodies()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1367, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1423, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1026, in _map_basic_serializer
    schema = self._map_serializer_field(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 667, in _map_serializer_field
    schema = self._unwrap_list_serializer(field, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1399, in _unwrap_list_serializer
    result = self._unwrap_list_serializer(serializer.child, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1396, in _unwrap_list_serializer
    component = self.resolve_serializer(serializer, direction)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1605, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 927, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 1020, in _map_basic_serializer
    for field in serializer.fields.values():
                 ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `transaction_no` is not valid for model `Payment` in `apps.orders.serializers.PaymentSerializer`.
[ERROR] 2025-07-25 19:23:11,821 basehttp 21004 16804 "GET /api/schema/ HTTP/1.1" 500 236685
[INFO] 2025-07-25 19:24:44,298 autoreload 21004 892 F:\zmkj-system\backend\apps\orders\serializers.py changed, reloading.
[INFO] 2025-07-25 19:24:44,967 autoreload 9768 12700 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:24:53,669 autoreload 9768 12700 F:\zmkj-system\backend\apps\orders\serializers.py changed, reloading.
[INFO] 2025-07-25 19:24:54,096 autoreload 20564 11184 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:24:59,680 autoreload 20564 11184 F:\zmkj-system\backend\apps\orders\serializers.py changed, reloading.
[INFO] 2025-07-25 19:25:00,086 autoreload 17924 16696 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:25:24,822 autoreload 21868 21360 Watching for file changes with StatReloader
[INFO] 2025-07-25 19:25:27,071 basehttp 21868 15016 "GET /api/redoc/ HTTP/1.1" 200 10112
[ERROR] 2025-07-25 19:25:27,356 log 21868 15016 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 91, in get_operation
    parameters = self._get_parameters()
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 259, in _get_parameters
    **dict_helper(self._get_filter_parameters()),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 545, in _get_filter_parameters
    parameters += filter_extension.get_schema_operation_parameters(self)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\contrib\django_filters.py", line 58, in get_schema_operation_parameters
    filterset_class = self.target.get_filterset_class(auto_schema.view, get_manager(model).none())
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\rest_framework\backends.py", line 49, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\filterset.py", line 82, in __new__
    new_class.base_filters = new_class.get_filters()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\filterset.py", line 371, in get_filters
    raise TypeError(
TypeError: 'Meta.fields' must not contain non-model field names: method
[ERROR] 2025-07-25 19:25:27,443 basehttp 21868 15016 "GET /api/schema/ HTTP/1.1" 500 173701
[INFO] 2025-07-26 09:46:40,571 autoreload 1904 10128 Watching for file changes with StatReloader
[WARNING] 2025-07-26 09:46:43,795 log 1904 10304 Not Found: /
[WARNING] 2025-07-26 09:46:43,796 basehttp 1904 10304 "GET / HTTP/1.1" 404 13112
[INFO] 2025-07-26 09:46:43,824 basehttp 1904 10304 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:43,824 basehttp 1904 16748 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:43,894 basehttp 1904 16748 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:43,930 basehttp 1904 16748 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
[WARNING] 2025-07-26 09:46:44,108 log 1904 16748 Not Found: /favicon.ico
[WARNING] 2025-07-26 09:46:44,109 basehttp 1904 16748 "GET /favicon.ico HTTP/1.1" 404 13173
[INFO] 2025-07-26 09:46:48,692 basehttp 1904 16748 "GET /admin/ HTTP/1.1" 200 25286
[INFO] 2025-07-26 09:46:48,702 basehttp 1904 16748 "GET /static/admin/css/base.css HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:48,702 basehttp 1904 23016 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:48,712 basehttp 1904 10304 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:48,769 basehttp 1904 23016 "GET /static/admin/css/dashboard.css HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:48,769 basehttp 1904 10304 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:48,769 basehttp 1904 16748 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:48,777 basehttp 1904 16748 "GET /static/admin/css/themes.css HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:48,781 basehttp 1904 16748 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:48,794 basehttp 1904 16748 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:48,794 basehttp 1904 10304 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:48,828 basehttp 1904 10304 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:54,882 basehttp 1904 10304 "GET /admin/products/product/ HTTP/1.1" 200 27111
[INFO] 2025-07-26 09:46:54,904 basehttp 1904 10304 "GET /static/admin/css/changelists.css HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:54,904 basehttp 1904 16748 "GET /static/admin/css/forms.css HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:54,904 basehttp 1904 16968 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:54,907 basehttp 1904 10304 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:54,910 basehttp 1904 16748 "GET /static/admin/js/actions.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:54,911 basehttp 1904 16968 "GET /static/admin/js/urlify.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:54,911 basehttp 1904 10304 "GET /static/admin/css/widgets.css HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:54,914 basehttp 1904 16748 "GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:54,915 basehttp 1904 16968 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:54,920 basehttp 1904 23464 "GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:54,920 basehttp 1904 24536 "GET /static/admin/js/core.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:54,937 basehttp 1904 10304 "GET /static/admin/img/search.svg HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:54,940 basehttp 1904 10304 "GET /static/admin/js/filters.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:46:55,174 basehttp 1904 23016 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 09:46:55,190 basehttp 1904 23016 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 304 0
[INFO] 2025-07-26 09:47:02,220 basehttp 1904 23016 "GET /api/docs/ HTTP/1.1" 200 13842
[ERROR] 2025-07-26 09:47:02,793 log 1904 23016 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 91, in get_operation
    parameters = self._get_parameters()
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 259, in _get_parameters
    **dict_helper(self._get_filter_parameters()),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 545, in _get_filter_parameters
    parameters += filter_extension.get_schema_operation_parameters(self)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\contrib\django_filters.py", line 58, in get_schema_operation_parameters
    filterset_class = self.target.get_filterset_class(auto_schema.view, get_manager(model).none())
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\rest_framework\backends.py", line 49, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\filterset.py", line 82, in __new__
    new_class.base_filters = new_class.get_filters()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\filterset.py", line 371, in get_filters
    raise TypeError(
TypeError: 'Meta.fields' must not contain non-model field names: method
[ERROR] 2025-07-26 09:47:02,900 basehttp 1904 23016 "GET /api/schema/ HTTP/1.1" 500 174022
[ERROR] 2025-07-26 09:47:03,072 log 1904 23016 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 91, in get_operation
    parameters = self._get_parameters()
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 259, in _get_parameters
    **dict_helper(self._get_filter_parameters()),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 545, in _get_filter_parameters
    parameters += filter_extension.get_schema_operation_parameters(self)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\contrib\django_filters.py", line 58, in get_schema_operation_parameters
    filterset_class = self.target.get_filterset_class(auto_schema.view, get_manager(model).none())
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\rest_framework\backends.py", line 49, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\filterset.py", line 82, in __new__
    new_class.base_filters = new_class.get_filters()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\filterset.py", line 371, in get_filters
    raise TypeError(
TypeError: 'Meta.fields' must not contain non-model field names: method
[ERROR] 2025-07-26 09:47:03,115 basehttp 1904 23016 "GET /api/schema/ HTTP/1.1" 500 174022
[INFO] 2025-07-26 09:47:03,426 basehttp 1904 23016 "GET /__debug__/history_sidebar/?store_id=aa91c588126c420191a8d57774af2268 HTTP/1.1" 200 9487
[INFO] 2025-07-26 09:57:08,778 autoreload 22632 22376 Watching for file changes with StatReloader
[INFO] 2025-07-26 09:57:15,636 basehttp 22632 9336 "GET /admin/users/user/add/ HTTP/1.1" 200 43991
[INFO] 2025-07-26 09:57:15,664 basehttp 22632 16472 "GET /static/admin/js/calendar.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:57:15,664 basehttp 22632 18900 "GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:57:15,665 basehttp 22632 18760 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:57:15,742 basehttp 22632 18760 "GET /static/admin/js/change_form.js HTTP/1.1" 304 0
[INFO] 2025-07-26 09:57:15,847 basehttp 22632 9336 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 09:57:15,948 basehttp 22632 9336 "GET /static/admin/img/icon-calendar.svg HTTP/1.1" 304 0
[INFO] 2025-07-26 09:57:19,629 basehttp 22632 9336 "GET /api/docs/ HTTP/1.1" 200 13842
[ERROR] 2025-07-26 09:57:19,977 log 22632 9336 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 91, in get_operation
    parameters = self._get_parameters()
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 259, in _get_parameters
    **dict_helper(self._get_filter_parameters()),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 545, in _get_filter_parameters
    parameters += filter_extension.get_schema_operation_parameters(self)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\contrib\django_filters.py", line 58, in get_schema_operation_parameters
    filterset_class = self.target.get_filterset_class(auto_schema.view, get_manager(model).none())
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\rest_framework\backends.py", line 49, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\filterset.py", line 82, in __new__
    new_class.base_filters = new_class.get_filters()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\filterset.py", line 371, in get_filters
    raise TypeError(
TypeError: 'Meta.fields' must not contain non-model field names: method
[ERROR] 2025-07-26 09:57:20,027 basehttp 22632 9336 "GET /api/schema/ HTTP/1.1" 500 174022
[ERROR] 2025-07-26 09:57:20,208 log 22632 9336 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "F:\zmkj-system\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 84, in get
    return self._get_schema_response(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 281, in get_schema
    paths=self.parse(request, public),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\generators.py", line 252, in parse
    operation = view.schema.get_operation(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 91, in get_operation
    parameters = self._get_parameters()
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 259, in _get_parameters
    **dict_helper(self._get_filter_parameters()),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\openapi.py", line 545, in _get_filter_parameters
    parameters += filter_extension.get_schema_operation_parameters(self)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\drf_spectacular\contrib\django_filters.py", line 58, in get_schema_operation_parameters
    filterset_class = self.target.get_filterset_class(auto_schema.view, get_manager(model).none())
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\rest_framework\backends.py", line 49, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\filterset.py", line 82, in __new__
    new_class.base_filters = new_class.get_filters()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django_filters\filterset.py", line 371, in get_filters
    raise TypeError(
TypeError: 'Meta.fields' must not contain non-model field names: method
[ERROR] 2025-07-26 09:57:20,302 basehttp 22632 9336 "GET /api/schema/ HTTP/1.1" 500 174022
[INFO] 2025-07-26 09:57:20,652 basehttp 22632 9336 "GET /__debug__/history_sidebar/?store_id=f7176dff170549b39dbe158f4e96537a HTTP/1.1" 200 9487
[INFO] 2025-07-26 10:03:28,127 autoreload 20076 24412 Watching for file changes with StatReloader
[INFO] 2025-07-26 10:03:31,991 basehttp 20076 1944 "GET /api/docs/ HTTP/1.1" 200 13842
[INFO] 2025-07-26 10:03:32,531 basehttp 20076 1944 "GET /api/schema/ HTTP/1.1" 200 263198
[INFO] 2025-07-26 10:03:40,596 basehttp 20076 1944 "GET /admin/users/user/add/ HTTP/1.1" 200 43991
[INFO] 2025-07-26 10:03:40,768 basehttp 20076 1944 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 10:03:43,216 basehttp 20076 1944 "GET /admin/products/product/ HTTP/1.1" 200 27110
[INFO] 2025-07-26 10:03:43,450 basehttp 20076 1944 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 10:03:49,520 basehttp 20076 1944 "GET /api/schema/ HTTP/1.1" 200 134459
[WARNING] 2025-07-26 10:03:55,354 log 20076 1944 Not Found: /api/
[WARNING] 2025-07-26 10:03:55,355 basehttp 20076 1944 "GET /api/ HTTP/1.1" 404 13145
[INFO] 2025-07-26 10:04:05,591 basehttp 20076 1944 "GET /admin/ HTTP/1.1" 200 25286
[INFO] 2025-07-26 10:04:07,433 basehttp 20076 1944 "GET /admin/switch_theme/dark/ HTTP/1.1" 302 0
[INFO] 2025-07-26 10:04:07,706 basehttp 20076 1944 "GET /admin/ HTTP/1.1" 200 25283
[INFO] 2025-07-26 10:04:12,379 basehttp 20076 1944 "GET /admin/switch_theme/blue/ HTTP/1.1" 302 0
[INFO] 2025-07-26 10:04:12,582 basehttp 20076 1944 "GET /admin/ HTTP/1.1" 200 25283
[INFO] 2025-07-26 10:04:19,808 basehttp 20076 1944 "GET /admin/switch_theme/dark/ HTTP/1.1" 302 0
[INFO] 2025-07-26 10:04:20,024 basehttp 20076 1944 "GET /admin/ HTTP/1.1" 200 25283
[INFO] 2025-07-26 10:08:36,481 autoreload 21784 23604 Watching for file changes with StatReloader
[INFO] 2025-07-26 10:08:41,024 basehttp 21784 23964 "GET /admin/ HTTP/1.1" 200 25276
[INFO] 2025-07-26 10:08:43,725 basehttp 21784 23964 "GET /admin/switch_theme/cool-black/ HTTP/1.1" 302 0
[INFO] 2025-07-26 10:08:43,937 basehttp 21784 23964 "GET /admin/ HTTP/1.1" 200 25290
[INFO] 2025-07-26 10:08:47,579 basehttp 21784 23964 "GET /admin/switch_theme/default/ HTTP/1.1" 302 0
[INFO] 2025-07-26 10:08:47,785 basehttp 21784 23964 "GET /admin/ HTTP/1.1" 200 25287
[INFO] 2025-07-26 10:08:51,540 basehttp 21784 23964 "GET /admin/switch_theme/cool-black/ HTTP/1.1" 302 0
[INFO] 2025-07-26 10:08:51,753 basehttp 21784 23964 "GET /admin/ HTTP/1.1" 200 25290
[INFO] 2025-07-26 10:08:53,324 basehttp 21784 23964 "GET /admin/switch_theme/elegant-blue/ HTTP/1.1" 302 0
[INFO] 2025-07-26 10:08:53,622 basehttp 21784 23964 "GET /admin/ HTTP/1.1" 200 25292
[INFO] 2025-07-26 10:08:55,401 basehttp 21784 23964 "GET /admin/switch_theme/soft-green/ HTTP/1.1" 302 0
[INFO] 2025-07-26 10:08:55,683 basehttp 21784 23964 "GET /admin/ HTTP/1.1" 200 25290
[INFO] 2025-07-26 10:08:57,215 basehttp 21784 23964 "GET /admin/ HTTP/1.1" 200 25290
[INFO] 2025-07-26 10:08:57,235 basehttp 21784 22856 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
[INFO] 2025-07-26 10:08:57,236 basehttp 21784 5668 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
[INFO] 2025-07-26 10:08:57,236 basehttp 21784 22088 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
[INFO] 2025-07-26 10:08:57,237 basehttp 21784 15460 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
[INFO] 2025-07-26 10:08:57,237 basehttp 21784 23964 "GET /static/admin/css/base.css HTTP/1.1" 200 22120
[INFO] 2025-07-26 10:08:57,238 basehttp 21784 20056 "GET /static/admin/css/responsive.css HTTP/1.1" 200 659
[INFO] 2025-07-26 10:08:57,239 basehttp 21784 22856 "GET /static/admin/css/themes.css HTTP/1.1" 200 2524
[INFO] 2025-07-26 10:08:57,240 basehttp 21784 5668 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 200 12621
[INFO] 2025-07-26 10:08:57,241 basehttp 21784 22088 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 200 13404
[INFO] 2025-07-26 10:08:57,242 basehttp 21784 22856 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
[INFO] 2025-07-26 10:08:57,243 basehttp 21784 5668 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 200 43
[INFO] 2025-07-26 10:08:57,247 basehttp 21784 5668 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
[INFO] 2025-07-26 10:08:57,247 basehttp 21784 22856 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
[INFO] 2025-07-26 10:08:57,247 basehttp 21784 22088 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
[INFO] 2025-07-26 10:08:57,257 basehttp 21784 22088 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 200 4527
[WARNING] 2025-07-26 10:08:57,436 log 21784 22088 Not Found: /favicon.ico
[WARNING] 2025-07-26 10:08:57,436 basehttp 21784 22088 "GET /favicon.ico HTTP/1.1" 404 13173
[INFO] 2025-07-26 10:09:00,024 basehttp 21784 22088 "GET /admin/switch_theme/cool-black/ HTTP/1.1" 302 0
[INFO] 2025-07-26 10:09:00,316 basehttp 21784 22088 "GET /admin/ HTTP/1.1" 200 25290
[INFO] 2025-07-26 10:09:04,042 basehttp 21784 22088 "GET /admin/switch_theme/default/ HTTP/1.1" 302 0
[INFO] 2025-07-26 10:09:04,249 basehttp 21784 22088 "GET /admin/ HTTP/1.1" 200 25287
[INFO] 2025-07-26 10:09:04,266 basehttp 21784 22088 "GET /static/admin/css/themes.css HTTP/1.1" 304 0
[INFO] 2025-07-26 10:09:05,958 basehttp 21784 22088 "GET /admin/switch_theme/elegant-blue/ HTTP/1.1" 302 0
[INFO] 2025-07-26 10:09:06,184 basehttp 21784 22088 "GET /admin/ HTTP/1.1" 200 25292
[INFO] 2025-07-26 10:09:08,504 basehttp 21784 22088 "GET /admin/switch_theme/soft-green/ HTTP/1.1" 302 0
[INFO] 2025-07-26 10:09:08,727 basehttp 21784 22088 "GET /admin/ HTTP/1.1" 200 25290
[INFO] 2025-07-26 10:09:10,918 basehttp 21784 22088 "GET /admin/products/productcategory/ HTTP/1.1" 200 26714
[INFO] 2025-07-26 10:09:11,151 basehttp 21784 22088 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 15:23:14,771 autoreload 7484 5636 Watching for file changes with StatReloader
[WARNING] 2025-07-26 15:23:22,051 log 7484 20860 Not Found: /
[WARNING] 2025-07-26 15:23:22,052 basehttp 7484 20860 "GET / HTTP/1.1" 404 13112
[INFO] 2025-07-26 15:23:22,080 basehttp 7484 20860 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
[INFO] 2025-07-26 15:23:22,081 basehttp 7484 21236 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
[INFO] 2025-07-26 15:23:22,151 basehttp 7484 21236 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
[INFO] 2025-07-26 15:23:22,172 basehttp 7484 21236 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
[WARNING] 2025-07-26 15:23:22,362 log 7484 21236 Not Found: /favicon.ico
[WARNING] 2025-07-26 15:23:22,363 basehttp 7484 21236 "GET /favicon.ico HTTP/1.1" 404 13173
[INFO] 2025-07-26 15:23:27,855 basehttp 7484 21236 "GET /admin/ HTTP/1.1" 200 25290
[INFO] 2025-07-26 15:23:27,864 basehttp 7484 21236 "GET /static/admin/css/base.css HTTP/1.1" 304 0
[INFO] 2025-07-26 15:23:27,865 basehttp 7484 23160 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
[INFO] 2025-07-26 15:23:27,877 basehttp 7484 20860 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
[INFO] 2025-07-26 15:23:27,942 basehttp 7484 20860 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
[INFO] 2025-07-26 15:23:27,943 basehttp 7484 21236 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
[INFO] 2025-07-26 15:23:27,943 basehttp 7484 23160 "GET /static/admin/css/dashboard.css HTTP/1.1" 304 0
[INFO] 2025-07-26 15:23:27,949 basehttp 7484 23160 "GET /static/admin/css/themes.css HTTP/1.1" 304 0
[INFO] 2025-07-26 15:23:27,953 basehttp 7484 23160 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
[INFO] 2025-07-26 15:23:27,966 basehttp 7484 23160 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
[INFO] 2025-07-26 15:23:27,966 basehttp 7484 21236 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
[INFO] 2025-07-26 15:23:27,997 basehttp 7484 21236 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
[INFO] 2025-07-26 15:23:34,845 basehttp 7484 21236 "GET /api/docs/ HTTP/1.1" 200 13842
[INFO] 2025-07-26 15:23:35,510 basehttp 7484 21236 "GET /api/schema/ HTTP/1.1" 200 263198
[INFO] 2025-07-26 15:33:30,430 autoreload 7484 5636 F:\zmkj-system\backend\zmkj\settings\base.py changed, reloading.
[INFO] 2025-07-26 15:33:31,014 autoreload 20792 21128 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:33:47,983 autoreload 20792 21128 F:\zmkj-system\backend\zmkj\urls.py changed, reloading.
[INFO] 2025-07-26 15:33:48,384 autoreload 14420 22976 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:34:43,193 autoreload 5244 13144 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:35:16,552 autoreload 5244 13144 F:\zmkj-system\backend\apps\wechat\official.py changed, reloading.
[INFO] 2025-07-26 15:35:16,949 autoreload 22564 5336 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:35:34,790 autoreload 22564 5336 F:\zmkj-system\backend\apps\wechat\official.py changed, reloading.
[INFO] 2025-07-26 15:35:35,182 autoreload 23048 18112 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:35:44,752 autoreload 23048 18112 F:\zmkj-system\backend\apps\wechat\official.py changed, reloading.
[INFO] 2025-07-26 15:35:45,144 autoreload 17920 10564 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:35:55,736 autoreload 17920 10564 F:\zmkj-system\backend\apps\wechat\official.py changed, reloading.
[INFO] 2025-07-26 15:35:56,121 autoreload 3008 18576 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:36:43,913 autoreload 3008 18576 F:\zmkj-system\backend\apps\wechat\miniprogram.py changed, reloading.
[INFO] 2025-07-26 15:36:44,315 autoreload 6288 22684 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:37:08,536 autoreload 6288 22684 F:\zmkj-system\backend\apps\wechat\miniprogram.py changed, reloading.
[INFO] 2025-07-26 15:37:08,924 autoreload 22824 14068 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:37:26,950 autoreload 22824 14068 F:\zmkj-system\backend\apps\wechat\miniprogram.py changed, reloading.
[INFO] 2025-07-26 15:37:27,352 autoreload 10028 18872 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:37:55,744 autoreload 10028 18872 F:\zmkj-system\backend\apps\wechat\auth.py changed, reloading.
[INFO] 2025-07-26 15:37:56,155 autoreload 7524 3020 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:38:16,210 autoreload 7524 3020 F:\zmkj-system\backend\apps\wechat\official.py changed, reloading.
[INFO] 2025-07-26 15:38:16,620 autoreload 17276 1680 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:39:26,224 autoreload 22784 7468 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:39:29,395 basehttp 22784 23444 "GET /api/docs/ HTTP/1.1" 200 13842
[INFO] 2025-07-26 15:39:29,997 basehttp 22784 23444 "GET /api/schema/ HTTP/1.1" 200 335591
[INFO] 2025-07-26 15:39:32,181 autoreload 22784 7468 F:\zmkj-system\backend\apps\wechat\miniprogram.py changed, reloading.
[INFO] 2025-07-26 15:39:32,678 autoreload 14008 12152 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:40:09,360 autoreload 14008 12152 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 15:40:09,761 autoreload 18136 4724 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:40:20,547 autoreload 18136 4724 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 15:40:20,935 autoreload 21284 5112 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:41:11,857 basehttp 21284 18204 "GET /api/docs/ HTTP/1.1" 200 13842
[INFO] 2025-07-26 15:41:12,439 basehttp 21284 18204 "GET /api/schema/ HTTP/1.1" 200 335591
[INFO] 2025-07-26 15:42:42,872 autoreload 12860 2032 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:42:47,871 basehttp 12860 17772 "GET /api/docs/ HTTP/1.1" 200 13842
[INFO] 2025-07-26 15:42:48,446 basehttp 12860 17772 "GET /api/schema/ HTTP/1.1" 200 335591
[INFO] 2025-07-26 15:43:02,445 basehttp 12860 17772 "GET /admin/ HTTP/1.1" 200 25290
[INFO] 2025-07-26 15:43:07,385 basehttp 12860 17772 "GET /admin/ HTTP/1.1" 200 25290
[INFO] 2025-07-26 15:45:26,381 autoreload 12860 2032 F:\zmkj-system\backend\apps\wechat\models.py changed, reloading.
[INFO] 2025-07-26 15:45:26,923 autoreload 22068 21436 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:46:06,726 autoreload 22068 21436 F:\zmkj-system\backend\apps\wechat\models.py changed, reloading.
[INFO] 2025-07-26 15:46:07,124 autoreload 9868 19184 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:47:57,420 autoreload 9868 19184 F:\zmkj-system\backend\apps\wechat\admin.py changed, reloading.
[INFO] 2025-07-26 15:47:57,815 autoreload 17164 18752 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:48:24,170 autoreload 17164 18752 F:\zmkj-system\backend\apps\wechat\admin.py changed, reloading.
[INFO] 2025-07-26 15:48:24,571 autoreload 13620 8628 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:48:36,405 autoreload 13620 8628 F:\zmkj-system\backend\apps\wechat\auth.py changed, reloading.
[INFO] 2025-07-26 15:48:36,799 autoreload 13428 10340 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:48:49,684 autoreload 13428 10340 F:\zmkj-system\backend\apps\wechat\auth.py changed, reloading.
[INFO] 2025-07-26 15:48:50,086 autoreload 9840 4704 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:49:07,108 autoreload 9840 4704 F:\zmkj-system\backend\apps\wechat\official.py changed, reloading.
[INFO] 2025-07-26 15:49:07,508 autoreload 4908 2104 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:49:20,378 autoreload 4908 2104 F:\zmkj-system\backend\apps\wechat\official.py changed, reloading.
[INFO] 2025-07-26 15:49:20,771 autoreload 12276 15264 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:49:36,833 autoreload 12276 15264 F:\zmkj-system\backend\apps\wechat\work.py changed, reloading.
[INFO] 2025-07-26 15:49:37,239 autoreload 18800 6232 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:49:49,090 autoreload 18800 6232 F:\zmkj-system\backend\apps\wechat\work.py changed, reloading.
[INFO] 2025-07-26 15:49:49,508 autoreload 19192 2776 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:50:00,334 autoreload 19192 2776 F:\zmkj-system\backend\apps\wechat\miniprogram.py changed, reloading.
[INFO] 2025-07-26 15:50:00,753 autoreload 2800 2452 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:50:13,601 autoreload 2800 2452 F:\zmkj-system\backend\apps\wechat\miniprogram.py changed, reloading.
[INFO] 2025-07-26 15:50:13,986 autoreload 23104 1124 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:50:27,172 basehttp 23104 3176 "GET /admin/ HTTP/1.1" 200 25290
[INFO] 2025-07-26 15:50:33,460 basehttp 23104 3176 "GET /admin/switch_theme/cool-black/ HTTP/1.1" 302 0
[INFO] 2025-07-26 15:50:33,684 basehttp 23104 3176 "GET /admin/ HTTP/1.1" 200 25291
[INFO] 2025-07-26 15:50:42,295 basehttp 23104 3176 "GET /admin/switch_theme/elegant-blue/ HTTP/1.1" 302 0
[INFO] 2025-07-26 15:50:42,509 basehttp 23104 3176 "GET /admin/ HTTP/1.1" 200 25292
[INFO] 2025-07-26 15:50:44,990 basehttp 23104 3176 "GET /admin/switch_theme/cool-black/ HTTP/1.1" 302 0
[INFO] 2025-07-26 15:50:45,187 basehttp 23104 3176 "GET /admin/ HTTP/1.1" 200 25290
[INFO] 2025-07-26 15:50:50,236 autoreload 23104 1124 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 15:50:50,798 autoreload 1508 19064 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:50:52,046 basehttp 1508 18608 "GET /admin/switch_theme/elegant-blue/ HTTP/1.1" 302 0
[INFO] 2025-07-26 15:50:52,328 basehttp 1508 18608 "GET /admin/ HTTP/1.1" 200 25292
[INFO] 2025-07-26 15:50:54,285 basehttp 1508 18608 "GET /admin/switch_theme/soft-green/ HTTP/1.1" 302 0
[INFO] 2025-07-26 15:50:54,541 basehttp 1508 18608 "GET /admin/ HTTP/1.1" 200 25290
[INFO] 2025-07-26 15:50:56,779 basehttp 1508 18608 "GET /admin/switch_theme/cool-black/ HTTP/1.1" 302 0
[INFO] 2025-07-26 15:50:57,016 basehttp 1508 18608 "GET /admin/ HTTP/1.1" 200 25290
[INFO] 2025-07-26 15:50:58,189 basehttp 1508 18608 "GET /admin/switch_theme/default/ HTTP/1.1" 302 0
[INFO] 2025-07-26 15:50:58,388 basehttp 1508 18608 "GET /admin/ HTTP/1.1" 200 25287
[INFO] 2025-07-26 15:51:02,837 autoreload 1508 19064 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 15:51:03,443 autoreload 23244 4480 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:51:14,253 autoreload 23244 4480 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 15:51:14,699 autoreload 11916 12824 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:52:49,414 autoreload 22040 15796 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:52:52,992 basehttp 22040 764 "GET /admin/ HTTP/1.1" 200 25287
[INFO] 2025-07-26 15:52:57,085 basehttp 22040 764 "GET /admin/ HTTP/1.1" 200 25287
[INFO] 2025-07-26 15:52:57,105 basehttp 22040 10620 "GET /static/admin/css/responsive.css HTTP/1.1" 200 659
[INFO] 2025-07-26 15:52:57,106 basehttp 22040 23096 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
[INFO] 2025-07-26 15:52:57,108 basehttp 22040 10620 "GET /static/admin/css/themes.css HTTP/1.1" 200 2524
[INFO] 2025-07-26 15:52:57,115 basehttp 22040 7936 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
[INFO] 2025-07-26 15:52:57,115 basehttp 22040 764 "GET /static/admin/css/base.css HTTP/1.1" 200 22120
[INFO] 2025-07-26 15:52:57,116 basehttp 22040 18512 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
[INFO] 2025-07-26 15:52:57,126 basehttp 22040 15208 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
[INFO] 2025-07-26 15:52:57,130 basehttp 22040 18512 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 200 43
[INFO] 2025-07-26 15:52:57,133 basehttp 22040 18512 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
[INFO] 2025-07-26 15:52:57,134 basehttp 22040 7936 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
[INFO] 2025-07-26 15:52:57,135 basehttp 22040 764 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
[INFO] 2025-07-26 15:52:57,137 basehttp 22040 10620 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 200 13404
[INFO] 2025-07-26 15:52:57,142 basehttp 22040 15208 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
[INFO] 2025-07-26 15:52:57,158 basehttp 22040 23096 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 200 12621
[INFO] 2025-07-26 15:52:57,168 basehttp 22040 23096 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 200 4527
[WARNING] 2025-07-26 15:52:57,354 log 22040 23096 Not Found: /favicon.ico
[WARNING] 2025-07-26 15:52:57,354 basehttp 22040 23096 "GET /favicon.ico HTTP/1.1" 404 13330
[INFO] 2025-07-26 15:53:17,945 basehttp 22040 23096 "GET /admin/ HTTP/1.1" 200 25287
[INFO] 2025-07-26 15:53:17,961 basehttp 22040 10620 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
[INFO] 2025-07-26 15:53:17,961 basehttp 22040 764 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
[INFO] 2025-07-26 15:53:17,961 basehttp 22040 7936 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
[INFO] 2025-07-26 15:53:17,961 basehttp 22040 18512 "GET /static/admin/css/responsive.css HTTP/1.1" 200 659
[INFO] 2025-07-26 15:53:17,962 basehttp 22040 15208 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
[INFO] 2025-07-26 15:53:17,962 basehttp 22040 23096 "GET /static/admin/css/base.css HTTP/1.1" 200 22120
[INFO] 2025-07-26 15:53:17,965 basehttp 22040 10620 "GET /static/admin/css/themes.css HTTP/1.1" 200 2524
[INFO] 2025-07-26 15:53:17,966 basehttp 22040 18512 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 200 13404
[INFO] 2025-07-26 15:53:17,966 basehttp 22040 764 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 200 12621
[INFO] 2025-07-26 15:53:17,972 basehttp 22040 18512 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
[INFO] 2025-07-26 15:53:17,972 basehttp 22040 764 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 200 43
[INFO] 2025-07-26 15:53:17,976 basehttp 22040 764 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
[INFO] 2025-07-26 15:53:17,976 basehttp 22040 10620 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
[INFO] 2025-07-26 15:53:17,976 basehttp 22040 18512 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
[INFO] 2025-07-26 15:53:17,986 basehttp 22040 18512 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 200 4527
[WARNING] 2025-07-26 15:53:18,159 log 22040 18512 Not Found: /favicon.ico
[WARNING] 2025-07-26 15:53:18,160 basehttp 22040 18512 "GET /favicon.ico HTTP/1.1" 404 13330
[INFO] 2025-07-26 15:54:05,004 basehttp 22040 18512 "GET /admin/ HTTP/1.1" 200 25287
[INFO] 2025-07-26 15:57:44,244 autoreload 7600 3352 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:57:49,280 basehttp 7600 10868 "GET /admin/ HTTP/1.1" 200 25287
[INFO] 2025-07-26 15:57:49,300 basehttp 7600 15472 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
[INFO] 2025-07-26 15:57:49,300 basehttp 7600 10868 "GET /static/admin/css/base.css HTTP/1.1" 200 22120
[INFO] 2025-07-26 15:57:49,301 basehttp 7600 5864 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
[INFO] 2025-07-26 15:57:49,302 basehttp 7600 23120 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
[INFO] 2025-07-26 15:57:49,302 basehttp 7600 22776 "GET /static/admin/css/responsive.css HTTP/1.1" 200 659
[INFO] 2025-07-26 15:57:49,303 basehttp 7600 4704 "GET /static/admin/css/themes.css HTTP/1.1" 200 2524
[INFO] 2025-07-26 15:57:49,306 basehttp 7600 15472 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
[INFO] 2025-07-26 15:57:49,307 basehttp 7600 10868 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 200 12621
[INFO] 2025-07-26 15:57:49,307 basehttp 7600 5864 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 200 13404
[INFO] 2025-07-26 15:57:49,346 basehttp 7600 5864 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
[INFO] 2025-07-26 15:57:49,350 basehttp 7600 5864 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 200 43
[INFO] 2025-07-26 15:57:49,356 basehttp 7600 5864 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
[INFO] 2025-07-26 15:57:49,356 basehttp 7600 15472 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
[INFO] 2025-07-26 15:57:49,356 basehttp 7600 10868 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
[INFO] 2025-07-26 15:57:49,365 basehttp 7600 10868 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 200 4527
[WARNING] 2025-07-26 15:57:49,652 log 7600 10868 Not Found: /favicon.ico
[WARNING] 2025-07-26 15:57:49,653 basehttp 7600 10868 "GET /favicon.ico HTTP/1.1" 404 13330
[INFO] 2025-07-26 15:59:29,275 autoreload 5724 21900 Watching for file changes with StatReloader
[INFO] 2025-07-26 15:59:31,736 basehttp 5724 14832 "GET /admin/ HTTP/1.1" 200 25287
[INFO] 2025-07-26 15:59:31,805 basehttp 5724 13048 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
[INFO] 2025-07-26 15:59:31,805 basehttp 5724 18484 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
[INFO] 2025-07-26 15:59:31,806 basehttp 5724 1460 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
[INFO] 2025-07-26 15:59:31,806 basehttp 5724 21328 "GET /static/admin/css/responsive.css HTTP/1.1" 200 659
[INFO] 2025-07-26 15:59:31,806 basehttp 5724 4504 "GET /static/admin/css/themes.css HTTP/1.1" 200 2524
[INFO] 2025-07-26 15:59:31,807 basehttp 5724 14832 "GET /static/admin/css/base.css HTTP/1.1" 200 22120
[INFO] 2025-07-26 15:59:31,809 basehttp 5724 18484 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
[INFO] 2025-07-26 15:59:31,809 basehttp 5724 13048 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 200 12621
[INFO] 2025-07-26 15:59:31,810 basehttp 5724 1460 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 200 13404
[INFO] 2025-07-26 15:59:31,866 basehttp 5724 1460 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
[INFO] 2025-07-26 15:59:31,874 basehttp 5724 1460 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 200 43
[INFO] 2025-07-26 15:59:31,887 basehttp 5724 18484 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
[INFO] 2025-07-26 15:59:31,887 basehttp 5724 13048 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
[INFO] 2025-07-26 15:59:31,887 basehttp 5724 1460 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
[INFO] 2025-07-26 15:59:31,924 basehttp 5724 1460 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 200 4527
[WARNING] 2025-07-26 15:59:32,137 log 5724 1460 Not Found: /favicon.ico
[WARNING] 2025-07-26 15:59:32,138 basehttp 5724 1460 "GET /favicon.ico HTTP/1.1" 404 13330
[INFO] 2025-07-26 16:01:46,457 autoreload 9864 18136 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:01:49,091 basehttp 9864 3812 "GET /admin/ HTTP/1.1" 200 26401
[INFO] 2025-07-26 16:01:51,897 basehttp 9864 3812 "GET /admin/wechat/wechatconfig/ HTTP/1.1" 200 27667
[INFO] 2025-07-26 16:01:51,922 basehttp 9864 3812 "GET /static/admin/css/changelists.css HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:51,922 basehttp 9864 5192 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:51,925 basehttp 9864 5532 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:51,926 basehttp 9864 3812 "GET /static/admin/js/actions.js HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:51,926 basehttp 9864 5192 "GET /static/admin/js/urlify.js HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:51,929 basehttp 9864 5532 "GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:51,930 basehttp 9864 3812 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:51,933 basehttp 9864 2832 "GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:51,933 basehttp 9864 10840 "GET /static/admin/js/core.js HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:51,941 basehttp 9864 5192 "GET /static/admin/img/search.svg HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:51,944 basehttp 9864 5192 "GET /static/admin/js/filters.js HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:52,137 basehttp 9864 5876 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:01:52,153 basehttp 9864 5876 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:54,018 basehttp 9864 5876 "GET /admin/wechat/wechatconfig/add/ HTTP/1.1" 200 38255
[INFO] 2025-07-26 16:01:54,041 basehttp 9864 5876 "GET /static/admin/css/forms.css HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:54,041 basehttp 9864 10840 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:54,046 basehttp 9864 10840 "GET /static/admin/css/widgets.css HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:54,066 basehttp 9864 10840 "GET /static/admin/js/change_form.js HTTP/1.1" 304 0
[INFO] 2025-07-26 16:01:54,248 basehttp 9864 5192 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:04:19,333 basehttp 9864 5192 "POST /admin/wechat/wechatconfig/add/ HTTP/1.1" 302 0
[INFO] 2025-07-26 16:04:19,606 basehttp 9864 5192 "GET /admin/wechat/wechatconfig/add/ HTTP/1.1" 200 38522
[INFO] 2025-07-26 16:04:19,814 basehttp 9864 5192 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:04:19,830 basehttp 9864 5192 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 304 0
[INFO] 2025-07-26 16:04:40,252 basehttp 9864 5192 "POST /admin/wechat/wechatconfig/add/ HTTP/1.1" 302 0
[INFO] 2025-07-26 16:04:40,465 basehttp 9864 5192 "GET /admin/wechat/wechatconfig/add/ HTTP/1.1" 200 38522
[INFO] 2025-07-26 16:04:40,608 basehttp 9864 5192 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:05:40,916 basehttp 9864 5192 "POST /admin/wechat/wechatconfig/add/ HTTP/1.1" 302 0
[INFO] 2025-07-26 16:05:41,120 basehttp 9864 5192 "GET /admin/wechat/wechatconfig/add/ HTTP/1.1" 200 38525
[INFO] 2025-07-26 16:05:41,266 basehttp 9864 5192 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:05:44,234 basehttp 9864 5192 "GET /admin/wechat/wechatconfig/ HTTP/1.1" 200 31652
[INFO] 2025-07-26 16:05:44,369 basehttp 9864 5192 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:05:44,390 basehttp 9864 5192 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
[INFO] 2025-07-26 16:06:00,080 basehttp 9864 5192 "GET /admin/wechat/wechatconfig/ HTTP/1.1" 200 31652
[INFO] 2025-07-26 16:06:00,258 basehttp 9864 5192 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:06:05,321 basehttp 9864 5192 "GET /admin/wechat/wechatconfig/add/ HTTP/1.1" 200 38255
[INFO] 2025-07-26 16:06:05,456 basehttp 9864 5192 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:06:15,330 basehttp 9864 5192 "GET /admin/switch_theme/cool-black/ HTTP/1.1" 302 0
[INFO] 2025-07-26 16:06:15,665 basehttp 9864 5192 "GET /admin/wechat/wechatconfig/add/ HTTP/1.1" 200 38258
[INFO] 2025-07-26 16:06:15,941 basehttp 9864 5192 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:06:21,857 basehttp 9864 5192 "GET /admin/switch_theme/elegant-blue/ HTTP/1.1" 302 0
[INFO] 2025-07-26 16:06:22,061 basehttp 9864 5192 "GET /admin/wechat/wechatconfig/add/ HTTP/1.1" 200 38260
[INFO] 2025-07-26 16:06:22,205 basehttp 9864 5192 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:06:24,221 basehttp 9864 5192 "GET /admin/switch_theme/soft-green/ HTTP/1.1" 302 0
[INFO] 2025-07-26 16:06:24,418 basehttp 9864 5192 "GET /admin/wechat/wechatconfig/add/ HTTP/1.1" 200 38258
[INFO] 2025-07-26 16:06:24,758 basehttp 9864 5192 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:06:26,512 basehttp 9864 5192 "GET /admin/switch_theme/default/ HTTP/1.1" 302 0
[INFO] 2025-07-26 16:06:26,719 basehttp 9864 5192 "GET /admin/wechat/wechatconfig/add/ HTTP/1.1" 200 38255
[INFO] 2025-07-26 16:06:26,862 basehttp 9864 5192 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:06:39,105 basehttp 9864 5192 "GET /admin/ HTTP/1.1" 200 27579
[INFO] 2025-07-26 16:06:48,795 basehttp 9864 5192 "GET /admin/ HTTP/1.1" 200 27579
[INFO] 2025-07-26 16:12:02,649 autoreload 8064 17736 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:12:03,522 basehttp 8064 624 "GET /admin/wechat/wechatconfig/ HTTP/1.1" 200 33748
[INFO] 2025-07-26 16:12:03,670 basehttp 8064 624 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:12:20,769 basehttp 8064 624 "POST /admin/wechat/wechatconfig/ HTTP/1.1" 200 25474
[INFO] 2025-07-26 16:12:20,797 basehttp 8064 624 "GET /static/admin/js/cancel.js HTTP/1.1" 200 884
[INFO] 2025-07-26 16:12:22,533 basehttp 8064 624 "POST /admin/wechat/wechatconfig/ HTTP/1.1" 302 0
[INFO] 2025-07-26 16:12:22,804 basehttp 8064 624 "GET /admin/wechat/wechatconfig/ HTTP/1.1" 200 33361
[INFO] 2025-07-26 16:12:22,931 basehttp 8064 624 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:12:27,962 basehttp 8064 624 "POST /admin/wechat/wechatconfig/ HTTP/1.1" 200 25474
[INFO] 2025-07-26 16:12:29,095 basehttp 8064 624 "POST /admin/wechat/wechatconfig/ HTTP/1.1" 302 0
[INFO] 2025-07-26 16:12:29,373 basehttp 8064 624 "GET /admin/wechat/wechatconfig/ HTTP/1.1" 200 32834
[INFO] 2025-07-26 16:12:29,516 basehttp 8064 624 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:12:34,388 basehttp 8064 624 "POST /admin/wechat/wechatconfig/ HTTP/1.1" 200 25471
[INFO] 2025-07-26 16:12:35,643 basehttp 8064 624 "POST /admin/wechat/wechatconfig/ HTTP/1.1" 302 0
[INFO] 2025-07-26 16:12:35,852 basehttp 8064 624 "GET /admin/wechat/wechatconfig/ HTTP/1.1" 200 32313
[INFO] 2025-07-26 16:12:36,109 basehttp 8064 624 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:12:40,241 basehttp 8064 624 "POST /admin/wechat/wechatconfig/ HTTP/1.1" 200 25471
[INFO] 2025-07-26 16:12:41,496 basehttp 8064 624 "POST /admin/wechat/wechatconfig/ HTTP/1.1" 302 0
[INFO] 2025-07-26 16:12:41,823 basehttp 8064 624 "GET /admin/wechat/wechatconfig/ HTTP/1.1" 200 31792
[INFO] 2025-07-26 16:12:41,985 basehttp 8064 624 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:13:18,079 autoreload 8064 17736 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 16:13:18,961 autoreload 20288 1980 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:13:56,693 autoreload 20288 1980 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 16:13:57,112 autoreload 7756 11120 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:14:07,916 autoreload 7756 11120 F:\zmkj-system\backend\apps\wechat\serializers.py changed, reloading.
[INFO] 2025-07-26 16:14:08,328 autoreload 14212 13400 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:14:49,169 autoreload 14212 13400 F:\zmkj-system\backend\apps\wechat\serializers.py changed, reloading.
[INFO] 2025-07-26 16:14:49,574 autoreload 2068 4784 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:15:06,205 basehttp 2068 23204 "GET /admin/products/productattributevalue/ HTTP/1.1" 200 26936
[INFO] 2025-07-26 16:15:06,408 basehttp 2068 23204 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:15:09,535 basehttp 2068 23204 "GET /admin/products/productattributevalue/add/ HTTP/1.1" 200 31568
[INFO] 2025-07-26 16:15:09,558 basehttp 2068 22648 "GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 304 0
[INFO] 2025-07-26 16:15:09,558 basehttp 2068 19184 "GET /static/admin/js/calendar.js HTTP/1.1" 304 0
[INFO] 2025-07-26 16:15:09,697 basehttp 2068 23204 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-26 16:15:09,737 basehttp 2068 23204 "GET /static/admin/img/icon-calendar.svg HTTP/1.1" 304 0
[INFO] 2025-07-26 16:15:09,737 basehttp 2068 19184 "GET /static/admin/img/icon-clock.svg HTTP/1.1" 200 677
[INFO] 2025-07-26 16:15:17,769 basehttp 2068 19184 "GET /admin/ HTTP/1.1" 200 28894
[INFO] 2025-07-26 16:15:17,811 basehttp 2068 19184 "GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 304 0
[INFO] 2025-07-26 16:15:29,573 autoreload 2068 4784 F:\zmkj-system\backend\apps\wechat\urls.py changed, reloading.
[INFO] 2025-07-26 16:15:30,153 autoreload 17576 22556 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:19:54,835 autoreload 17576 22556 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 16:19:55,278 autoreload 19692 9700 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:20:06,905 autoreload 19692 9700 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 16:20:07,327 autoreload 4664 18772 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:20:18,957 autoreload 4664 18772 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 16:20:19,358 autoreload 5660 2924 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:20:36,180 autoreload 5660 2924 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 16:20:36,565 autoreload 23420 13344 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:20:52,337 autoreload 23420 13344 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 16:20:52,722 autoreload 18596 22264 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:21:07,452 autoreload 18596 22264 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 16:21:07,863 autoreload 17316 16152 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:21:22,579 autoreload 17316 16152 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 16:21:22,969 autoreload 11896 13424 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:21:37,706 autoreload 11896 13424 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 16:21:38,099 autoreload 13700 11952 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:22:23,837 autoreload 13700 11952 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 16:22:24,218 autoreload 22320 3480 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:22:36,888 autoreload 22320 3480 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 16:22:37,268 autoreload 10104 4500 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:22:47,866 autoreload 10104 4500 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 16:22:48,266 autoreload 23040 9508 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:22:59,886 autoreload 23040 9508 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 16:23:00,269 autoreload 17996 6300 Watching for file changes with StatReloader
[INFO] 2025-07-26 16:23:12,907 autoreload 17996 6300 F:\zmkj-system\backend\apps\wechat\views.py changed, reloading.
[INFO] 2025-07-26 16:23:13,293 autoreload 10884 1956 Watching for file changes with StatReloader
[WARNING] 2025-07-27 14:55:19,000 log 12680 1952 Bad Request: /api/v1/wechat/miniprogram/auth/
[WARNING] 2025-07-27 14:55:19,418 log 12680 1952 Forbidden: /api/v1/wechat/api/payment/create_order/
[WARNING] 2025-07-27 14:55:19,812 log 12680 1952 Forbidden: /api/v1/wechat/api/payment/create_order/
[WARNING] 2025-07-27 14:55:20,996 log 12680 1952 Bad Request: /api/v1/wechat/api/stats/user_stats/
[WARNING] 2025-07-27 14:58:34,048 log 23336 13908 Bad Request: /api/v1/wechat/api/payment/query_order/
[WARNING] 2025-07-27 15:00:09,403 log 2888 17052 Bad Request: /api/v1/wechat/miniprogram/auth/
[WARNING] 2025-07-27 15:00:10,195 log 2888 17052 Bad Request: /api/v1/wechat/api/payment/create_order/
[WARNING] 2025-07-27 15:00:11,365 log 2888 17052 Bad Request: /api/v1/wechat/api/stats/user_stats/
[WARNING] 2025-07-27 15:01:54,689 log 17392 18112 Bad Request: /api/v1/wechat/api/stats/user_stats/
[WARNING] 2025-07-27 15:04:14,416 log 2416 15392 Bad Request: /api/v1/wechat/miniprogram/auth/
[WARNING] 2025-07-27 15:04:15,203 log 2416 15392 Bad Request: /api/v1/wechat/api/payment/create_order/
[INFO] 2025-07-27 15:04:33,280 autoreload 8800 12856 Watching for file changes with StatReloader
[WARNING] 2025-07-27 15:04:36,265 log 8800 13852 Not Found: /
[WARNING] 2025-07-27 15:04:36,266 basehttp 8800 13852 "GET / HTTP/1.1" 404 13269
[INFO] 2025-07-27 15:04:36,299 basehttp 8800 13852 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:36,299 basehttp 8800 16048 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:36,368 basehttp 8800 16048 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:36,415 basehttp 8800 16048 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
[WARNING] 2025-07-27 15:04:36,590 log 8800 16048 Not Found: /favicon.ico
[WARNING] 2025-07-27 15:04:36,591 basehttp 8800 16048 "GET /favicon.ico HTTP/1.1" 404 13330
[INFO] 2025-07-27 15:04:40,519 basehttp 8800 10264 "GET /admin/ HTTP/1.1" 200 28893
[INFO] 2025-07-27 15:04:40,529 basehttp 8800 10264 "GET /static/admin/css/base.css HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:40,530 basehttp 8800 5664 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:40,545 basehttp 8800 1304 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:40,599 basehttp 8800 10264 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:40,599 basehttp 8800 5664 "GET /static/admin/css/dashboard.css HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:40,600 basehttp 8800 1304 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:40,605 basehttp 8800 1304 "GET /static/admin/css/themes.css HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:40,609 basehttp 8800 1304 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:40,622 basehttp 8800 1304 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:40,622 basehttp 8800 5664 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:40,658 basehttp 8800 1304 "GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:40,658 basehttp 8800 5664 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:44,619 basehttp 8800 5664 "GET /admin/wechat/wechatconfig/ HTTP/1.1" 200 31652
[INFO] 2025-07-27 15:04:44,639 basehttp 8800 5664 "GET /static/admin/css/changelists.css HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:44,640 basehttp 8800 10264 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:44,642 basehttp 8800 10400 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:44,643 basehttp 8800 5664 "GET /static/admin/js/actions.js HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:44,644 basehttp 8800 10264 "GET /static/admin/js/urlify.js HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:44,646 basehttp 8800 10400 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:44,649 basehttp 8800 5664 "GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:44,653 basehttp 8800 396 "GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:44,654 basehttp 8800 16904 "GET /static/admin/js/core.js HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:44,656 basehttp 8800 396 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:44,669 basehttp 8800 16904 "GET /static/admin/img/search.svg HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:44,673 basehttp 8800 16904 "GET /static/admin/js/filters.js HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:44,813 basehttp 8800 1304 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-27 15:04:44,829 basehttp 8800 16904 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:44,829 basehttp 8800 1304 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:47,279 basehttp 8800 16904 "GET /admin/wechat/wechatconfig/3/change/ HTTP/1.1" 200 38640
[INFO] 2025-07-27 15:04:47,299 basehttp 8800 16904 "GET /static/admin/css/forms.css HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:47,299 basehttp 8800 396 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:47,306 basehttp 8800 396 "GET /static/admin/css/widgets.css HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:47,328 basehttp 8800 396 "GET /static/admin/js/change_form.js HTTP/1.1" 304 0
[INFO] 2025-07-27 15:04:47,529 basehttp 8800 1304 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-27 15:05:18,842 basehttp 8800 1304 "GET /admin/wechat/wechatconfig/ HTTP/1.1" 200 31653
[INFO] 2025-07-27 15:05:19,082 basehttp 8800 1304 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-27 15:05:21,266 basehttp 8800 1304 "GET /admin/wechat/wechatconfig/1/change/ HTTP/1.1" 200 38703
[INFO] 2025-07-27 15:05:21,449 basehttp 8800 1304 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-27 15:05:42,481 basehttp 8800 1304 "GET /admin/wechat/wechatconfig/ HTTP/1.1" 200 31652
[INFO] 2025-07-27 15:05:42,644 basehttp 8800 1304 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-27 15:05:44,676 basehttp 8800 1304 "GET /admin/wechat/wechatconfig/2/change/ HTTP/1.1" 200 38595
[INFO] 2025-07-27 15:05:44,934 basehttp 8800 1304 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[WARNING] 2025-07-27 15:42:06,903 log 5532 8444 Bad Request: /api/v1/distribution/join/
[WARNING] 2025-07-27 15:43:12,107 log 1940 21856 Bad Request: /api/v1/distribution/join/
[WARNING] 2025-07-27 15:43:43,157 log 16476 23356 Bad Request: /api/v1/distribution/join/
[INFO] 2025-07-27 15:44:14,665 autoreload 4516 23116 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:44:29,748 basehttp 4516 7156 "GET /admin/ HTTP/1.1" 200 28893
[INFO] 2025-07-27 15:44:35,110 basehttp 4516 7156 "GET /admin/ HTTP/1.1" 200 28893
[WARNING] 2025-07-27 15:46:49,033 log 17228 6252 Bad Request: /api/v1/distribution/join/
[WARNING] 2025-07-27 15:47:21,856 log 8144 18012 Bad Request: /api/v1/distribution/join/
[INFO] 2025-07-27 15:47:41,694 autoreload 4516 23116 F:\zmkj-system\backend\apps\distribution\signals.py changed, reloading.
[INFO] 2025-07-27 15:47:42,188 autoreload 3940 9576 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:50:27,074 basehttp 3940 6296 "GET /admin/ HTTP/1.1" 200 28893
[INFO] 2025-07-27 15:50:31,136 basehttp 3940 6296 "GET /admin/ HTTP/1.1" 200 28893
[INFO] 2025-07-27 15:51:55,606 autoreload 3940 9576 F:\zmkj-system\backend\apps\distribution\admin.py changed, reloading.
[INFO] 2025-07-27 15:51:56,118 autoreload 16660 19796 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:52:13,146 autoreload 16660 19796 F:\zmkj-system\backend\apps\distribution\admin.py changed, reloading.
[INFO] 2025-07-27 15:52:13,568 autoreload 2528 20672 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:52:17,753 basehttp 2528 4388 "GET /admin/ HTTP/1.1" 200 28893
[WARNING] 2025-07-27 15:52:18,035 log 2528 4388 Not Found: /favicon.ico
[WARNING] 2025-07-27 15:52:18,036 basehttp 2528 4388 "GET /favicon.ico HTTP/1.1" 404 13493
[INFO] 2025-07-27 15:52:22,289 basehttp 2528 4388 "GET /admin/ HTTP/1.1" 200 28893
[INFO] 2025-07-27 15:52:22,308 basehttp 2528 5280 "GET /static/admin/css/responsive.css HTTP/1.1" 200 659
[INFO] 2025-07-27 15:52:22,308 basehttp 2528 12772 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
[INFO] 2025-07-27 15:52:22,309 basehttp 2528 12020 "GET /static/admin/css/themes.css HTTP/1.1" 200 2524
[INFO] 2025-07-27 15:52:22,320 basehttp 2528 23004 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
[INFO] 2025-07-27 15:52:22,320 basehttp 2528 14240 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
[INFO] 2025-07-27 15:52:22,321 basehttp 2528 4388 "GET /static/admin/css/base.css HTTP/1.1" 200 22120
[INFO] 2025-07-27 15:52:22,330 basehttp 2528 5280 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 200 12621
[INFO] 2025-07-27 15:52:22,339 basehttp 2528 12772 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
[INFO] 2025-07-27 15:52:22,344 basehttp 2528 5280 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 200 43
[INFO] 2025-07-27 15:52:22,351 basehttp 2528 4388 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
[INFO] 2025-07-27 15:52:22,351 basehttp 2528 5280 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
[INFO] 2025-07-27 15:52:22,351 basehttp 2528 14240 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
[INFO] 2025-07-27 15:52:22,351 basehttp 2528 12020 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 200 13404
[INFO] 2025-07-27 15:52:22,352 basehttp 2528 23004 "GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 200 392
[INFO] 2025-07-27 15:52:22,356 basehttp 2528 12772 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
[INFO] 2025-07-27 15:52:22,365 basehttp 2528 12772 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 200 4527
[WARNING] 2025-07-27 15:52:22,516 log 2528 12772 Not Found: /favicon.ico
[WARNING] 2025-07-27 15:52:22,516 basehttp 2528 12772 "GET /favicon.ico HTTP/1.1" 404 13493
[INFO] 2025-07-27 15:52:24,933 autoreload 2528 20672 F:\zmkj-system\backend\apps\distribution\admin.py changed, reloading.
[INFO] 2025-07-27 15:52:25,465 autoreload 3192 18336 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:52:39,379 autoreload 3192 18336 F:\zmkj-system\backend\apps\distribution\admin.py changed, reloading.
[INFO] 2025-07-27 15:52:39,815 autoreload 7212 11872 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:52:49,372 basehttp 7212 15204 "GET /admin/ HTTP/1.1" 200 28893
[INFO] 2025-07-27 15:52:51,830 autoreload 7212 11872 F:\zmkj-system\backend\apps\distribution\admin.py changed, reloading.
[INFO] 2025-07-27 15:52:52,290 autoreload 3052 13256 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:53:06,200 autoreload 3052 13256 F:\zmkj-system\backend\apps\distribution\admin.py changed, reloading.
[INFO] 2025-07-27 15:53:06,608 autoreload 5940 13852 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:53:17,423 autoreload 5940 13852 F:\zmkj-system\backend\apps\distribution\admin.py changed, reloading.
[INFO] 2025-07-27 15:53:17,837 autoreload 4576 16900 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:53:21,984 basehttp 4576 11504 "GET /admin/ HTTP/1.1" 200 28893
[INFO] 2025-07-27 15:53:30,904 autoreload 4576 16900 F:\zmkj-system\backend\apps\distribution\admin.py changed, reloading.
[INFO] 2025-07-27 15:53:31,370 autoreload 5240 10584 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:53:47,381 autoreload 5240 10584 F:\zmkj-system\backend\apps\distribution\admin.py changed, reloading.
[INFO] 2025-07-27 15:53:47,800 autoreload 5424 18620 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:54:10,035 autoreload 5424 18620 F:\zmkj-system\backend\apps\distribution\admin.py changed, reloading.
[INFO] 2025-07-27 15:54:10,445 autoreload 3344 20176 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:54:24,420 autoreload 3344 20176 F:\zmkj-system\backend\apps\distribution\admin.py changed, reloading.
[INFO] 2025-07-27 15:54:24,877 autoreload 2172 18260 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:55:21,918 autoreload 17664 7640 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:57:04,131 autoreload 17664 7640 F:\zmkj-system\backend\apps\distribution\admin.py changed, reloading.
[INFO] 2025-07-27 15:57:04,522 autoreload 16212 22924 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:57:12,299 basehttp 16212 16480 "GET /admin/ HTTP/1.1" 200 28893
[INFO] 2025-07-27 15:57:17,682 autoreload 16212 22924 F:\zmkj-system\backend\apps\distribution\admin.py changed, reloading.
[INFO] 2025-07-27 15:57:18,194 autoreload 18452 16968 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:57:51,269 autoreload 14436 17888 Watching for file changes with StatReloader
[INFO] 2025-07-27 15:58:00,818 basehttp 18452 4448 "GET /admin/ HTTP/1.1" 200 28893
[INFO] 2025-07-27 15:58:06,829 basehttp 18452 4448 "GET /admin/switch_theme/cool-black/ HTTP/1.1" 302 0
[INFO] 2025-07-27 15:58:07,047 basehttp 18452 4448 "GET /admin/ HTTP/1.1" 200 28896
[INFO] 2025-07-27 15:58:09,048 basehttp 18452 4448 "GET /admin/switch_theme/elegant-blue/ HTTP/1.1" 302 0
[INFO] 2025-07-27 15:58:09,266 basehttp 18452 4448 "GET /admin/ HTTP/1.1" 200 28898
[INFO] 2025-07-27 15:58:11,239 basehttp 18452 4448 "GET /admin/switch_theme/soft-green/ HTTP/1.1" 302 0
[INFO] 2025-07-27 15:58:11,437 basehttp 18452 4448 "GET /admin/ HTTP/1.1" 200 28896
[INFO] 2025-07-27 15:58:13,399 basehttp 18452 4448 "GET /admin/ HTTP/1.1" 200 28896
[INFO] 2025-07-27 15:58:17,389 basehttp 18452 4448 "GET /admin/switch_theme/default/ HTTP/1.1" 302 0
[INFO] 2025-07-27 15:58:17,682 basehttp 18452 4448 "GET /admin/ HTTP/1.1" 200 28893
[INFO] 2025-07-27 15:58:28,670 basehttp 18452 4448 "GET /admin/ HTTP/1.1" 200 28893
[INFO] 2025-07-27 15:59:46,946 autoreload 14124 22852 Watching for file changes with StatReloader
[INFO] 2025-07-27 16:00:03,551 autoreload 16740 19764 Watching for file changes with StatReloader
[INFO] 2025-07-27 16:00:08,995 basehttp 16740 7356 "GET /admin/ HTTP/1.1" 200 28894
[INFO] 2025-07-27 16:02:44,095 autoreload 6060 10264 Watching for file changes with StatReloader
[INFO] 2025-07-27 16:02:47,525 basehttp 6060 10288 "GET /admin/ HTTP/1.1" 200 28893
[INFO] 2025-07-27 16:03:14,703 basehttp 6060 10288 "GET /admin/ HTTP/1.1" 200 28893
[INFO] 2025-07-27 16:03:14,722 basehttp 6060 10632 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
[INFO] 2025-07-27 16:03:14,722 basehttp 6060 16660 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
[INFO] 2025-07-27 16:03:14,723 basehttp 6060 10288 "GET /static/admin/css/base.css HTTP/1.1" 200 22120
[INFO] 2025-07-27 16:03:14,723 basehttp 6060 888 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
[INFO] 2025-07-27 16:03:14,724 basehttp 6060 18336 "GET /static/admin/css/themes.css HTTP/1.1" 200 2524
[INFO] 2025-07-27 16:03:14,725 basehttp 6060 22636 "GET /static/admin/css/responsive.css HTTP/1.1" 200 659
[INFO] 2025-07-27 16:03:14,727 basehttp 6060 10632 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 200 12621
[INFO] 2025-07-27 16:03:14,727 basehttp 6060 16660 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
[INFO] 2025-07-27 16:03:14,727 basehttp 6060 10288 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 200 13404
[INFO] 2025-07-27 16:03:14,729 basehttp 6060 10288 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
[INFO] 2025-07-27 16:03:14,732 basehttp 6060 10288 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 200 43
[INFO] 2025-07-27 16:03:14,739 basehttp 6060 10288 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
[INFO] 2025-07-27 16:03:14,739 basehttp 6060 10632 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
[INFO] 2025-07-27 16:03:14,739 basehttp 6060 16660 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
[INFO] 2025-07-27 16:03:14,739 basehttp 6060 22636 "GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 200 392
[INFO] 2025-07-27 16:03:14,751 basehttp 6060 22636 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 200 4527
[WARNING] 2025-07-27 16:03:14,946 log 6060 22636 Not Found: /favicon.ico
[WARNING] 2025-07-27 16:03:14,946 basehttp 6060 22636 "GET /favicon.ico HTTP/1.1" 404 13493
[INFO] 2025-07-27 16:03:19,523 basehttp 6060 22636 "POST /admin/logout/ HTTP/1.1" 200 13236
[INFO] 2025-07-27 16:03:20,968 basehttp 6060 22636 "GET /admin/ HTTP/1.1" 302 0
[INFO] 2025-07-27 16:03:21,130 basehttp 6060 22636 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 13067
[INFO] 2025-07-27 16:03:21,142 basehttp 6060 22636 "GET /static/admin/css/login.css HTTP/1.1" 304 0
[INFO] 2025-07-27 16:03:26,923 basehttp 6060 22636 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
[INFO] 2025-07-27 16:03:27,122 basehttp 6060 22636 "GET /admin/ HTTP/1.1" 200 28885
[INFO] 2025-07-27 16:03:37,857 autoreload 6060 10264 F:\zmkj-system\backend\apps\distribution\admin.py changed, reloading.
[INFO] 2025-07-27 16:03:38,541 autoreload 17508 22792 Watching for file changes with StatReloader
[INFO] 2025-07-27 16:03:52,774 basehttp 17508 16740 "GET /admin/ HTTP/1.1" 200 34325
[INFO] 2025-07-27 16:03:56,547 basehttp 17508 16740 "GET /admin/distribution/promotioncode/ HTTP/1.1" 200 31849
[INFO] 2025-07-27 16:03:56,757 basehttp 17508 16740 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-27 16:03:59,085 basehttp 17508 16740 "GET /admin/distribution/promotioncode/add/ HTTP/1.1" 200 42601
[INFO] 2025-07-27 16:03:59,105 basehttp 17508 23452 "GET /static/admin/js/calendar.js HTTP/1.1" 304 0
[INFO] 2025-07-27 16:03:59,106 basehttp 17508 22216 "GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 304 0
[INFO] 2025-07-27 16:03:59,249 basehttp 17508 16740 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-27 16:03:59,311 basehttp 17508 16740 "GET /static/admin/img/icon-clock.svg HTTP/1.1" 304 0
[INFO] 2025-07-27 16:03:59,311 basehttp 17508 22216 "GET /static/admin/img/icon-calendar.svg HTTP/1.1" 304 0
[INFO] 2025-07-27 16:04:14,614 basehttp 17508 16740 "GET /admin/distribution/marketingactivity/ HTTP/1.1" 200 32852
[INFO] 2025-07-27 16:04:14,784 basehttp 17508 16740 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-27 16:04:17,455 basehttp 17508 16740 "GET /admin/distribution/marketingactivity/add/ HTTP/1.1" 200 43141
[INFO] 2025-07-27 16:04:17,634 basehttp 17508 16740 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-27 16:04:24,281 autoreload 15448 15820 Watching for file changes with StatReloader
[INFO] 2025-07-27 16:05:10,004 basehttp 17508 3596 "GET /admin/distribution/marketingactivity/add/ HTTP/1.1" 200 43141
[INFO] 2025-07-27 16:05:10,278 basehttp 17508 3596 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-27 16:05:12,776 basehttp 17508 3596 "GET /admin/ HTTP/1.1" 200 34325
[INFO] 2025-07-27 16:05:22,487 basehttp 17508 3596 "GET /admin/products/ HTTP/1.1" 200 19192
[INFO] 2025-07-27 16:05:24,662 basehttp 17508 3596 "GET /admin/ HTTP/1.1" 200 34325
[INFO] 2025-07-28 17:17:19,181 autoreload 24076 9548 Watching for file changes with StatReloader
[WARNING] 2025-07-28 17:17:26,561 log 24076 5508 Not Found: /
[WARNING] 2025-07-28 17:17:26,562 basehttp 24076 5508 "GET / HTTP/1.1" 404 13433
[INFO] 2025-07-28 17:17:26,603 basehttp 24076 24296 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 200 12621
[INFO] 2025-07-28 17:17:26,612 basehttp 24076 11376 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 200 13404
[INFO] 2025-07-28 17:17:26,671 basehttp 24076 11376 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 200 43
[INFO] 2025-07-28 17:17:26,724 basehttp 24076 11376 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 200 4527
[WARNING] 2025-07-28 17:17:26,790 log 24076 11376 Not Found: /favicon.ico
[WARNING] 2025-07-28 17:17:26,790 basehttp 24076 11376 "GET /favicon.ico HTTP/1.1" 404 13494
[INFO] 2025-07-28 17:17:34,604 basehttp 24076 18240 "GET /admin/ HTTP/1.1" 302 0
[INFO] 2025-07-28 17:17:34,864 basehttp 24076 18240 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 13067
[INFO] 2025-07-28 17:17:34,878 basehttp 24076 22088 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
[INFO] 2025-07-28 17:17:34,884 basehttp 24076 21664 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
[INFO] 2025-07-28 17:17:34,885 basehttp 24076 18240 "GET /static/admin/css/base.css HTTP/1.1" 200 22120
[INFO] 2025-07-28 17:17:34,941 basehttp 24076 18240 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
[INFO] 2025-07-28 17:17:34,942 basehttp 24076 21664 "GET /static/admin/css/login.css HTTP/1.1" 200 951
[INFO] 2025-07-28 17:17:34,942 basehttp 24076 22088 "GET /static/admin/css/responsive.css HTTP/1.1" 200 659
[INFO] 2025-07-28 17:17:34,949 basehttp 24076 22088 "GET /static/admin/css/themes.css HTTP/1.1" 200 2524
[INFO] 2025-07-28 17:17:34,962 basehttp 24076 21664 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
[INFO] 2025-07-28 17:17:44,736 basehttp 24076 21664 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 13220
[INFO] 2025-07-28 17:17:50,400 basehttp 24076 21664 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
[INFO] 2025-07-28 17:17:50,733 basehttp 24076 21664 "GET /admin/ HTTP/1.1" 200 34325
[INFO] 2025-07-28 17:17:50,747 basehttp 24076 21664 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
[INFO] 2025-07-28 17:17:50,778 basehttp 24076 21664 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
[INFO] 2025-07-28 17:17:50,778 basehttp 24076 18240 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
[INFO] 2025-07-28 17:17:50,778 basehttp 24076 22088 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
[INFO] 2025-07-28 17:17:50,778 basehttp 24076 21488 "GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 200 392
[INFO] 2025-07-28 17:17:59,612 basehttp 24076 21488 "GET /admin/distribution/distributionlevel/ HTTP/1.1" 200 36458
[INFO] 2025-07-28 17:17:59,644 basehttp 24076 18240 "GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
[INFO] 2025-07-28 17:17:59,649 basehttp 24076 21488 "GET /static/admin/css/changelists.css HTTP/1.1" 200 6878
[INFO] 2025-07-28 17:17:59,668 basehttp 24076 18240 "GET /static/admin/js/actions.js HTTP/1.1" 200 8076
[INFO] 2025-07-28 17:17:59,668 basehttp 24076 22880 "GET /static/admin/js/core.js HTTP/1.1" 200 6208
[INFO] 2025-07-28 17:17:59,673 basehttp 24076 21932 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 9777
[INFO] 2025-07-28 17:17:59,675 basehttp 24076 21488 "GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
[INFO] 2025-07-28 17:17:59,688 basehttp 24076 22880 "GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
[INFO] 2025-07-28 17:17:59,743 basehttp 24076 21664 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 285314
[INFO] 2025-07-28 17:17:59,747 basehttp 24076 22880 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
[INFO] 2025-07-28 17:17:59,767 basehttp 24076 18240 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 325171
[INFO] 2025-07-28 17:17:59,776 basehttp 24076 21664 "GET /static/admin/img/search.svg HTTP/1.1" 200 458
[INFO] 2025-07-28 17:17:59,843 basehttp 24076 21664 "GET /static/admin/js/filters.js HTTP/1.1" 200 978
[INFO] 2025-07-28 17:17:59,853 basehttp 24076 22088 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-28 17:17:59,879 basehttp 24076 22088 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
[INFO] 2025-07-28 17:17:59,901 basehttp 24076 21664 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
[INFO] 2025-07-28 17:18:03,357 basehttp 24076 21664 "GET /admin/ HTTP/1.1" 200 34325
[ERROR] 2025-07-28 17:19:07,779 log 24076 21664 Internal Server Error: /admin/distribution/distributionrelation/
Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\db\models\options.py", line 683, in get_field
    return self.fields_map[field_name]
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'team_size'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\contrib\admin\utils.py", line 290, in lookup_field
    f = _get_non_gfk_field(opts, name)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\contrib\admin\utils.py", line 330, in _get_non_gfk_field
    field = opts.get_field(name)
            ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\db\models\options.py", line 685, in get_field
    raise FieldDoesNotExist(
django.core.exceptions.FieldDoesNotExist: DistributionRelation has no field named 'team_size'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 1016, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\contrib\admin\templatetags\base.py", line 45, in render
    return super().render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\template\library.py", line 359, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 354, in result_list
    "results": list(results(cl)),
               ^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 330, in results
    yield ResultList(None, items_for_result(cl, res, None))
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 321, in __init__
    super().__init__(*items)
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 219, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\zmkj-system\backend\venv\Lib\site-packages\django\contrib\admin\utils.py", line 299, in lookup_field
    value = attr(obj)
            ^^^^^^^^^
  File "F:\zmkj-system\backend\apps\distribution\admin.py", line 136, in team_size
    stats['direct_children'],
    ~~~~~^^^^^^^^^^^^^^^^^^^
KeyError: 'direct_children'
[ERROR] 2025-07-28 17:19:07,906 basehttp 24076 21664 "GET /admin/distribution/distributionrelation/ HTTP/1.1" 500 469786
[INFO] 2025-07-28 17:19:13,977 basehttp 24076 21664 "GET /admin/ HTTP/1.1" 200 34325
[WARNING] 2025-07-28 17:22:35,122 log 10464 22520 Bad Request: /api/v1/wechat/miniprogram/auth/
[WARNING] 2025-07-28 17:22:35,883 log 10464 22520 Bad Request: /api/v1/wechat/api/payment/create_order/
[INFO] 2025-07-29 16:46:54,538 autoreload 10784 18372 Watching for file changes with StatReloader
[WARNING] 2025-07-29 17:40:24,969 log 10784 21416 Not Found: /banners/banner1.jpg
[WARNING] 2025-07-29 17:40:24,969 log 10784 23596 Not Found: /banners/banner2.jpg
[WARNING] 2025-07-29 17:40:24,969 log 10784 12980 Not Found: /banners/banner3.jpg
[WARNING] 2025-07-29 17:40:24,970 basehttp 10784 21416 "GET /banners/banner1.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:40:24,971 basehttp 10784 23596 "GET /banners/banner2.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:40:24,971 basehttp 10784 12980 "GET /banners/banner3.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:40:51,084 log 10784 19736 Not Found: /banners/banner2.jpg
[WARNING] 2025-07-29 17:40:51,089 log 10784 22944 Not Found: /banners/banner3.jpg
[WARNING] 2025-07-29 17:40:51,089 basehttp 10784 19736 "GET /banners/banner2.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:40:51,093 log 10784 7956 Not Found: /banners/banner1.jpg
[WARNING] 2025-07-29 17:40:51,093 basehttp 10784 22944 "GET /banners/banner3.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:40:51,095 basehttp 10784 7956 "GET /banners/banner1.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:40:51,100 log 10784 17832 Not Found: /categories/clothing.jpg
[WARNING] 2025-07-29 17:40:51,103 log 10784 14848 Not Found: /categories/home.jpg
[WARNING] 2025-07-29 17:40:51,106 log 10784 18992 Not Found: /categories/digital.jpg
[WARNING] 2025-07-29 17:40:51,108 basehttp 10784 17832 "GET /categories/clothing.jpg HTTP/1.1" 404 13544
[WARNING] 2025-07-29 17:40:51,108 basehttp 10784 14848 "GET /categories/home.jpg HTTP/1.1" 404 13527
[WARNING] 2025-07-29 17:40:51,109 basehttp 10784 18992 "GET /categories/digital.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 17:40:51,403 log 10784 22208 Not Found: /categories/beauty.jpg
[WARNING] 2025-07-29 17:40:51,406 log 10784 6904 Not Found: /categories/sports.jpg
[WARNING] 2025-07-29 17:40:51,409 log 10784 22944 Not Found: /categories/food.jpg
[WARNING] 2025-07-29 17:40:51,410 basehttp 10784 22208 "GET /categories/beauty.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 17:40:51,410 basehttp 10784 6904 "GET /categories/sports.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 17:40:51,411 basehttp 10784 22944 "GET /categories/food.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:46:35,974 log 10784 21828 Not Found: /banners/banner1.jpg
[WARNING] 2025-07-29 17:46:35,978 log 10784 25000 Not Found: /banners/banner2.jpg
[WARNING] 2025-07-29 17:46:35,980 basehttp 10784 21828 "GET /banners/banner1.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:46:35,985 log 10784 25084 Not Found: /categories/clothing.jpg
[WARNING] 2025-07-29 17:46:35,986 basehttp 10784 25000 "GET /banners/banner2.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:46:35,989 log 10784 24876 Not Found: /categories/home.jpg
[WARNING] 2025-07-29 17:46:35,992 log 10784 8852 Not Found: /banners/banner3.jpg
[WARNING] 2025-07-29 17:46:35,996 log 10784 24380 Not Found: /categories/digital.jpg
[WARNING] 2025-07-29 17:46:35,996 basehttp 10784 25084 "GET /categories/clothing.jpg HTTP/1.1" 404 13543
[WARNING] 2025-07-29 17:46:35,997 basehttp 10784 24876 "GET /categories/home.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:46:35,999 basehttp 10784 8852 "GET /banners/banner3.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:46:36,000 basehttp 10784 24380 "GET /categories/digital.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 17:46:36,216 log 10784 25516 Not Found: /categories/beauty.jpg
[WARNING] 2025-07-29 17:46:36,220 log 10784 25408 Not Found: /categories/sports.jpg
[WARNING] 2025-07-29 17:46:36,220 basehttp 10784 25516 "GET /categories/beauty.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 17:46:36,225 log 10784 964 Not Found: /categories/food.jpg
[WARNING] 2025-07-29 17:46:36,226 basehttp 10784 25408 "GET /categories/sports.jpg HTTP/1.1" 404 13535
[WARNING] 2025-07-29 17:46:36,226 basehttp 10784 964 "GET /categories/food.jpg HTTP/1.1" 404 13527
[WARNING] 2025-07-29 17:46:48,528 log 10784 23904 Not Found: /banners/banner1.jpg
[WARNING] 2025-07-29 17:46:48,533 log 10784 3472 Not Found: /banners/banner2.jpg
[WARNING] 2025-07-29 17:46:48,536 basehttp 10784 23904 "GET /banners/banner1.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:46:48,537 basehttp 10784 3472 "GET /banners/banner2.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:46:48,541 log 10784 4064 Not Found: /categories/clothing.jpg
[WARNING] 2025-07-29 17:46:48,545 log 10784 21668 Not Found: /categories/digital.jpg
[WARNING] 2025-07-29 17:46:48,546 basehttp 10784 4064 "GET /categories/clothing.jpg HTTP/1.1" 404 13544
[WARNING] 2025-07-29 17:46:48,552 log 10784 16724 Not Found: /categories/home.jpg
[WARNING] 2025-07-29 17:46:48,556 log 10784 25132 Not Found: /banners/banner3.jpg
[WARNING] 2025-07-29 17:46:48,556 basehttp 10784 21668 "GET /categories/digital.jpg HTTP/1.1" 404 13539
[WARNING] 2025-07-29 17:46:48,557 basehttp 10784 16724 "GET /categories/home.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:46:48,558 basehttp 10784 25132 "GET /banners/banner3.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:46:49,910 log 10784 25488 Not Found: /categories/beauty.jpg
[WARNING] 2025-07-29 17:46:49,913 log 10784 25540 Not Found: /categories/sports.jpg
[WARNING] 2025-07-29 17:46:49,917 log 10784 23812 Not Found: /categories/food.jpg
[WARNING] 2025-07-29 17:46:49,918 basehttp 10784 25488 "GET /categories/beauty.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 17:46:49,918 basehttp 10784 25540 "GET /categories/sports.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 17:46:49,919 basehttp 10784 23812 "GET /categories/food.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:47:12,949 log 10784 21900 Not Found: /banners/banner2.jpg
[WARNING] 2025-07-29 17:47:12,952 log 10784 24916 Not Found: /banners/banner3.jpg
[WARNING] 2025-07-29 17:47:12,955 log 10784 21560 Not Found: /banners/banner1.jpg
[WARNING] 2025-07-29 17:47:12,955 basehttp 10784 21900 "GET /banners/banner2.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:47:12,955 basehttp 10784 24916 "GET /banners/banner3.jpg HTTP/1.1" 404 13527
[WARNING] 2025-07-29 17:47:12,956 basehttp 10784 21560 "GET /banners/banner1.jpg HTTP/1.1" 404 13527
[WARNING] 2025-07-29 17:47:13,494 log 10784 11232 Not Found: /categories/home.jpg
[WARNING] 2025-07-29 17:47:13,498 log 10784 23004 Not Found: /categories/clothing.jpg
[WARNING] 2025-07-29 17:47:13,503 log 10784 25088 Not Found: /categories/digital.jpg
[WARNING] 2025-07-29 17:47:13,506 log 10784 18816 Not Found: /categories/beauty.jpg
[WARNING] 2025-07-29 17:47:13,507 basehttp 10784 23004 "GET /categories/clothing.jpg HTTP/1.1" 404 13544
[WARNING] 2025-07-29 17:47:13,507 basehttp 10784 11232 "GET /categories/home.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:47:13,509 log 10784 7496 Not Found: /categories/food.jpg
[WARNING] 2025-07-29 17:47:13,513 log 10784 23296 Not Found: /categories/sports.jpg
[WARNING] 2025-07-29 17:47:13,513 basehttp 10784 25088 "GET /categories/digital.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 17:47:13,514 basehttp 10784 18816 "GET /categories/beauty.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 17:47:13,516 basehttp 10784 7496 "GET /categories/food.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:47:13,516 basehttp 10784 23296 "GET /categories/sports.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 17:47:36,907 log 10784 19680 Not Found: /banners/banner2.jpg
[WARNING] 2025-07-29 17:47:36,910 log 10784 12388 Not Found: /banners/banner3.jpg
[WARNING] 2025-07-29 17:47:36,914 log 10784 10848 Not Found: /categories/clothing.jpg
[WARNING] 2025-07-29 17:47:36,918 log 10784 10780 Not Found: /banners/banner1.jpg
[WARNING] 2025-07-29 17:47:36,918 basehttp 10784 19680 "GET /banners/banner2.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:47:36,921 log 10784 6724 Not Found: /categories/digital.jpg
[WARNING] 2025-07-29 17:47:36,927 log 10784 22916 Not Found: /categories/home.jpg
[WARNING] 2025-07-29 17:47:36,927 basehttp 10784 12388 "GET /banners/banner3.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:47:36,927 basehttp 10784 10848 "GET /categories/clothing.jpg HTTP/1.1" 404 13544
[WARNING] 2025-07-29 17:47:36,928 basehttp 10784 10780 "GET /banners/banner1.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:47:36,929 basehttp 10784 6724 "GET /categories/digital.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 17:47:36,929 basehttp 10784 22916 "GET /categories/home.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:47:37,384 log 10784 23516 Not Found: /categories/beauty.jpg
[WARNING] 2025-07-29 17:47:37,387 log 10784 23840 Not Found: /categories/food.jpg
[WARNING] 2025-07-29 17:47:37,391 log 10784 23900 Not Found: /categories/sports.jpg
[WARNING] 2025-07-29 17:47:37,392 basehttp 10784 23516 "GET /categories/beauty.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 17:47:37,393 basehttp 10784 23840 "GET /categories/food.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:47:37,393 basehttp 10784 23900 "GET /categories/sports.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 17:47:37,398 log 10784 20296 Not Found: /products/earphone1.jpg
[WARNING] 2025-07-29 17:47:37,401 log 10784 24120 Not Found: /products/tshirt1.jpg
[WARNING] 2025-07-29 17:47:37,403 basehttp 10784 20296 "GET /products/earphone1.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 17:47:37,403 basehttp 10784 24120 "GET /products/tshirt1.jpg HTTP/1.1" 404 13532
[WARNING] 2025-07-29 17:47:37,459 log 10784 12792 Not Found: /products/decoration1.jpg
[WARNING] 2025-07-29 17:47:37,460 basehttp 10784 12792 "GET /products/decoration1.jpg HTTP/1.1" 404 13547
[WARNING] 2025-07-29 17:47:37,565 log 10784 21768 Not Found: /products/skincare1.jpg
[WARNING] 2025-07-29 17:47:37,566 basehttp 10784 21768 "GET /products/skincare1.jpg HTTP/1.1" 404 13539
[WARNING] 2025-07-29 17:47:50,537 log 10784 25316 Not Found: /banners/banner1.jpg
[WARNING] 2025-07-29 17:47:50,541 log 10784 11052 Not Found: /banners/banner3.jpg
[WARNING] 2025-07-29 17:47:50,545 log 10784 11064 Not Found: /banners/banner2.jpg
[WARNING] 2025-07-29 17:47:50,545 basehttp 10784 25316 "GET /banners/banner1.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:47:50,546 basehttp 10784 11052 "GET /banners/banner3.jpg HTTP/1.1" 404 13527
[WARNING] 2025-07-29 17:47:50,546 basehttp 10784 11064 "GET /banners/banner2.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:47:50,895 log 10784 24824 Not Found: /categories/clothing.jpg
[WARNING] 2025-07-29 17:47:50,896 basehttp 10784 24824 "GET /categories/clothing.jpg HTTP/1.1" 404 13544
[WARNING] 2025-07-29 17:47:50,902 log 10784 25176 Not Found: /categories/home.jpg
[WARNING] 2025-07-29 17:47:50,904 log 10784 25460 Not Found: /categories/digital.jpg
[WARNING] 2025-07-29 17:47:50,909 log 10784 24604 Not Found: /categories/sports.jpg
[WARNING] 2025-07-29 17:47:50,910 basehttp 10784 25176 "GET /categories/home.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:47:50,914 log 10784 24508 Not Found: /categories/beauty.jpg
[WARNING] 2025-07-29 17:47:50,914 basehttp 10784 25460 "GET /categories/digital.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 17:47:50,918 log 10784 25252 Not Found: /categories/food.jpg
[WARNING] 2025-07-29 17:47:50,919 basehttp 10784 24604 "GET /categories/sports.jpg HTTP/1.1" 404 13535
[WARNING] 2025-07-29 17:47:50,920 basehttp 10784 24508 "GET /categories/beauty.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 17:47:50,921 basehttp 10784 25252 "GET /categories/food.jpg HTTP/1.1" 404 13527
[WARNING] 2025-07-29 17:47:51,327 log 10784 2036 Not Found: /products/tshirt1.jpg
[WARNING] 2025-07-29 17:47:51,332 log 10784 15128 Not Found: /products/decoration1.jpg
[WARNING] 2025-07-29 17:47:51,335 log 10784 11872 Not Found: /products/skincare1.jpg
[WARNING] 2025-07-29 17:47:51,337 log 10784 22540 Not Found: /products/earphone1.jpg
[WARNING] 2025-07-29 17:47:51,337 basehttp 10784 2036 "GET /products/tshirt1.jpg HTTP/1.1" 404 13532
[WARNING] 2025-07-29 17:47:51,338 basehttp 10784 15128 "GET /products/decoration1.jpg HTTP/1.1" 404 13547
[WARNING] 2025-07-29 17:47:51,338 basehttp 10784 11872 "GET /products/skincare1.jpg HTTP/1.1" 404 13539
[WARNING] 2025-07-29 17:47:51,340 basehttp 10784 22540 "GET /products/earphone1.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 17:53:01,936 log 10784 22748 Not Found: /
[WARNING] 2025-07-29 17:53:01,937 basehttp 10784 22748 "GET / HTTP/1.1" 404 13432
[INFO] 2025-07-29 17:53:01,958 basehttp 10784 404 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:01,958 basehttp 10784 22748 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:02,025 basehttp 10784 22748 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:02,049 basehttp 10784 22748 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
[WARNING] 2025-07-29 17:53:02,128 log 10784 22748 Not Found: /favicon.ico
[WARNING] 2025-07-29 17:53:02,128 basehttp 10784 22748 "GET /favicon.ico HTTP/1.1" 404 13494
[INFO] 2025-07-29 17:53:05,005 basehttp 10784 25508 "GET /admin/ HTTP/1.1" 302 0
[INFO] 2025-07-29 17:53:05,360 basehttp 10784 25508 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 13067
[INFO] 2025-07-29 17:53:05,373 basehttp 10784 25508 "GET /static/admin/css/base.css HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:05,373 basehttp 10784 23824 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:05,375 basehttp 10784 25276 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:05,439 basehttp 10784 25508 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:05,440 basehttp 10784 23824 "GET /static/admin/css/login.css HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:05,447 basehttp 10784 23824 "GET /static/admin/css/themes.css HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:05,516 basehttp 10784 25276 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:05,517 basehttp 10784 23824 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:12,518 basehttp 10784 23824 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
[INFO] 2025-07-29 17:53:12,973 basehttp 10784 23824 "GET /admin/ HTTP/1.1" 200 34325
[INFO] 2025-07-29 17:53:12,998 basehttp 10784 23824 "GET /static/admin/css/dashboard.css HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:13,025 basehttp 10784 23824 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:13,025 basehttp 10784 25508 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:13,025 basehttp 10784 25276 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:13,026 basehttp 10784 8536 "GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:41,196 basehttp 10784 8536 "GET /admin/products/productcategory/ HTTP/1.1" 200 31644
[INFO] 2025-07-29 17:53:41,218 basehttp 10784 8536 "GET /static/admin/css/changelists.css HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:41,218 basehttp 10784 23824 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:41,221 basehttp 10784 8536 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:41,222 basehttp 10784 23824 "GET /static/admin/js/actions.js HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:41,225 basehttp 10784 8536 "GET /static/admin/js/urlify.js HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:41,227 basehttp 10784 23824 "GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:41,228 basehttp 10784 8536 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:41,246 basehttp 10784 25276 "GET /static/admin/css/forms.css HTTP/1.1" 200 8525
[INFO] 2025-07-29 17:53:41,255 basehttp 10784 25268 "GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:41,255 basehttp 10784 25476 "GET /static/admin/js/core.js HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:41,263 basehttp 10784 25476 "GET /static/admin/img/search.svg HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:41,331 basehttp 10784 25276 "GET /static/admin/css/widgets.css HTTP/1.1" 200 11991
[INFO] 2025-07-29 17:53:41,335 basehttp 10784 25276 "GET /static/admin/js/filters.js HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:41,424 basehttp 10784 25508 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:53:41,441 basehttp 10784 25508 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 304 0
[INFO] 2025-07-29 17:53:43,924 basehttp 10784 25508 "GET /admin/products/productcategory/add/ HTTP/1.1" 200 35981
[INFO] 2025-07-29 17:53:43,943 basehttp 10784 25476 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
[INFO] 2025-07-29 17:53:43,956 basehttp 10784 25276 "GET /static/admin/js/change_form.js HTTP/1.1" 200 606
[INFO] 2025-07-29 17:53:44,231 basehttp 10784 25508 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:54:18,281 basehttp 10784 25508 "POST /admin/products/productcategory/add/ HTTP/1.1" 302 0
[INFO] 2025-07-29 17:54:18,498 basehttp 10784 25508 "GET /admin/products/productcategory/add/ HTTP/1.1" 200 36272
[INFO] 2025-07-29 17:54:18,642 basehttp 10784 25508 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:54:18,657 basehttp 10784 25508 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 304 0
[INFO] 2025-07-29 17:54:35,056 basehttp 10784 25508 "POST /admin/products/productcategory/add/ HTTP/1.1" 302 0
[INFO] 2025-07-29 17:54:35,413 basehttp 10784 25508 "GET /admin/products/productcategory/add/ HTTP/1.1" 200 36309
[INFO] 2025-07-29 17:54:35,643 basehttp 10784 25508 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:54:46,206 basehttp 10784 25508 "POST /admin/products/productcategory/add/ HTTP/1.1" 302 0
[INFO] 2025-07-29 17:54:46,432 basehttp 10784 25508 "GET /admin/products/productcategory/add/ HTTP/1.1" 200 36346
[INFO] 2025-07-29 17:54:46,598 basehttp 10784 25508 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:54:56,950 basehttp 10784 25508 "POST /admin/products/productcategory/add/ HTTP/1.1" 302 0
[INFO] 2025-07-29 17:54:57,281 basehttp 10784 25508 "GET /admin/products/productcategory/ HTTP/1.1" 200 38374
[INFO] 2025-07-29 17:54:57,423 basehttp 10784 25508 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:54:57,439 basehttp 10784 25508 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 304 0
[INFO] 2025-07-29 17:55:04,215 basehttp 10784 25508 "GET /admin/products/product/ HTTP/1.1" 200 32492
[INFO] 2025-07-29 17:55:04,521 basehttp 10784 25508 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:55:05,890 basehttp 10784 25508 "GET /admin/products/productattribute/ HTTP/1.1" 200 31346
[INFO] 2025-07-29 17:55:06,068 basehttp 10784 25508 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:55:08,004 basehttp 10784 25508 "GET /admin/products/productattribute/add/ HTTP/1.1" 200 36469
[INFO] 2025-07-29 17:55:08,037 basehttp 10784 25508 "GET /static/admin/js/calendar.js HTTP/1.1" 200 9141
[INFO] 2025-07-29 17:55:08,037 basehttp 10784 25476 "GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
[INFO] 2025-07-29 17:55:08,158 basehttp 10784 25276 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:55:08,221 basehttp 10784 25476 "GET /static/admin/img/icon-clock.svg HTTP/1.1" 200 677
[INFO] 2025-07-29 17:55:08,228 basehttp 10784 25276 "GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
[INFO] 2025-07-29 17:56:07,216 basehttp 10784 25276 "POST /admin/products/productattribute/add/ HTTP/1.1" 302 0
[INFO] 2025-07-29 17:56:07,633 basehttp 10784 25276 "GET /admin/products/productattribute/add/ HTTP/1.1" 200 36721
[INFO] 2025-07-29 17:56:07,782 basehttp 10784 25276 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:56:10,507 basehttp 10784 25276 "GET /admin/products/productattributevalue/ HTTP/1.1" 200 30938
[INFO] 2025-07-29 17:56:10,657 basehttp 10784 25276 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:56:11,867 basehttp 10784 25276 "GET /admin/products/productattributevalue/add/ HTTP/1.1" 200 35604
[INFO] 2025-07-29 17:56:12,014 basehttp 10784 25276 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:56:19,744 basehttp 10784 25276 "GET /admin/products/productreview/ HTTP/1.1" 200 32452
[INFO] 2025-07-29 17:56:19,912 basehttp 10784 25276 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:56:20,429 basehttp 10784 25276 "GET /admin/products/productimage/ HTTP/1.1" 200 31636
[INFO] 2025-07-29 17:56:20,572 basehttp 10784 25276 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:56:21,845 basehttp 10784 25276 "GET /admin/products/product/ HTTP/1.1" 200 32493
[INFO] 2025-07-29 17:56:21,996 basehttp 10784 25276 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:56:22,814 basehttp 10784 25276 "GET /admin/products/product/add/ HTTP/1.1" 200 73448
[INFO] 2025-07-29 17:56:22,850 basehttp 10784 25276 "GET /static/admin/js/inlines.js HTTP/1.1" 200 15628
[INFO] 2025-07-29 17:56:22,856 basehttp 10784 25276 "GET /static/admin/img/icon-unknown.svg HTTP/1.1" 200 655
[INFO] 2025-07-29 17:56:22,969 basehttp 10784 25476 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:56:23,041 basehttp 10784 25476 "GET /static/admin/img/inline-delete.svg HTTP/1.1" 200 537
[INFO] 2025-07-29 17:58:11,396 basehttp 10784 25476 "POST /admin/products/product/add/ HTTP/1.1" 200 73771
[INFO] 2025-07-29 17:58:11,633 basehttp 10784 25476 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 17:58:22,151 basehttp 10784 25476 "POST /admin/products/product/add/ HTTP/1.1" 302 0
[INFO] 2025-07-29 17:58:22,388 basehttp 10784 25476 "GET /admin/products/product/ HTTP/1.1" 200 36800
[INFO] 2025-07-29 17:58:22,537 basehttp 10784 25476 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[WARNING] 2025-07-29 17:58:40,506 log 10784 13028 Not Found: /categories/clothing.jpg
[WARNING] 2025-07-29 17:58:40,507 basehttp 10784 13028 "GET /categories/clothing.jpg HTTP/1.1" 404 13544
[WARNING] 2025-07-29 17:58:40,510 log 10784 12644 Not Found: /banners/banner3.jpg
[WARNING] 2025-07-29 17:58:40,513 log 10784 25516 Not Found: /banners/banner2.jpg
[WARNING] 2025-07-29 17:58:40,516 log 10784 14664 Not Found: /categories/digital.jpg
[WARNING] 2025-07-29 17:58:40,520 log 10784 24596 Not Found: /categories/home.jpg
[WARNING] 2025-07-29 17:58:40,522 log 10784 6148 Not Found: /banners/banner1.jpg
[WARNING] 2025-07-29 17:58:40,523 basehttp 10784 12644 "GET /banners/banner3.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:58:40,523 basehttp 10784 25516 "GET /banners/banner2.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:58:40,523 basehttp 10784 14664 "GET /categories/digital.jpg HTTP/1.1" 404 13539
[WARNING] 2025-07-29 17:58:40,524 basehttp 10784 24596 "GET /categories/home.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:58:40,525 basehttp 10784 6148 "GET /banners/banner1.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:58:40,829 log 10784 11232 Not Found: /categories/beauty.jpg
[WARNING] 2025-07-29 17:58:40,831 basehttp 10784 11232 "GET /categories/beauty.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 17:58:40,835 log 10784 3568 Not Found: /categories/food.jpg
[WARNING] 2025-07-29 17:58:40,839 log 10784 24344 Not Found: /categories/sports.jpg
[WARNING] 2025-07-29 17:58:40,840 basehttp 10784 3568 "GET /categories/food.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 17:58:40,843 log 10784 24784 Not Found: /products/tshirt1.jpg
[WARNING] 2025-07-29 17:58:40,844 basehttp 10784 24344 "GET /categories/sports.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 17:58:40,847 log 10784 10800 Not Found: /products/earphone1.jpg
[WARNING] 2025-07-29 17:58:40,848 basehttp 10784 24784 "GET /products/tshirt1.jpg HTTP/1.1" 404 13531
[WARNING] 2025-07-29 17:58:40,849 basehttp 10784 10800 "GET /products/earphone1.jpg HTTP/1.1" 404 13539
[WARNING] 2025-07-29 17:58:41,037 log 10784 23460 Not Found: /products/decoration1.jpg
[WARNING] 2025-07-29 17:58:41,038 basehttp 10784 23460 "GET /products/decoration1.jpg HTTP/1.1" 404 13547
[WARNING] 2025-07-29 17:58:41,042 log 10784 23912 Not Found: /products/skincare1.jpg
[WARNING] 2025-07-29 17:58:41,043 basehttp 10784 23912 "GET /products/skincare1.jpg HTTP/1.1" 404 13539
[INFO] 2025-07-29 18:01:03,448 autoreload 10784 18372 F:\zmkj-system\backend\apps\core\models.py changed, reloading.
[INFO] 2025-07-29 18:01:04,694 autoreload 26556 8676 Watching for file changes with StatReloader
[INFO] 2025-07-29 18:01:58,036 autoreload 26556 8676 F:\zmkj-system\backend\apps\core\admin.py changed, reloading.
[INFO] 2025-07-29 18:01:58,471 autoreload 17528 13028 Watching for file changes with StatReloader
[INFO] 2025-07-29 18:02:24,860 autoreload 17528 13028 F:\zmkj-system\backend\apps\core\admin.py changed, reloading.
[INFO] 2025-07-29 18:02:25,282 autoreload 26076 18476 Watching for file changes with StatReloader
[INFO] 2025-07-29 18:02:38,245 autoreload 26076 18476 F:\zmkj-system\backend\apps\core\admin.py changed, reloading.
[INFO] 2025-07-29 18:02:38,705 autoreload 6280 11028 Watching for file changes with StatReloader
[INFO] 2025-07-29 18:03:27,683 autoreload 24512 22412 Watching for file changes with StatReloader
[INFO] 2025-07-29 18:03:29,817 basehttp 24512 24600 "GET /admin/products/product/ HTTP/1.1" 200 37496
[INFO] 2025-07-29 18:03:29,999 basehttp 24512 24600 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 18:04:42,129 autoreload 24512 22412 F:\zmkj-system\backend\apps\core\views.py changed, reloading.
[INFO] 2025-07-29 18:04:42,662 autoreload 5396 25044 Watching for file changes with StatReloader
[INFO] 2025-07-29 18:05:00,736 autoreload 5396 25044 F:\zmkj-system\backend\apps\core\views.py changed, reloading.
[INFO] 2025-07-29 18:05:01,160 autoreload 26348 25888 Watching for file changes with StatReloader
[INFO] 2025-07-29 18:05:16,122 autoreload 26348 25888 F:\zmkj-system\backend\apps\core\views.py changed, reloading.
[INFO] 2025-07-29 18:05:16,556 autoreload 24072 24404 Watching for file changes with StatReloader
[INFO] 2025-07-29 18:05:28,443 autoreload 24072 24404 F:\zmkj-system\backend\apps\core\urls.py changed, reloading.
[INFO] 2025-07-29 18:05:28,854 autoreload 23612 24376 Watching for file changes with StatReloader
[INFO] 2025-07-29 18:05:43,837 autoreload 23612 24376 F:\zmkj-system\backend\apps\core\urls.py changed, reloading.
[INFO] 2025-07-29 18:05:44,256 autoreload 25716 24452 Watching for file changes with StatReloader
[WARNING] 2025-07-29 18:06:32,660 log 25716 24748 Not Found: /categories/clothing.jpg
[WARNING] 2025-07-29 18:06:32,660 log 25716 19844 Not Found: /categories/beauty.jpg
[WARNING] 2025-07-29 18:06:32,660 log 25716 25880 Not Found: /categories/home.jpg
[WARNING] 2025-07-29 18:06:32,660 log 25716 26488 Not Found: /categories/food.jpg
[WARNING] 2025-07-29 18:06:32,660 log 25716 21616 Not Found: /categories/sports.jpg
[WARNING] 2025-07-29 18:06:32,660 log 25716 24668 Not Found: /categories/digital.jpg
[WARNING] 2025-07-29 18:06:32,661 basehttp 25716 24748 "GET /categories/clothing.jpg HTTP/1.1" 404 13544
[WARNING] 2025-07-29 18:06:32,662 basehttp 25716 25880 "GET /categories/home.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 18:06:32,663 basehttp 25716 19844 "GET /categories/beauty.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 18:06:32,664 basehttp 25716 26488 "GET /categories/food.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 18:06:32,665 basehttp 25716 21616 "GET /categories/sports.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 18:06:32,665 basehttp 25716 24668 "GET /categories/digital.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 18:06:32,953 log 25716 12216 Not Found: /products/tshirt1.jpg
[WARNING] 2025-07-29 18:06:32,957 log 25716 22440 Not Found: /products/decoration1.jpg
[WARNING] 2025-07-29 18:06:32,960 log 25716 24868 Not Found: /products/earphone1.jpg
[WARNING] 2025-07-29 18:06:32,960 basehttp 25716 12216 "GET /products/tshirt1.jpg HTTP/1.1" 404 13532
[WARNING] 2025-07-29 18:06:32,961 basehttp 25716 22440 "GET /products/decoration1.jpg HTTP/1.1" 404 13547
[WARNING] 2025-07-29 18:06:32,961 basehttp 25716 24868 "GET /products/earphone1.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 18:06:33,125 log 25716 26244 Not Found: /products/skincare1.jpg
[WARNING] 2025-07-29 18:06:33,125 basehttp 25716 26244 "GET /products/skincare1.jpg HTTP/1.1" 404 13539
[WARNING] 2025-07-29 18:06:44,905 log 25716 26372 Not Found: /categories/clothing.jpg
[WARNING] 2025-07-29 18:06:44,907 basehttp 25716 26372 "GET /categories/clothing.jpg HTTP/1.1" 404 13544
[WARNING] 2025-07-29 18:06:44,978 log 25716 8280 Not Found: /categories/home.jpg
[WARNING] 2025-07-29 18:06:44,980 basehttp 25716 8280 "GET /categories/home.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 18:06:44,982 log 25716 24848 Not Found: /categories/digital.jpg
[WARNING] 2025-07-29 18:06:44,986 log 25716 23052 Not Found: /categories/food.jpg
[WARNING] 2025-07-29 18:06:44,990 log 25716 23508 Not Found: /categories/beauty.jpg
[WARNING] 2025-07-29 18:06:44,993 log 25716 948 Not Found: /categories/sports.jpg
[WARNING] 2025-07-29 18:06:44,994 basehttp 25716 24848 "GET /categories/digital.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 18:06:44,994 basehttp 25716 23052 "GET /categories/food.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 18:06:44,994 basehttp 25716 23508 "GET /categories/beauty.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 18:06:44,996 basehttp 25716 948 "GET /categories/sports.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 18:06:45,080 log 25716 22220 Not Found: /products/tshirt1.jpg
[WARNING] 2025-07-29 18:06:45,081 basehttp 25716 22220 "GET /products/tshirt1.jpg HTTP/1.1" 404 13531
[WARNING] 2025-07-29 18:06:45,210 log 25716 8280 Not Found: /products/earphone1.jpg
[WARNING] 2025-07-29 18:06:45,214 log 25716 11372 Not Found: /products/skincare1.jpg
[WARNING] 2025-07-29 18:06:45,214 basehttp 25716 8280 "GET /products/earphone1.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 18:06:45,215 basehttp 25716 11372 "GET /products/skincare1.jpg HTTP/1.1" 404 13539
[WARNING] 2025-07-29 18:06:45,217 log 25716 25816 Not Found: /products/decoration1.jpg
[WARNING] 2025-07-29 18:06:45,219 basehttp 25716 25816 "GET /products/decoration1.jpg HTTP/1.1" 404 13547
[WARNING] 2025-07-29 18:06:56,504 log 25716 22940 Not Found: /categories/clothing.jpg
[WARNING] 2025-07-29 18:06:56,508 log 25716 15128 Not Found: /categories/sports.jpg
[WARNING] 2025-07-29 18:06:56,513 log 25716 25048 Not Found: /categories/home.jpg
[WARNING] 2025-07-29 18:06:56,515 log 25716 25536 Not Found: /categories/digital.jpg
[WARNING] 2025-07-29 18:06:56,516 basehttp 25716 22940 "GET /categories/clothing.jpg HTTP/1.1" 404 13544
[WARNING] 2025-07-29 18:06:56,516 basehttp 25716 15128 "GET /categories/sports.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 18:06:56,519 log 25716 1840 Not Found: /categories/food.jpg
[WARNING] 2025-07-29 18:06:56,520 basehttp 25716 25048 "GET /categories/home.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 18:06:56,523 log 25716 5320 Not Found: /categories/beauty.jpg
[WARNING] 2025-07-29 18:06:56,523 basehttp 25716 25536 "GET /categories/digital.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 18:06:56,525 basehttp 25716 1840 "GET /categories/food.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 18:06:56,526 basehttp 25716 5320 "GET /categories/beauty.jpg HTTP/1.1" 404 13535
[WARNING] 2025-07-29 18:06:56,906 log 25716 26084 Not Found: /products/tshirt1.jpg
[WARNING] 2025-07-29 18:06:56,910 log 25716 25748 Not Found: /products/skincare1.jpg
[WARNING] 2025-07-29 18:06:56,914 log 25716 8872 Not Found: /products/decoration1.jpg
[WARNING] 2025-07-29 18:06:56,916 log 25716 18812 Not Found: /products/earphone1.jpg
[WARNING] 2025-07-29 18:06:56,917 basehttp 25716 26084 "GET /products/tshirt1.jpg HTTP/1.1" 404 13532
[WARNING] 2025-07-29 18:06:56,918 basehttp 25716 25748 "GET /products/skincare1.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 18:06:56,918 basehttp 25716 8872 "GET /products/decoration1.jpg HTTP/1.1" 404 13548
[WARNING] 2025-07-29 18:06:56,919 basehttp 25716 18812 "GET /products/earphone1.jpg HTTP/1.1" 404 13540
[INFO] 2025-07-29 18:10:34,814 basehttp 25716 25432 "GET /admin/products/product/ HTTP/1.1" 200 37495
[INFO] 2025-07-29 18:10:35,020 basehttp 25716 25432 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 18:10:38,145 basehttp 25716 25432 "GET /admin/core/banner/ HTTP/1.1" 200 33427
[INFO] 2025-07-29 18:10:38,301 basehttp 25716 25432 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 18:10:42,033 basehttp 25716 25432 "GET /admin/core/banner/add/ HTTP/1.1" 200 40652
[INFO] 2025-07-29 18:10:42,299 basehttp 25716 25432 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 18:11:33,327 basehttp 25716 25432 "POST /admin/core/banner/add/ HTTP/1.1" 302 0
[INFO] 2025-07-29 18:11:33,566 basehttp 25716 25432 "GET /admin/core/banner/add/ HTTP/1.1" 200 40902
[INFO] 2025-07-29 18:11:33,779 basehttp 25716 25432 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 18:11:46,994 basehttp 25716 25432 "POST /admin/core/banner/add/ HTTP/1.1" 302 0
[INFO] 2025-07-29 18:11:47,226 basehttp 25716 25432 "GET /admin/core/banner/add/ HTTP/1.1" 200 40899
[INFO] 2025-07-29 18:11:47,427 basehttp 25716 25432 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 18:11:59,066 basehttp 25716 25432 "POST /admin/core/banner/add/ HTTP/1.1" 302 0
[INFO] 2025-07-29 18:11:59,473 basehttp 25716 25432 "GET /admin/core/banner/ HTTP/1.1" 200 40652
[INFO] 2025-07-29 18:11:59,706 basehttp 25716 15892 "GET /media/banners/2025/07/29/image-1745882799452.png HTTP/1.1" 200 287365
[INFO] 2025-07-29 18:11:59,807 basehttp 25716 25432 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 18:11:59,912 basehttp 25716 25048 "GET /media/banners/2025/07/29/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_2025-05-13_073359_030.jpg HTTP/1.1" 200 47811
[INFO] 2025-07-29 18:11:59,997 basehttp 25716 15892 "GET /media/banners/2025/07/29/PixPin_2025-07-20_09-50-41.png HTTP/1.1" 200 781337
[WARNING] 2025-07-29 18:12:06,555 log 25716 18424 Not Found: /categories/clothing.jpg
[WARNING] 2025-07-29 18:12:06,557 basehttp 25716 18424 "GET /categories/clothing.jpg HTTP/1.1" 404 13544
[WARNING] 2025-07-29 18:12:06,562 log 25716 4916 Not Found: /categories/digital.jpg
[WARNING] 2025-07-29 18:12:06,565 log 25716 26004 Not Found: /categories/sports.jpg
[WARNING] 2025-07-29 18:12:06,568 log 25716 24780 Not Found: /categories/home.jpg
[WARNING] 2025-07-29 18:12:06,571 log 25716 4872 Not Found: /categories/beauty.jpg
[WARNING] 2025-07-29 18:12:06,574 log 25716 19716 Not Found: /categories/food.jpg
[WARNING] 2025-07-29 18:12:06,575 basehttp 25716 4916 "GET /categories/digital.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 18:12:06,576 basehttp 25716 26004 "GET /categories/sports.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 18:12:06,578 basehttp 25716 24780 "GET /categories/home.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 18:12:06,578 basehttp 25716 4872 "GET /categories/beauty.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 18:12:06,579 basehttp 25716 19716 "GET /categories/food.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 18:12:06,838 log 25716 2360 Not Found: /products/tshirt1.jpg
[WARNING] 2025-07-29 18:12:06,841 log 25716 20128 Not Found: /products/earphone1.jpg
[WARNING] 2025-07-29 18:12:06,842 basehttp 25716 2360 "GET /products/tshirt1.jpg HTTP/1.1" 404 13532
[WARNING] 2025-07-29 18:12:06,842 basehttp 25716 20128 "GET /products/earphone1.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 18:12:06,847 log 25716 19736 Not Found: /products/skincare1.jpg
[WARNING] 2025-07-29 18:12:06,850 log 25716 22880 Not Found: /products/decoration1.jpg
[WARNING] 2025-07-29 18:12:06,850 basehttp 25716 19736 "GET /products/skincare1.jpg HTTP/1.1" 404 13539
[WARNING] 2025-07-29 18:12:06,850 basehttp 25716 22880 "GET /products/decoration1.jpg HTTP/1.1" 404 13548
[WARNING] 2025-07-29 18:13:51,749 log 25716 26012 Not Found: /api/v1/banners/
[WARNING] 2025-07-29 18:13:51,750 basehttp 25716 26012 "GET /api/v1/banners/ HTTP/1.1" 404 13509
[INFO] 2025-07-29 18:15:55,313 basehttp 25716 26012 "GET /api/v1/core/banners/ HTTP/1.1" 200 21879
[INFO] 2025-07-29 18:15:55,419 basehttp 25716 2604 "GET /static/rest_framework/css/bootstrap-tweaks.css HTTP/1.1" 200 3426
[INFO] 2025-07-29 18:15:55,420 basehttp 25716 15436 "GET /static/rest_framework/css/prettify.css HTTP/1.1" 200 817
[INFO] 2025-07-29 18:15:55,420 basehttp 25716 1092 "GET /static/rest_framework/css/default.css HTTP/1.1" 200 1152
[INFO] 2025-07-29 18:15:55,423 basehttp 25716 26012 "GET /static/rest_framework/css/bootstrap.min.css HTTP/1.1" 200 121457
[INFO] 2025-07-29 18:15:55,445 basehttp 25716 22920 "GET /static/rest_framework/js/ajax-form.js HTTP/1.1" 200 3796
[INFO] 2025-07-29 18:15:55,475 basehttp 25716 2604 "GET /static/rest_framework/js/csrf.js HTTP/1.1" 200 1793
[INFO] 2025-07-29 18:15:55,476 basehttp 25716 1092 "GET /static/rest_framework/js/prettify-min.js HTTP/1.1" 200 13632
[INFO] 2025-07-29 18:15:55,477 basehttp 25716 15436 "GET /static/rest_framework/js/bootstrap.min.js HTTP/1.1" 200 39680
[INFO] 2025-07-29 18:15:55,498 basehttp 25716 22920 "GET /static/rest_framework/js/load-ajax-form.js HTTP/1.1" 200 59
[INFO] 2025-07-29 18:15:55,514 basehttp 25716 26012 "GET /static/rest_framework/js/default.js HTTP/1.1" 200 1268
[INFO] 2025-07-29 18:15:55,525 basehttp 25716 2604 "GET /static/rest_framework/img/grid.png HTTP/1.1" 200 1458
[INFO] 2025-07-29 18:15:55,535 basehttp 25716 15316 "GET /static/rest_framework/js/jquery-3.7.1.min.js HTTP/1.1" 200 87533
[INFO] 2025-07-29 18:16:22,908 basehttp 25716 15316 "GET /admin/core/banner/ HTTP/1.1" 200 40451
[INFO] 2025-07-29 18:16:23,064 basehttp 25716 15316 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-29 18:16:23,171 basehttp 25716 2604 "GET /media/banners/2025/07/29/image-1745882799452.png HTTP/1.1" 304 0
[INFO] 2025-07-29 18:16:23,266 basehttp 25716 26012 "GET /media/banners/2025/07/29/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_2025-05-13_073359_030.jpg HTTP/1.1" 304 0
[INFO] 2025-07-29 18:16:23,347 basehttp 25716 15316 "GET /media/banners/2025/07/29/PixPin_2025-07-20_09-50-41.png HTTP/1.1" 304 0
[INFO] 2025-07-29 18:17:09,259 autoreload 25716 24452 F:\zmkj-system\backend\zmkj\settings\development.py changed, reloading.
[INFO] 2025-07-29 18:17:10,221 autoreload 24656 9292 Watching for file changes with StatReloader
[WARNING] 2025-07-29 18:17:30,378 log 24656 24412 Not Found: /categories/beauty.jpg
[WARNING] 2025-07-29 18:17:30,378 log 24656 26552 Not Found: /categories/home.jpg
[WARNING] 2025-07-29 18:17:30,378 log 24656 21764 Not Found: /categories/sports.jpg
[WARNING] 2025-07-29 18:17:30,378 log 24656 26388 Not Found: /categories/clothing.jpg
[WARNING] 2025-07-29 18:17:30,378 log 24656 20564 Not Found: /categories/digital.jpg
[WARNING] 2025-07-29 18:17:30,378 log 24656 24820 Not Found: /categories/food.jpg
[WARNING] 2025-07-29 18:17:30,379 basehttp 24656 24412 "GET /categories/beauty.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 18:17:30,380 basehttp 24656 26552 "GET /categories/home.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 18:17:30,381 basehttp 24656 21764 "GET /categories/sports.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 18:17:30,382 basehttp 24656 26388 "GET /categories/clothing.jpg HTTP/1.1" 404 13544
[WARNING] 2025-07-29 18:17:30,383 basehttp 24656 20564 "GET /categories/digital.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 18:17:30,385 basehttp 24656 24820 "GET /categories/food.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 18:17:30,708 log 24656 23428 Not Found: /products/tshirt1.jpg
[WARNING] 2025-07-29 18:17:30,709 basehttp 24656 23428 "GET /products/tshirt1.jpg HTTP/1.1" 404 13532
[WARNING] 2025-07-29 18:17:30,714 log 24656 15316 Not Found: /products/earphone1.jpg
[WARNING] 2025-07-29 18:17:30,717 log 24656 1092 Not Found: /products/skincare1.jpg
[WARNING] 2025-07-29 18:17:30,720 log 24656 15436 Not Found: /products/decoration1.jpg
[WARNING] 2025-07-29 18:17:30,720 basehttp 24656 15316 "GET /products/earphone1.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 18:17:30,722 basehttp 24656 1092 "GET /products/skincare1.jpg HTTP/1.1" 404 13539
[WARNING] 2025-07-29 18:17:30,722 basehttp 24656 15436 "GET /products/decoration1.jpg HTTP/1.1" 404 13548
[INFO] 2025-07-29 18:18:55,572 basehttp 24656 12568 "GET /api/v1/core/banners/ HTTP/1.1" 200 595
[WARNING] 2025-07-29 18:19:35,558 log 24656 21680 Not Found: /categories/clothing.jpg
[WARNING] 2025-07-29 18:19:35,561 log 24656 10720 Not Found: /categories/digital.jpg
[WARNING] 2025-07-29 18:19:35,562 basehttp 24656 21680 "GET /categories/clothing.jpg HTTP/1.1" 404 13544
[WARNING] 2025-07-29 18:19:35,563 basehttp 24656 10720 "GET /categories/digital.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 18:19:35,566 log 24656 21092 Not Found: /categories/home.jpg
[WARNING] 2025-07-29 18:19:35,569 log 24656 22980 Not Found: /categories/food.jpg
[WARNING] 2025-07-29 18:19:35,573 log 24656 22692 Not Found: /categories/beauty.jpg
[WARNING] 2025-07-29 18:19:35,576 log 24656 9272 Not Found: /categories/sports.jpg
[WARNING] 2025-07-29 18:19:35,578 basehttp 24656 21092 "GET /categories/home.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 18:19:35,578 basehttp 24656 22980 "GET /categories/food.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-29 18:19:35,578 basehttp 24656 22692 "GET /categories/beauty.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 18:19:35,579 basehttp 24656 9272 "GET /categories/sports.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-29 18:19:35,843 log 24656 23028 Not Found: /products/tshirt1.jpg
[WARNING] 2025-07-29 18:19:35,844 basehttp 24656 23028 "GET /products/tshirt1.jpg HTTP/1.1" 404 13532
[WARNING] 2025-07-29 18:19:35,849 log 24656 1984 Not Found: /products/earphone1.jpg
[WARNING] 2025-07-29 18:19:35,852 log 24656 26080 Not Found: /products/decoration1.jpg
[WARNING] 2025-07-29 18:19:35,855 log 24656 26168 Not Found: /products/skincare1.jpg
[WARNING] 2025-07-29 18:19:35,856 basehttp 24656 1984 "GET /products/earphone1.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-29 18:19:35,856 basehttp 24656 26080 "GET /products/decoration1.jpg HTTP/1.1" 404 13548
[WARNING] 2025-07-29 18:19:35,856 basehttp 24656 26168 "GET /products/skincare1.jpg HTTP/1.1" 404 13539
[INFO] 2025-07-29 18:35:51,043 basehttp 24656 15660 "GET /admin/ HTTP/1.1" 200 36529
[INFO] 2025-07-29 19:00:06,847 basehttp 24656 24300 "GET /admin/distribution/ HTTP/1.1" 200 20174
[INFO] 2025-07-29 19:00:10,076 basehttp 24656 24300 "GET /admin/ HTTP/1.1" 200 36529
[WARNING] 2025-07-29 19:55:14,734 log 24656 24252 Not Found: /
[WARNING] 2025-07-29 19:55:14,735 basehttp 24656 24252 "GET / HTTP/1.1" 404 13433
[WARNING] 2025-07-29 19:55:15,518 log 24656 24252 Not Found: /
[WARNING] 2025-07-29 19:55:15,519 basehttp 24656 24252 "GET / HTTP/1.1" 404 13433
[INFO] 2025-07-30 17:48:18,861 autoreload 9800 27520 Watching for file changes with StatReloader
[INFO] 2025-07-30 17:59:51,434 basehttp 9800 23792 "GET /api/v1/core/banners/?position=home HTTP/1.1" 200 598
[INFO] 2025-07-30 18:00:18,371 basehttp 9800 23792 "GET /api/v1/core/banners/?position=home HTTP/1.1" 200 598
[INFO] 2025-07-30 18:01:48,998 basehttp 9800 23792 "GET /api/v1/core/banners/?position=home HTTP/1.1" 200 598
[INFO] 2025-07-30 18:04:20,093 basehttp 9800 23792 - Broken pipe from ('127.0.0.1', 63217)
[INFO] 2025-07-30 18:04:20,363 basehttp 9800 5872 "GET /api/v1/core/banners/?position=home HTTP/1.1" 200 598
[INFO] 2025-07-30 18:05:19,731 basehttp 9800 5872 "GET /api/v1/core/banners/?position=home HTTP/1.1" 200 598
[INFO] 2025-07-30 18:05:26,548 basehttp 9800 5872 "GET /api/v1/core/banners/?position=home HTTP/1.1" 200 598
[INFO] 2025-07-30 18:06:24,839 basehttp 9800 5872 "GET /api/v1/core/banners/?position=home HTTP/1.1" 200 598
[INFO] 2025-07-30 18:06:50,548 basehttp 9800 5872 "GET /api/v1/core/banners/?position=home HTTP/1.1" 200 598
[INFO] 2025-07-30 18:07:15,848 basehttp 9800 5872 "GET /api/v1/core/banners/?position=home HTTP/1.1" 200 598
[INFO] 2025-07-30 18:07:25,922 basehttp 9800 5872 "GET /api/v1/core/banners/?position=home HTTP/1.1" 200 598
[WARNING] 2025-07-30 18:07:26,383 log 9800 22736 Not Found: /categories/home.jpg
[WARNING] 2025-07-30 18:07:26,384 log 9800 28492 Not Found: /categories/clothing.jpg
[WARNING] 2025-07-30 18:07:26,384 log 9800 20812 Not Found: /categories/food.jpg
[WARNING] 2025-07-30 18:07:26,384 log 9800 29364 Not Found: /categories/beauty.jpg
[WARNING] 2025-07-30 18:07:26,384 log 9800 27996 Not Found: /categories/digital.jpg
[WARNING] 2025-07-30 18:07:26,384 log 9800 25692 Not Found: /categories/sports.jpg
[WARNING] 2025-07-30 18:07:26,384 basehttp 9800 28492 "GET /categories/clothing.jpg HTTP/1.1" 404 13544
[WARNING] 2025-07-30 18:07:26,385 basehttp 9800 22736 "GET /categories/home.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-30 18:07:26,385 basehttp 9800 29364 "GET /categories/beauty.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-30 18:07:26,386 basehttp 9800 20812 "GET /categories/food.jpg HTTP/1.1" 404 13528
[WARNING] 2025-07-30 18:07:26,388 basehttp 9800 25692 "GET /categories/sports.jpg HTTP/1.1" 404 13536
[WARNING] 2025-07-30 18:07:26,389 basehttp 9800 27996 "GET /categories/digital.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-30 18:07:26,733 log 9800 24264 Not Found: /products/earphone1.jpg
[WARNING] 2025-07-30 18:07:26,734 basehttp 9800 24264 "GET /products/earphone1.jpg HTTP/1.1" 404 13540
[WARNING] 2025-07-30 18:07:26,736 log 9800 12820 Not Found: /products/tshirt1.jpg
[WARNING] 2025-07-30 18:07:26,740 log 9800 26264 Not Found: /products/decoration1.jpg
[WARNING] 2025-07-30 18:07:26,744 log 9800 17352 Not Found: /products/skincare1.jpg
[WARNING] 2025-07-30 18:07:26,744 basehttp 9800 12820 "GET /products/tshirt1.jpg HTTP/1.1" 404 13532
[WARNING] 2025-07-30 18:07:26,745 basehttp 9800 26264 "GET /products/decoration1.jpg HTTP/1.1" 404 13547
[WARNING] 2025-07-30 18:07:26,745 basehttp 9800 17352 "GET /products/skincare1.jpg HTTP/1.1" 404 13539
[INFO] 2025-07-30 18:21:34,210 autoreload 9800 27520 F:\zmkj-system\backend\zmkj\settings\base.py changed, reloading.
[INFO] 2025-07-30 18:21:35,058 autoreload 23952 11592 Watching for file changes with StatReloader
[INFO] 2025-07-30 18:21:52,131 autoreload 23952 11592 F:\zmkj-system\backend\zmkj\urls.py changed, reloading.
[INFO] 2025-07-30 18:21:52,569 autoreload 27656 26796 Watching for file changes with StatReloader
[INFO] 2025-07-30 18:22:37,615 autoreload 6660 16676 Watching for file changes with StatReloader
[WARNING] 2025-07-30 18:22:48,922 log 6660 17016 Not Found: /
[WARNING] 2025-07-30 18:22:48,923 basehttp 6660 17016 "GET / HTTP/1.1" 404 13585
[INFO] 2025-07-30 18:22:48,952 basehttp 6660 17016 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
[INFO] 2025-07-30 18:22:48,952 basehttp 6660 4728 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
[INFO] 2025-07-30 18:22:49,023 basehttp 6660 4728 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
[INFO] 2025-07-30 18:22:49,105 basehttp 6660 4728 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
[WARNING] 2025-07-30 18:22:49,264 log 6660 4728 Not Found: /favicon.ico
[WARNING] 2025-07-30 18:22:49,265 basehttp 6660 4728 "GET /favicon.ico HTTP/1.1" 404 13646
[INFO] 2025-07-30 18:22:53,709 basehttp 6660 21248 "GET /admin/ HTTP/1.1" 200 36529
[INFO] 2025-07-30 18:22:53,718 basehttp 6660 21248 "GET /static/admin/css/base.css HTTP/1.1" 304 0
[INFO] 2025-07-30 18:22:53,718 basehttp 6660 28108 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
[INFO] 2025-07-30 18:22:53,734 basehttp 6660 19684 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
[INFO] 2025-07-30 18:22:53,783 basehttp 6660 19684 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
[INFO] 2025-07-30 18:22:53,783 basehttp 6660 21248 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
[INFO] 2025-07-30 18:22:53,783 basehttp 6660 28108 "GET /static/admin/css/dashboard.css HTTP/1.1" 304 0
[INFO] 2025-07-30 18:22:53,792 basehttp 6660 28108 "GET /static/admin/css/themes.css HTTP/1.1" 304 0
[INFO] 2025-07-30 18:22:53,797 basehttp 6660 28108 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
[INFO] 2025-07-30 18:22:53,810 basehttp 6660 28108 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
[INFO] 2025-07-30 18:22:53,810 basehttp 6660 21248 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
[INFO] 2025-07-30 18:22:53,848 basehttp 6660 28108 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
[INFO] 2025-07-30 18:22:53,848 basehttp 6660 21248 "GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 304 0
[INFO] 2025-07-30 18:25:03,657 autoreload 6660 16676 F:\zmkj-system\backend\apps\ai\admin.py changed, reloading.
[INFO] 2025-07-30 18:25:04,205 autoreload 26204 23352 Watching for file changes with StatReloader
[INFO] 2025-07-30 18:25:15,039 autoreload 26204 23352 F:\zmkj-system\backend\apps\ai\admin.py changed, reloading.
[INFO] 2025-07-30 18:25:15,476 autoreload 29680 17484 Watching for file changes with StatReloader
[INFO] 2025-07-30 18:25:26,330 autoreload 29680 17484 F:\zmkj-system\backend\apps\ai\admin.py changed, reloading.
[INFO] 2025-07-30 18:25:26,763 autoreload 16648 28684 Watching for file changes with StatReloader
[INFO] 2025-07-30 18:25:36,571 autoreload 16648 28684 F:\zmkj-system\backend\apps\ai\admin.py changed, reloading.
[INFO] 2025-07-30 18:25:37,022 autoreload 8444 21432 Watching for file changes with StatReloader
[INFO] 2025-07-30 18:25:46,837 autoreload 8444 21432 F:\zmkj-system\backend\apps\ai\admin.py changed, reloading.
[INFO] 2025-07-30 18:25:47,267 autoreload 10836 9488 Watching for file changes with StatReloader
[INFO] 2025-07-30 18:25:57,079 autoreload 10836 9488 F:\zmkj-system\backend\apps\ai\admin.py changed, reloading.
[INFO] 2025-07-30 18:25:57,493 autoreload 12316 9628 Watching for file changes with StatReloader
[INFO] 2025-07-30 18:26:07,294 autoreload 12316 9628 F:\zmkj-system\backend\apps\ai\admin.py changed, reloading.
[INFO] 2025-07-30 18:26:07,764 autoreload 2700 21540 Watching for file changes with StatReloader
[INFO] 2025-07-30 18:26:20,715 autoreload 2700 21540 F:\zmkj-system\backend\apps\ai\admin.py changed, reloading.
[INFO] 2025-07-30 18:26:21,148 autoreload 22656 23076 Watching for file changes with StatReloader
[INFO] 2025-07-30 18:29:45,399 autoreload 29588 7856 Watching for file changes with StatReloader
[INFO] 2025-07-30 18:29:49,251 basehttp 29588 24720 "GET /admin/ HTTP/1.1" 200 74439
[INFO] 2025-07-30 18:29:49,267 basehttp 29588 24720 "GET /static/admin/css/custom_admin.css HTTP/1.1" 200 6989
[INFO] 2025-07-30 18:29:56,595 basehttp 29588 24720 "GET /admin/distribution/ HTTP/1.1" 200 47301
[INFO] 2025-07-30 18:29:58,499 basehttp 29588 24720 "GET /admin/products/ HTTP/1.1" 200 46078
[INFO] 2025-07-30 18:29:59,699 basehttp 29588 24720 "GET /admin/ai/ HTTP/1.1" 200 46014
[INFO] 2025-07-30 18:30:05,927 basehttp 29588 24720 "GET /admin/ HTTP/1.1" 200 74439
[INFO] 2025-07-30 18:30:05,946 basehttp 29588 24720 "GET /static/admin/css/custom_admin.css HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:33,618 basehttp 29588 24720 "GET /admin/admin/logentry/ HTTP/1.1" 200 47192
[INFO] 2025-07-30 18:30:33,638 basehttp 29588 24720 "GET /static/admin/css/changelists.css HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:33,639 basehttp 29588 23268 "GET /static/admin/css/custom_admin.css HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:33,641 basehttp 29588 7604 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:33,642 basehttp 29588 24720 "GET /static/admin/js/actions.js HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:33,643 basehttp 29588 27840 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:33,643 basehttp 29588 23268 "GET /static/admin/js/urlify.js HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:33,644 basehttp 29588 7604 "GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:33,645 basehttp 29588 24720 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:33,653 basehttp 29588 20064 "GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:33,657 basehttp 29588 3056 "GET /static/admin/js/core.js HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:33,659 basehttp 29588 24720 "GET /static/admin/img/search.svg HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:33,685 basehttp 29588 24720 "GET /static/admin/js/filters.js HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:33,705 basehttp 29588 24720 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:47,098 basehttp 29588 24720 "GET /admin/ai/aitask/ HTTP/1.1" 200 36172
[INFO] 2025-07-30 18:30:47,295 basehttp 29588 24720 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-30 18:30:48,311 basehttp 29588 24720 "GET /admin/ai/aiserviceconfig/ HTTP/1.1" 200 35977
[INFO] 2025-07-30 18:30:48,469 basehttp 29588 24720 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-30 18:30:52,747 basehttp 29588 24720 "GET /admin/ai/aiserviceconfig/add/ HTTP/1.1" 200 39815
[INFO] 2025-07-30 18:30:52,778 basehttp 29588 3056 "GET /static/admin/css/custom_admin.css HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:52,778 basehttp 29588 7604 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:52,878 basehttp 29588 24720 "GET /static/admin/css/forms.css HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:52,958 basehttp 29588 3056 "GET /static/admin/css/widgets.css HTTP/1.1" 304 0
[INFO] 2025-07-30 18:30:53,248 basehttp 29588 20064 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-30 18:30:53,265 basehttp 29588 24720 "GET /static/admin/js/change_form.js HTTP/1.1" 304 0
[INFO] 2025-07-30 18:31:02,803 basehttp 29588 20064 "GET /admin/ai/recommendationconfig/ HTTP/1.1" 200 36008
[INFO] 2025-07-30 18:31:02,963 basehttp 29588 20064 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-30 18:31:04,320 basehttp 29588 20064 "GET /admin/ai/aitask/ HTTP/1.1" 200 36172
[INFO] 2025-07-30 18:31:04,473 basehttp 29588 20064 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
[INFO] 2025-07-30 18:31:05,459 basehttp 29588 20064 "GET /admin/ HTTP/1.1" 200 74439
