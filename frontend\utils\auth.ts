/**
 * 认证相关工具函数
 * 处理用户登录状态、Token管理等
 */

import { getStorage, setStorage, removeStorage } from './storage'

// Token存储键名
const TOKEN_KEY = 'user_token'
const USER_INFO_KEY = 'user_info'
const LOGIN_TIME_KEY = 'login_time'

// Token过期时间（7天，单位：毫秒）
const TOKEN_EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000

// 用户信息接口
export interface UserInfo {
  id: number
  username: string
  nickname: string
  avatar: string
  phone: string
  email?: string
  is_distributor: boolean
  distributor_level?: number
  parent_id?: number
  created_at: string
  updated_at: string
}

/**
 * 设置Token
 * @param token 用户Token
 */
export function setToken(token: string): void {
  setStorage(TOKEN_KEY, token)
  setStorage(LOGIN_TIME_KEY, Date.now())
}

/**
 * 获取Token
 * @returns Token字符串或null
 */
export function getToken(): string | null {
  const token = getStorage(TOKEN_KEY)
  const loginTime = getStorage(LOGIN_TIME_KEY)
  
  if (!token || !loginTime) {
    return null
  }
  
  // 检查Token是否过期
  const now = Date.now()
  if (now - loginTime > TOKEN_EXPIRE_TIME) {
    removeToken()
    return null
  }
  
  return token
}

/**
 * 移除Token
 */
export function removeToken(): void {
  removeStorage(TOKEN_KEY)
  removeStorage(LOGIN_TIME_KEY)
  removeStorage(USER_INFO_KEY)
}

/**
 * 检查Token是否有效
 * @returns 是否有效
 */
export function isTokenValid(): boolean {
  return getToken() !== null
}

/**
 * 设置用户信息
 * @param userInfo 用户信息
 */
export function setUserInfo(userInfo: UserInfo): void {
  setStorage(USER_INFO_KEY, userInfo)
}

/**
 * 获取用户信息
 * @returns 用户信息或null
 */
export function getUserInfo(): UserInfo | null {
  return getStorage(USER_INFO_KEY)
}

/**
 * 移除用户信息
 */
export function removeUserInfo(): void {
  removeStorage(USER_INFO_KEY)
}

/**
 * 检查用户是否已登录
 * @returns 是否已登录
 */
export function isLoggedIn(): boolean {
  return isTokenValid() && getUserInfo() !== null
}

/**
 * 用户登录
 * @param token 用户Token
 * @param userInfo 用户信息
 */
export function login(token: string, userInfo: UserInfo): void {
  setToken(token)
  setUserInfo(userInfo)
}

/**
 * 用户登出
 */
export function logout(): void {
  removeToken()
  removeUserInfo()
}

/**
 * 检查用户是否为分销商
 * @returns 是否为分销商
 */
export function isDistributor(): boolean {
  const userInfo = getUserInfo()
  return userInfo?.is_distributor || false
}

/**
 * 获取用户分销等级
 * @returns 分销等级
 */
export function getDistributorLevel(): number {
  const userInfo = getUserInfo()
  return userInfo?.distributor_level || 0
}

/**
 * 获取用户上级ID
 * @returns 上级用户ID
 */
export function getParentId(): number | null {
  const userInfo = getUserInfo()
  return userInfo?.parent_id || null
}

/**
 * 微信登录
 * @returns Promise<{code: string, userInfo?: any}>
 */
export function wxLogin(): Promise<{code: string, userInfo?: any}> {
  return new Promise((resolve, reject) => {
    // 获取微信登录code
    uni.login({
      provider: 'weixin',
      success: (loginRes) => {
        if (loginRes.code) {
          // 获取用户信息
          uni.getUserProfile({
            desc: '用于完善用户资料',
            success: (userRes) => {
              resolve({
                code: loginRes.code,
                userInfo: userRes.userInfo
              })
            },
            fail: () => {
              // 即使获取用户信息失败，也返回code
              resolve({
                code: loginRes.code
              })
            }
          })
        } else {
          reject(new Error('获取微信登录code失败'))
        }
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

/**
 * 检查微信授权状态
 * @param scope 授权范围
 * @returns Promise<boolean>
 */
export function checkWxAuth(scope: string): Promise<boolean> {
  return new Promise((resolve) => {
    uni.getSetting({
      success: (res) => {
        resolve(!!res.authSetting[scope])
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

/**
 * 请求微信授权
 * @param scope 授权范围
 * @returns Promise<boolean>
 */
export function requestWxAuth(scope: string): Promise<boolean> {
  return new Promise((resolve) => {
    uni.authorize({
      scope,
      success: () => {
        resolve(true)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

/**
 * 跳转到登录页面
 * @param redirect 登录成功后的重定向页面
 */
export function redirectToLogin(redirect?: string): void {
  const url = redirect 
    ? `/pages/auth/login?redirect=${encodeURIComponent(redirect)}`
    : '/pages/auth/login'
    
  uni.reLaunch({ url })
}

/**
 * 检查登录状态，未登录则跳转到登录页
 * @param redirect 登录成功后的重定向页面
 * @returns 是否已登录
 */
export function requireLogin(redirect?: string): boolean {
  if (!isLoggedIn()) {
    redirectToLogin(redirect)
    return false
  }
  return true
}

/**
 * 获取Token剩余有效时间（毫秒）
 * @returns 剩余时间或0
 */
export function getTokenRemainingTime(): number {
  const loginTime = getStorage(LOGIN_TIME_KEY)
  if (!loginTime) {
    return 0
  }
  
  const now = Date.now()
  const remaining = TOKEN_EXPIRE_TIME - (now - loginTime)
  return Math.max(0, remaining)
}

/**
 * 检查Token是否即将过期（1天内）
 * @returns 是否即将过期
 */
export function isTokenExpiringSoon(): boolean {
  const remaining = getTokenRemainingTime()
  const oneDayInMs = 24 * 60 * 60 * 1000
  return remaining > 0 && remaining < oneDayInMs
}

/**
 * 刷新Token（如果后端支持）
 * @returns Promise<boolean>
 */
export async function refreshToken(): Promise<boolean> {
  try {
    // 这里需要调用后端的刷新Token接口
    // const response = await http.post('/auth/refresh-token')
    // if (response.success) {
    //   setToken(response.data.token)
    //   return true
    // }
    return false
  } catch (error) {
    console.error('刷新Token失败:', error)
    return false
  }
}

export default {
  setToken,
  getToken,
  removeToken,
  isTokenValid,
  setUserInfo,
  getUserInfo,
  removeUserInfo,
  isLoggedIn,
  login,
  logout,
  isDistributor,
  getDistributorLevel,
  getParentId,
  wxLogin,
  checkWxAuth,
  requestWxAuth,
  redirectToLogin,
  requireLogin,
  getTokenRemainingTime,
  isTokenExpiringSoon,
  refreshToken
}
