from django.test import TestCase
from django.contrib.auth import get_user_model
from apps.products.models import ProductCategory, Product
from apps.orders.models import Order, OrderItem, Payment

User = get_user_model()

class OrderModelTest(TestCase):
    """订单模型测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建用户
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # 创建产品分类和产品
        self.category = ProductCategory.objects.create(
            name='电子产品'
        )
        self.product = Product.objects.create(
            name='iPhone 15',
            category=self.category,
            sku='IP15-256G-BLK',
            price=7999.00,
            stock=100
        )
        
        # 创建订单
        self.order = Order.objects.create(
            user=self.user,
            total_amount=7999.00,
            final_amount=7999.00,
            recipient_name='张三',
            recipient_phone='13800138000',
            shipping_address='广东省深圳市南山区科技园'
        )
    
    def test_order_creation(self):
        """测试订单创建"""
        self.assertEqual(self.order.user, self.user)
        self.assertEqual(self.order.total_amount, 7999.00)
        self.assertEqual(self.order.final_amount, 7999.00)
        self.assertEqual(self.order.status, 'pending')
        self.assertIsNotNone(self.order.order_no)
        self.assertTrue(self.order.order_no.startswith('ZM'))
    
    def test_order_str_representation(self):
        """测试订单字符串表示"""
        expected = f"订单{self.order.order_no} - {self.user.username}"
        self.assertEqual(str(self.order), expected)
    
    def test_order_can_cancel(self):
        """测试订单是否可以取消"""
        # 待支付状态可以取消
        self.order.status = 'pending'
        self.assertTrue(self.order.can_cancel())
        
        # 已支付状态可以取消
        self.order.status = 'paid'
        self.assertTrue(self.order.can_cancel())
        
        # 已发货状态不能取消
        self.order.status = 'shipped'
        self.assertFalse(self.order.can_cancel())
    
    def test_order_status_display_color(self):
        """测试订单状态显示颜色"""
        self.order.status = 'pending'
        color = self.order.get_status_display_color()
        self.assertEqual(color, '#f39c12')  # 橙色


class OrderItemModelTest(TestCase):
    """订单项模型测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建用户
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # 创建产品分类和产品
        self.category = ProductCategory.objects.create(
            name='电子产品'
        )
        self.product = Product.objects.create(
            name='iPhone 15',
            category=self.category,
            sku='IP15-256G-BLK',
            price=7999.00,
            stock=100
        )
        
        # 创建订单
        self.order = Order.objects.create(
            user=self.user,
            total_amount=7999.00,
            final_amount=7999.00,
            recipient_name='张三',
            recipient_phone='13800138000',
            shipping_address='广东省深圳市南山区科技园'
        )
        
        # 创建订单项
        self.order_item = OrderItem.objects.create(
            order=self.order,
            product=self.product,
            product_name=self.product.name,
            product_sku=self.product.sku,
            product_price=self.product.price,
            quantity=1
        )
    
    def test_order_item_creation(self):
        """测试订单项创建"""
        self.assertEqual(self.order_item.order, self.order)
        self.assertEqual(self.order_item.product, self.product)
        self.assertEqual(self.order_item.quantity, 1)
        self.assertEqual(self.order_item.subtotal, 7999.00)
    
    def test_order_item_str_representation(self):
        """测试订单项字符串表示"""
        expected = f"{self.order.order_no} - {self.product.name} × {self.order_item.quantity}"
        self.assertEqual(str(self.order_item), expected)


class PaymentModelTest(TestCase):
    """支付记录模型测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建用户
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # 创建产品分类和产品
        self.category = ProductCategory.objects.create(
            name='电子产品'
        )
        self.product = Product.objects.create(
            name='iPhone 15',
            category=self.category,
            sku='IP15-256G-BLK',
            price=7999.00,
            stock=100
        )
        
        # 创建订单
        self.order = Order.objects.create(
            user=self.user,
            total_amount=7999.00,
            final_amount=7999.00,
            recipient_name='张三',
            recipient_phone='13800138000',
            shipping_address='广东省深圳市南山区科技园'
        )
        
        # 创建支付记录
        self.payment = Payment.objects.create(
            order=self.order,
            payment_method='wechat',
            amount=7999.00,
            status='pending'
        )
    
    def test_payment_creation(self):
        """测试支付记录创建"""
        self.assertEqual(self.payment.order, self.order)
        self.assertEqual(self.payment.payment_method, 'wechat')
        self.assertEqual(self.payment.amount, 7999.00)
        self.assertEqual(self.payment.status, 'pending')
        self.assertIsNotNone(self.payment.payment_no)
        self.assertTrue(self.payment.payment_no.startswith('PAY'))
    
    def test_payment_str_representation(self):
        """测试支付记录字符串表示"""
        expected = f"支付{self.payment.payment_no} - {self.order.order_no}"
        self.assertEqual(str(self.payment), expected)
