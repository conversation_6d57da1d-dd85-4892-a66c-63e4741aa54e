"""
智梦科技系统开发环境配置
创建时间：2025年7月24日
"""

from .base import *

# 开发环境特定设置
DEBUG = True
ALLOWED_HOSTS = ['localhost', '127.0.0.1', 'zmkj.nat100.top']

# 开发环境数据库 - 使用SQLite
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# 开发环境缓存 - 可选择使用Redis或内存缓存
if config('USE_REDIS_CACHE', default=False, cast=bool):
    CACHES = {
        'default': {
            'BACKEND': 'django.core.cache.backends.redis.RedisCache',
            'LOCATION': f'redis://{config("REDIS_HOST", default="localhost")}:{config("REDIS_PORT", default="6379")}/{config("REDIS_DB", default="0")}',
            'OPTIONS': {
                'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            },
            'KEY_PREFIX': 'zmkj-dev',
            'TIMEOUT': 300,
        }
    }
else:
    CACHES = {
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'zmkj-dev-cache',
        }
    }

# 开发环境邮件后端 - 控制台输出
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# 开发工具
INSTALLED_APPS += [
    'debug_toolbar',
]

MIDDLEWARE = [
    'debug_toolbar.middleware.DebugToolbarMiddleware',
] + MIDDLEWARE

# Debug Toolbar配置
INTERNAL_IPS = [
    '127.0.0.1',
    'localhost',
]

DEBUG_TOOLBAR_CONFIG = {
    'DISABLE_PANELS': [
        'debug_toolbar.panels.redirects.RedirectsPanel',
    ],
    'SHOW_TEMPLATE_CONTEXT': True,
}

# CORS设置 - 开发环境允许所有源，支持http和https
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True
# 明确允许的源（支持http和https）
CORS_ALLOWED_ORIGINS = [
    'http://zmkj.nat100.top',
    'https://zmkj.nat100.top',
    'http://localhost:8080',
    'https://localhost:8080',
    'http://127.0.0.1:8080',
    'https://127.0.0.1:8080',
]

# 允许的请求头
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

# 允许的HTTP方法
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

# 静态文件设置
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# 日志级别调整
LOGGING['loggers']['django']['level'] = 'DEBUG'
LOGGING['loggers']['zmkj']['level'] = 'DEBUG'

# 开发环境安全设置放宽
SECURE_SSL_REDIRECT = False
SECURE_BROWSER_XSS_FILTER = False
SECURE_CONTENT_TYPE_NOSNIFF = False
X_FRAME_OPTIONS = 'SAMEORIGIN'

# Session设置
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# 文件上传限制放宽
FILE_UPLOAD_MAX_MEMORY_SIZE = 10485760  # 10MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 10485760  # 10MB

print("🏗️ 智梦科技系统 - 开发环境已加载") 