"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_config = require("../../utils/config.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "test",
  setup(__props) {
    const bannerLoading = common_vendor.ref(false);
    const bannerError = common_vendor.ref("");
    const bannerData = common_vendor.ref([]);
    const config = common_vendor.ref(utils_config.CONFIG);
    const currentEnv = common_vendor.ref(utils_config.getEnv());
    const testBannerApi = () => {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        bannerLoading.value = true;
        bannerError.value = "";
        bannerData.value = [];
        try {
          common_vendor.index.__f__("log", "at pages/test/test.uvue:58", "开始测试轮播图API...");
          common_vendor.index.__f__("log", "at pages/test/test.uvue:59", "API地址:", config.value.API_BASE_URL);
          const response = yield utils_request.http.get("/core/banners/", new UTSJSONObject({
            position: "home"
          }));
          common_vendor.index.__f__("log", "at pages/test/test.uvue:65", "API响应:", response);
          if (response.code === 0 && response.data) {
            bannerData.value = response.data;
            common_vendor.index.__f__("log", "at pages/test/test.uvue:69", "轮播图数据:", bannerData.value);
          } else {
            bannerError.value = response.message || "获取数据失败";
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/test/test.uvue:74", "API调用失败:", error);
          bannerError.value = error.message || "网络请求失败";
        } finally {
          bannerLoading.value = false;
        }
      });
    };
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at pages/test/test.uvue:83", "测试页面已加载");
      common_vendor.index.__f__("log", "at pages/test/test.uvue:84", "当前配置:", config.value);
      common_vendor.index.__f__("log", "at pages/test/test.uvue:85", "当前协议检测结果:", config.value.API_BASE_URL.startsWith("http://") ? "HTTP" : "HTTPS");
      testBannerApi();
    });
    return (_ctx = null, _cache = null) => {
      const __returned__ = common_vendor.e(new UTSJSONObject({
        a: common_vendor.o(testBannerApi),
        b: bannerLoading.value
      }), bannerLoading.value ? new UTSJSONObject({}) : new UTSJSONObject({}), new UTSJSONObject({
        c: bannerError.value
      }), bannerError.value ? new UTSJSONObject({
        d: common_vendor.t(bannerError.value)
      }) : new UTSJSONObject({}), new UTSJSONObject({
        e: bannerData.value.length > 0
      }), bannerData.value.length > 0 ? new UTSJSONObject({
        f: common_vendor.f(bannerData.value, (banner = null, index = null, i0 = null) => {
          return new UTSJSONObject({
            a: common_vendor.t(banner.title),
            b: common_vendor.t(banner.image),
            c: index
          });
        })
      }) : new UTSJSONObject({}), new UTSJSONObject({
        g: common_vendor.t(config.value.API_BASE_URL),
        h: common_vendor.t(config.value.CDN_BASE_URL),
        i: common_vendor.t(currentEnv.value),
        j: common_vendor.sei(common_vendor.gei(_ctx, ""), "view")
      }));
      return __returned__;
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f37d094f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/test/test.js.map
