# Generated by Django 5.2.4 on 2025-07-30 10:29

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AIServiceConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('name', models.CharField(max_length=100, verbose_name='服务名称')),
                ('service_type', models.CharField(choices=[('chatbot', '智能客服'), ('recommendation', '产品推荐'), ('analysis', '行为分析'), ('content_generation', '内容生成'), ('data_mining', '数据挖掘')], max_length=50, verbose_name='服务类型')),
                ('api_endpoint', models.URLField(blank=True, verbose_name='API端点')),
                ('api_key', models.CharField(blank=True, max_length=255, verbose_name='API密钥')),
                ('config_data', models.JSONField(blank=True, default=dict, verbose_name='配置数据')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': 'AI服务配置',
                'verbose_name_plural': 'AI服务配置',
                'db_table': 'ai_service_config',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AITask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('name', models.CharField(max_length=200, verbose_name='任务名称')),
                ('task_type', models.CharField(choices=[('recommendation_training', '推荐模型训练'), ('behavior_analysis', '行为分析'), ('content_generation', '内容生成'), ('data_mining', '数据挖掘'), ('model_evaluation', '模型评估')], max_length=50, verbose_name='任务类型')),
                ('status', models.CharField(choices=[('pending', '待执行'), ('running', '执行中'), ('completed', '已完成'), ('failed', '执行失败'), ('cancelled', '已取消')], default='pending', max_length=20, verbose_name='任务状态')),
                ('parameters', models.JSONField(blank=True, default=dict, verbose_name='任务参数')),
                ('result_data', models.JSONField(blank=True, default=dict, verbose_name='结果数据')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('progress', models.IntegerField(default=0, verbose_name='进度百分比')),
            ],
            options={
                'verbose_name': 'AI任务',
                'verbose_name_plural': 'AI任务',
                'db_table': 'ai_task',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RecommendationConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('name', models.CharField(max_length=100, verbose_name='配置名称')),
                ('algorithm_type', models.CharField(choices=[('collaborative', '协同过滤'), ('content_based', '基于内容'), ('hybrid', '混合推荐'), ('popularity', '热门推荐'), ('random', '随机推荐')], max_length=50, verbose_name='算法类型')),
                ('parameters', models.JSONField(default=dict, verbose_name='算法参数')),
                ('weight', models.FloatField(default=1.0, verbose_name='权重')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '推荐算法配置',
                'verbose_name_plural': '推荐算法配置',
                'db_table': 'ai_recommendation_config',
                'ordering': ['-weight', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ChatbotConversation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('session_id', models.CharField(max_length=100, unique=True, verbose_name='会话ID')),
                ('status', models.CharField(choices=[('active', '进行中'), ('closed', '已结束'), ('transferred', '已转人工')], default='active', max_length=20, verbose_name='会话状态')),
                ('start_time', models.DateTimeField(auto_now_add=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '智能客服对话',
                'verbose_name_plural': '智能客服对话',
                'db_table': 'ai_chatbot_conversation',
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='ChatbotMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('message_type', models.CharField(choices=[('user', '用户消息'), ('bot', '机器人回复'), ('system', '系统消息')], max_length=20, verbose_name='消息类型')),
                ('content', models.TextField(verbose_name='消息内容')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='ai.chatbotconversation', verbose_name='对话')),
            ],
            options={
                'verbose_name': '智能客服消息',
                'verbose_name_plural': '智能客服消息',
                'db_table': 'ai_chatbot_message',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserBehaviorLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('session_id', models.CharField(blank=True, max_length=100, verbose_name='会话ID')),
                ('action_type', models.CharField(choices=[('view', '浏览'), ('click', '点击'), ('purchase', '购买'), ('search', '搜索'), ('share', '分享'), ('favorite', '收藏'), ('comment', '评论')], max_length=20, verbose_name='行为类型')),
                ('target_type', models.CharField(max_length=50, verbose_name='目标类型')),
                ('target_id', models.CharField(max_length=100, verbose_name='目标ID')),
                ('context_data', models.JSONField(blank=True, default=dict, verbose_name='上下文数据')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户行为日志',
                'verbose_name_plural': '用户行为日志',
                'db_table': 'ai_user_behavior_log',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'action_type'], name='ai_user_beh_user_id_dabd85_idx'), models.Index(fields=['target_type', 'target_id'], name='ai_user_beh_target__f26c22_idx'), models.Index(fields=['created_at'], name='ai_user_beh_created_3aa5d2_idx')],
            },
        ),
    ]
