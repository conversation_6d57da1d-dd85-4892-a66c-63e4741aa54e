{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
/* 折叠菜单样式 */
.module {
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.module h2 {
    background: #79aec8;
    color: white;
    margin: 0;
    padding: 10px 15px;
    cursor: pointer;
    position: relative;
    user-select: none;
    transition: background-color 0.3s ease;
}

.module h2:hover {
    background: #6ba6cd;
}

.module h2::after {
    content: '▼';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
}

.module.collapsed h2::after {
    transform: translateY(-50%) rotate(-90deg);
}

.module-content {
    max-height: 1000px;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: white;
}

.module.collapsed .module-content {
    max-height: 0;
}

.module table {
    width: 100%;
    margin: 0;
    border-collapse: collapse;
}

.module table th,
.module table td {
    padding: 8px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.module table th {
    background: #f8f9fa;
    font-weight: bold;
    color: #333;
}

.module table tr:hover {
    background: #f5f5f5;
}

.module table a {
    color: #447e9b;
    text-decoration: none;
}

.module table a:hover {
    color: #2e5266;
    text-decoration: underline;
}

/* 添加/修改按钮样式 */
.addlink, .changelink {
    display: inline-block;
    padding: 2px 8px;
    margin-left: 10px;
    background: #79aec8;
    color: white !important;
    text-decoration: none;
    border-radius: 3px;
    font-size: 11px;
}

.addlink:hover, .changelink:hover {
    background: #6ba6cd;
    text-decoration: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .module h2 {
        padding: 8px 12px;
        font-size: 14px;
    }
    
    .module table th,
    .module table td {
        padding: 6px 12px;
        font-size: 13px;
    }
}

/* AI功能模块特殊样式 */
.module.ai-module h2 {
    background: #28a745;
}

.module.ai-module h2:hover {
    background: #218838;
}

/* 微信模块特殊样式 */
.module.wechat-module h2 {
    background: #07c160;
}

.module.wechat-module h2:hover {
    background: #06ad56;
}

/* 分销模块特殊样式 */
.module.distribution-module h2 {
    background: #6f42c1;
}

.module.distribution-module h2:hover {
    background: #5a32a3;
}
</style>
{% endblock %}

{% block content %}
<h1>{% trans 'Site administration' %}</h1>

<div id="content-main">
    {% include "admin/app_list.html" %}
    {% if not app_list %}
        <p>{% trans "You don't have permission to edit anything." %}</p>
    {% endif %}
</div>



<!-- 添加快捷键提示 -->
<div style="position: fixed; bottom: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 10px; border-radius: 4px; font-size: 12px; z-index: 1000;">
    <div><strong>快捷键：</strong></div>
    <div>Alt+E - 展开第一个</div>
    <div>Alt+C - 折叠所有</div>
    <div>Alt+1~9 - 切换菜单</div>
    <div style="margin-top: 5px; font-size: 10px; opacity: 0.8;">手风琴模式：同时只能展开一个菜单</div>
</div>
{% endblock %}
