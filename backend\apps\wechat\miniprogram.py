"""
微信小程序模块
"""
import json
import time
import base64
import hashlib
import requests
from django.conf import settings
from django.contrib.auth import get_user_model
from .models import WechatUser, WechatConfig

# 临时处理加密库导入
try:
    from Crypto.Cipher import AES
    HAS_CRYPTO = True
except ImportError:
    # 如果没有安装pycryptodome，创建一个占位符
    HAS_CRYPTO = False
    class AES:
        MODE_CBC = 'CBC'
        @staticmethod
        def new(key, mode, iv):
            raise NotImplementedError("请安装pycryptodome: pip install pycryptodome")

User = get_user_model()


class WechatMiniprogram:
    """微信小程序API类"""
    
    def __init__(self, config_name='default'):
        # 从数据库获取配置
        config = WechatConfig.get_config('miniprogram', config_name)
        self.app_id = config.get('app_id', '')
        self.app_secret = config.get('app_secret', '')
        self.api_base = 'https://api.weixin.qq.com'
        self._access_token = None
        self._access_token_expires = 0
    
    def get_access_token(self):
        """获取access_token"""
        if self._access_token and time.time() < self._access_token_expires:
            return self._access_token
        
        url = f"{self.api_base}/cgi-bin/token"
        params = {
            'grant_type': 'client_credential',
            'appid': self.app_id,
            'secret': self.app_secret
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if 'errcode' in data:
            raise Exception(f"获取access_token失败: {data.get('errmsg', '未知错误')}")
        
        self._access_token = data['access_token']
        self._access_token_expires = time.time() + data['expires_in'] - 300  # 提前5分钟过期
        
        return self._access_token
    
    def code2session(self, js_code):
        """小程序登录凭证校验"""
        url = f"{self.api_base}/sns/jscode2session"
        params = {
            'appid': self.app_id,
            'secret': self.app_secret,
            'js_code': js_code,
            'grant_type': 'authorization_code'
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if 'errcode' in data:
            raise Exception(f"登录凭证校验失败: {data.get('errmsg', '未知错误')}")
        
        return data
    
    def decrypt_data(self, encrypted_data, iv, session_key):
        """解密小程序数据"""
        if not HAS_CRYPTO:
            raise Exception("缺少加密库，请安装: pip install pycryptodome")

        try:
            # Base64解码
            session_key = base64.b64decode(session_key)
            encrypted_data = base64.b64decode(encrypted_data)
            iv = base64.b64decode(iv)

            # AES解密
            cipher = AES.new(session_key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(encrypted_data)

            # 去除填充
            decrypted = self._unpad(decrypted)

            # 解析JSON
            result = json.loads(decrypted.decode('utf-8'))

            # 验证水印
            if result.get('watermark', {}).get('appid') != self.app_id:
                raise Exception("数据水印验证失败")

            return result

        except Exception as e:
            raise Exception(f"数据解密失败: {str(e)}")
    
    def _unpad(self, s):
        """去除PKCS7填充"""
        return s[:-ord(s[len(s)-1:])]
    
    def login_or_create_user(self, js_code, encrypted_data=None, iv=None):
        """小程序登录或创建用户"""
        # 获取session信息
        session_data = self.code2session(js_code)
        openid = session_data['openid']
        session_key = session_data['session_key']
        unionid = session_data.get('unionid')
        
        # 解密用户信息（如果提供）
        user_info = None
        if encrypted_data and iv:
            try:
                user_info = self.decrypt_data(encrypted_data, iv, session_key)
            except Exception as e:
                print(f"解密用户信息失败: {e}")
        
        # 查找或创建微信用户
        wechat_user, created = WechatUser.objects.get_or_create(
            openid=openid,
            defaults={
                'unionid': unionid,
                'nickname': user_info.get('nickName') if user_info else openid,
                'avatar_url': user_info.get('avatarUrl') if user_info else '',
                'gender': user_info.get('gender', 0) if user_info else 0,
                'city': user_info.get('city') if user_info else '',
                'province': user_info.get('province') if user_info else '',
                'country': user_info.get('country') if user_info else '',
                'language': user_info.get('language', 'zh_CN') if user_info else 'zh_CN',
            }
        )
        
        # 如果是新用户或没有关联系统用户，创建系统用户
        if created or not wechat_user.user:
            # 生成用户名
            username = f"mp_{openid[:10]}"
            counter = 1
            original_username = username
            while User.objects.filter(username=username).exists():
                username = f"{original_username}_{counter}"
                counter += 1
            
            # 创建系统用户
            user = User.objects.create_user(
                username=username,
                nickname=user_info.get('nickName') if user_info else '',
                wechat_openid=openid,
                wechat_unionid=unionid,
            )
            
            # 关联微信用户
            wechat_user.user = user
            wechat_user.save()
        else:
            # 更新用户信息
            user = wechat_user.user
            if user_info:
                if not user.nickname:
                    user.nickname = user_info.get('nickName')
                if not user.wechat_openid:
                    user.wechat_openid = openid
                if not user.wechat_unionid and unionid:
                    user.wechat_unionid = unionid
                user.save()
                
                # 更新微信用户信息
                wechat_user.nickname = user_info.get('nickName')
                wechat_user.avatar_url = user_info.get('avatarUrl')
                wechat_user.gender = user_info.get('gender', 0)
                wechat_user.city = user_info.get('city')
                wechat_user.province = user_info.get('province')
                wechat_user.country = user_info.get('country')
                wechat_user.save()
        
        return {
            'user': user,
            'wechat_user': wechat_user,
            'session_key': session_key,
            'openid': openid,
            'unionid': unionid
        }
    
    def decrypt_phone_number(self, encrypted_data, iv, session_key):
        """解密手机号"""
        phone_data = self.decrypt_data(encrypted_data, iv, session_key)
        return {
            'phone_number': phone_data.get('phoneNumber'),
            'pure_phone_number': phone_data.get('purePhoneNumber'),
            'country_code': phone_data.get('countryCode'),
        }
    
    def send_subscribe_message(self, openid, template_id, data, page=None):
        """发送订阅消息"""
        access_token = self.get_access_token()
        url = f"{self.api_base}/cgi-bin/message/subscribe/send"
        
        message_data = {
            'touser': openid,
            'template_id': template_id,
            'data': data
        }
        
        if page:
            message_data['page'] = page
        
        response = requests.post(
            url,
            json=message_data,
            params={'access_token': access_token}
        )
        result = response.json()
        
        if result.get('errcode') != 0:
            raise Exception(f"发送订阅消息失败: {result.get('errmsg', '未知错误')}")
        
        return result
    
    def get_unlimited_qrcode(self, scene, page=None, width=430):
        """获取小程序码（不限制数量）"""
        access_token = self.get_access_token()
        url = f"{self.api_base}/wxa/getwxacodeunlimit"
        
        data = {
            'scene': scene,
            'width': width
        }
        
        if page:
            data['page'] = page
        
        response = requests.post(
            url,
            json=data,
            params={'access_token': access_token}
        )
        
        # 检查是否返回错误信息
        content_type = response.headers.get('content-type', '')
        if 'application/json' in content_type:
            result = response.json()
            if result.get('errcode') != 0:
                raise Exception(f"获取小程序码失败: {result.get('errmsg', '未知错误')}")
        
        return response.content
    
    def get_qrcode(self, path, width=430):
        """获取小程序二维码（有限制）"""
        access_token = self.get_access_token()
        url = f"{self.api_base}/cgi-bin/wxaapp/createwxaqrcode"
        
        data = {
            'path': path,
            'width': width
        }
        
        response = requests.post(
            url,
            json=data,
            params={'access_token': access_token}
        )
        
        # 检查是否返回错误信息
        content_type = response.headers.get('content-type', '')
        if 'application/json' in content_type:
            result = response.json()
            if result.get('errcode') != 0:
                raise Exception(f"获取小程序二维码失败: {result.get('errmsg', '未知错误')}")
        
        return response.content
    
    def check_content_security(self, content):
        """内容安全检测"""
        access_token = self.get_access_token()
        url = f"{self.api_base}/wxa/msg_sec_check"
        
        data = {
            'content': content
        }
        
        response = requests.post(
            url,
            json=data,
            params={'access_token': access_token}
        )
        result = response.json()
        
        if result.get('errcode') != 0:
            if result.get('errcode') == 87014:
                return {'safe': False, 'reason': '内容含有违法违规内容'}
            else:
                raise Exception(f"内容安全检测失败: {result.get('errmsg', '未知错误')}")
        
        return {'safe': True}
    
    def check_image_security(self, media_content):
        """图片安全检测"""
        access_token = self.get_access_token()
        url = f"{self.api_base}/wxa/img_sec_check"
        
        files = {
            'media': ('image.jpg', media_content, 'image/jpeg')
        }
        
        response = requests.post(
            url,
            files=files,
            params={'access_token': access_token}
        )
        result = response.json()
        
        if result.get('errcode') != 0:
            if result.get('errcode') == 87014:
                return {'safe': False, 'reason': '图片含有违法违规内容'}
            else:
                raise Exception(f"图片安全检测失败: {result.get('errmsg', '未知错误')}")
        
        return {'safe': True}
