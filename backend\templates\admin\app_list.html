{% load i18n %}

{% if app_list %}
    <style>
    /* 左侧菜单折叠样式 */
    .app-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .app-list .app {
        margin-bottom: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
    }
    
    .app-list .app-title {
        display: block;
        background: #79aec8;
        color: white;
        padding: 10px 15px;
        text-decoration: none;
        font-weight: bold;
        cursor: pointer;
        position: relative;
        user-select: none;
        transition: background-color 0.3s ease;
    }
    
    .app-list .app-title:hover {
        background: #6ba6cd;
        color: white;
        text-decoration: none;
    }
    
    .app-list .app-title::after {
        content: '▼';
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        transition: transform 0.3s ease;
        font-size: 12px;
    }
    
    .app-list .app.collapsed .app-title::after {
        transform: translateY(-50%) rotate(-90deg);
    }
    
    .app-list .model-list {
        max-height: 500px;
        overflow: hidden;
        transition: max-height 0.3s ease;
        background: white;
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .app-list .app.collapsed .model-list {
        max-height: 0;
    }
    
    .app-list .model-list li {
        border-bottom: 1px solid #eee;
    }
    
    .app-list .model-list li:last-child {
        border-bottom: none;
    }
    
    .app-list .model-list a {
        display: block;
        padding: 8px 15px;
        color: #447e9b;
        text-decoration: none;
        transition: background-color 0.2s ease;
    }
    
    .app-list .model-list a:hover {
        background: #f5f5f5;
        color: #2e5266;
        text-decoration: none;
    }
    
    .app-list .model-list .addlink,
    .app-list .model-list .changelink {
        display: inline-block;
        margin-left: 10px;
        padding: 2px 6px;
        background: #79aec8;
        color: white;
        text-decoration: none;
        border-radius: 3px;
        font-size: 11px;
    }
    
    .app-list .model-list .addlink:hover,
    .app-list .model-list .changelink:hover {
        background: #6ba6cd;
        text-decoration: none;
    }
    
    /* AI模块特殊颜色 */
    .app-list .app.app-ai .app-title {
        background: #28a745;
    }
    
    .app-list .app.app-ai .app-title:hover {
        background: #218838;
    }
    
    /* 微信模块特殊颜色 */
    .app-list .app.app-wechat .app-title {
        background: #07c160;
    }
    
    .app-list .app.app-wechat .app-title:hover {
        background: #06ad56;
    }
    
    /* 分销模块特殊颜色 */
    .app-list .app.app-distribution .app-title {
        background: #6f42c1;
    }
    
    .app-list .app.app-distribution .app-title:hover {
        background: #5a32a3;
    }
    
    /* 产品模块特殊颜色 */
    .app-list .app.app-products .app-title {
        background: #fd7e14;
    }
    
    .app-list .app.app-products .app-title:hover {
        background: #e8690b;
    }
    
    /* 订单模块特殊颜色 */
    .app-list .app.app-orders .app-title {
        background: #dc3545;
    }
    
    .app-list .app.app-orders .app-title:hover {
        background: #c82333;
    }
    
    /* 用户模块特殊颜色 */
    .app-list .app.app-users .app-title {
        background: #17a2b8;
    }
    
    .app-list .app.app-users .app-title:hover {
        background: #138496;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .app-list .app-title {
            padding: 8px 12px;
            font-size: 14px;
        }
        
        .app-list .model-list a {
            padding: 6px 12px;
            font-size: 13px;
        }
    }
    </style>

    <div class="app-list">
        {% for app in app_list %}
            <div class="app app-{{ app.app_label }} collapsed" data-app="{{ app.app_label }}">
                <a href="#" class="app-title" onclick="toggleApp(event, this)">
                    {{ app.name }}
                </a>
                {% if app.models %}
                    <ul class="model-list">
                        {% for model in app.models %}
                            <li class="model-{{ model.object_name|lower }}">
                                {% if model.admin_url %}
                                    <a href="{{ model.admin_url }}">
                                        {{ model.name }}
                                        {% if model.add_url %}
                                            <span class="addlink" onclick="event.stopPropagation(); window.location.href='{{ model.add_url }}'">{% trans 'Add' %}</span>
                                        {% endif %}
                                    </a>
                                {% else %}
                                    <span>{{ model.name }}</span>
                                    {% if model.add_url %}
                                        <a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>
                                    {% endif %}
                                {% endif %}
                            </li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>
        {% endfor %}
    </div>

    <script>
    function toggleApp(event, element) {
        event.preventDefault();
        event.stopPropagation();
        
        const app = element.closest('.app');
        const appLabel = app.dataset.app;
        
        // 切换折叠状态
        app.classList.toggle('collapsed');
        
        // 保存状态到localStorage
        const isCollapsed = app.classList.contains('collapsed');
        localStorage.setItem('admin_app_' + appLabel, isCollapsed ? 'collapsed' : 'expanded');
    }
    
    // 页面加载时恢复状态
    document.addEventListener('DOMContentLoaded', function() {
        const apps = document.querySelectorAll('.app[data-app]');
        
        apps.forEach(function(app) {
            const appLabel = app.dataset.app;
            const savedState = localStorage.getItem('admin_app_' + appLabel);
            
            // 如果有保存的状态，使用保存的状态；否则保持默认的collapsed状态
            if (savedState === 'expanded') {
                app.classList.remove('collapsed');
            } else {
                app.classList.add('collapsed');
            }
        });
        
        // 添加全局快捷键
        document.addEventListener('keydown', function(e) {
            // Alt+E 展开所有应用
            if (e.altKey && e.key === 'e') {
                e.preventDefault();
                apps.forEach(function(app) {
                    app.classList.remove('collapsed');
                    const appLabel = app.dataset.app;
                    localStorage.setItem('admin_app_' + appLabel, 'expanded');
                });
            }
            
            // Alt+C 折叠所有应用
            if (e.altKey && e.key === 'c') {
                e.preventDefault();
                apps.forEach(function(app) {
                    app.classList.add('collapsed');
                    const appLabel = app.dataset.app;
                    localStorage.setItem('admin_app_' + appLabel, 'collapsed');
                });
            }
        });
    });
    </script>
{% endif %}
