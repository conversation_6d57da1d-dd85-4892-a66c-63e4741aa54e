#!/usr/bin/env python
"""
环境检查脚本
用于检查Django项目的依赖是否完整
"""

import sys
import subprocess
import pkg_resources

def check_dependencies():
    """检查依赖包"""
    # 从requirements.txt读取依赖
    requirements_file = "requirements.txt"
    
    try:
        with open(requirements_file, 'r', encoding='utf-8') as f:
            requirements = f.read().splitlines()
    except FileNotFoundError:
        print(f"错误: 找不到 {requirements_file} 文件")
        return False
    
    # 过滤掉注释和空行
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]
    
    print("正在检查依赖包...")
    missing_packages = []
    
    for requirement in requirements:
        try:
            # 处理带版本号的包名
            if '==' in requirement:
                package_name = requirement.split('==')[0]
            elif '>=' in requirement:
                package_name = requirement.split('>=')[0]
            elif '<=' in requirement:
                package_name = requirement.split('<=')[0]
            else:
                package_name = requirement
            
            pkg_resources.require(requirement)
            print(f"✓ {requirement}")
        except pkg_resources.DistributionNotFound:
            print(f"✗ 缺失: {requirement}")
            missing_packages.append(requirement)
        except pkg_resources.VersionConflict as e:
            print(f"✗ 版本冲突: {requirement} ({e})")
            missing_packages.append(requirement)
    
    if missing_packages:
        print(f"\n发现 {len(missing_packages)} 个缺失或版本不匹配的包:")
        for package in missing_packages:
            print(f"  - {package}")
        
        # 询问是否安装缺失的包
        response = input("\n是否自动安装缺失的包? (y/N): ")
        if response.lower() in ['y', 'yes']:
            install_missing_packages(missing_packages)
        else:
            print("请手动安装缺失的包")
            return False
    else:
        print("\n所有依赖包都已正确安装!")
    
    return True

def install_missing_packages(packages):
    """安装缺失的包"""
    print("正在安装缺失的包...")
    for package in packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✓ 成功安装 {package}")
        except subprocess.CalledProcessError:
            print(f"✗ 安装 {package} 失败")
            return False
    
    print("所有缺失的包都已成功安装!")
    return True

if __name__ == "__main__":
    check_dependencies()
