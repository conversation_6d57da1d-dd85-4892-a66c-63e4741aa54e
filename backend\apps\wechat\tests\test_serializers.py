"""
微信序列化器测试
"""
from django.test import TestCase
from rest_framework.exceptions import ValidationError
from apps.wechat.serializers import (
    WechatConfigSerializer, WechatPaymentOrderSerializer,
    WechatPaymentQuerySerializer, WechatPaymentRefundSerializer
)
from apps.wechat.models import WechatConfig


class WechatConfigSerializerTest(TestCase):
    """微信配置序列化器测试"""
    
    def test_official_config_validation_success(self):
        """测试公众号配置验证成功"""
        data = {
            'platform': 'official',
            'name': 'test',
            'app_id': 'test_app_id',
            'app_secret': 'test_app_secret',
            'token': 'test_token',
            'description': '测试公众号配置'
        }
        
        serializer = WechatConfigSerializer(data=data)
        self.assertTrue(serializer.is_valid())
    
    def test_official_config_validation_fail(self):
        """测试公众号配置验证失败"""
        data = {
            'platform': 'official',
            'name': 'test',
            'app_id': 'test_app_id',
            # 缺少 app_secret 和 token
            'description': '测试公众号配置'
        }
        
        serializer = WechatConfigSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('公众号配置缺少必要字段', str(serializer.errors))
    
    def test_miniprogram_config_validation_success(self):
        """测试小程序配置验证成功"""
        data = {
            'platform': 'miniprogram',
            'name': 'test',
            'app_id': 'test_app_id',
            'app_secret': 'test_app_secret',
            'description': '测试小程序配置'
        }
        
        serializer = WechatConfigSerializer(data=data)
        self.assertTrue(serializer.is_valid())
    
    def test_work_config_validation_success(self):
        """测试企业微信配置验证成功"""
        data = {
            'platform': 'work',
            'name': 'test',
            'corp_id': 'test_corp_id',
            'corp_secret': 'test_corp_secret',
            'agent_id': 'test_agent_id',
            'description': '测试企业微信配置'
        }
        
        serializer = WechatConfigSerializer(data=data)
        self.assertTrue(serializer.is_valid())
    
    def test_payment_config_validation_success(self):
        """测试微信支付配置验证成功"""
        data = {
            'platform': 'payment',
            'name': 'test',
            'app_id': 'test_app_id',
            'mch_id': 'test_mch_id',
            'mch_key': 'test_mch_key',
            'description': '测试微信支付配置'
        }
        
        serializer = WechatConfigSerializer(data=data)
        self.assertTrue(serializer.is_valid())
    
    def test_sensitive_fields_hidden(self):
        """测试敏感字段隐藏"""
        config = WechatConfig.objects.create(
            platform='official',
            name='test',
            app_id='test_app_id',
            app_secret='test_app_secret',
            token='test_token'
        )

        serializer = WechatConfigSerializer(config)
        data = serializer.data

        # 检查敏感字段是否被隐藏
        self.assertEqual(data['app_secret'], '***已配置***')
        self.assertEqual(data['token'], '***已配置***')
        self.assertEqual(data['app_id'], 'test_app_id')  # 非敏感字段正常显示


class WechatPaymentOrderSerializerTest(TestCase):
    """微信支付订单序列化器测试"""
    
    def test_jsapi_order_validation_success(self):
        """测试JSAPI支付订单验证成功"""
        data = {
            'out_trade_no': 'test_order_123',
            'total_fee': 100,
            'body': '测试商品',
            'trade_type': 'JSAPI',
            'openid': 'test_openid'
        }
        
        serializer = WechatPaymentOrderSerializer(data=data)
        self.assertTrue(serializer.is_valid())
    
    def test_jsapi_order_missing_openid(self):
        """测试JSAPI支付缺少openid"""
        data = {
            'out_trade_no': 'test_order_123',
            'total_fee': 100,
            'body': '测试商品',
            'trade_type': 'JSAPI'
            # 缺少 openid
        }
        
        serializer = WechatPaymentOrderSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('JSAPI支付必须提供用户openid', str(serializer.errors))
    
    def test_native_order_validation_success(self):
        """测试扫码支付订单验证成功"""
        data = {
            'out_trade_no': 'test_order_123',
            'total_fee': 100,
            'body': '测试商品',
            'trade_type': 'NATIVE'
        }
        
        serializer = WechatPaymentOrderSerializer(data=data)
        self.assertTrue(serializer.is_valid())
    
    def test_invalid_total_fee(self):
        """测试无效金额"""
        data = {
            'out_trade_no': 'test_order_123',
            'total_fee': 0,  # 无效金额
            'body': '测试商品',
            'trade_type': 'NATIVE'
        }
        
        serializer = WechatPaymentOrderSerializer(data=data)
        self.assertFalse(serializer.is_valid())


class WechatPaymentQuerySerializerTest(TestCase):
    """微信支付查询序列化器测试"""
    
    def test_query_with_out_trade_no(self):
        """测试使用商户订单号查询"""
        data = {'out_trade_no': 'test_order_123'}
        
        serializer = WechatPaymentQuerySerializer(data=data)
        self.assertTrue(serializer.is_valid())
    
    def test_query_with_transaction_id(self):
        """测试使用微信交易号查询"""
        data = {'transaction_id': 'wx_transaction_123'}
        
        serializer = WechatPaymentQuerySerializer(data=data)
        self.assertTrue(serializer.is_valid())
    
    def test_query_without_params(self):
        """测试查询缺少参数"""
        data = {}
        
        serializer = WechatPaymentQuerySerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('必须提供商户订单号或微信交易号', str(serializer.errors))


class WechatPaymentRefundSerializerTest(TestCase):
    """微信支付退款序列化器测试"""
    
    def test_refund_validation_success(self):
        """测试退款验证成功"""
        data = {
            'out_trade_no': 'test_order_123',
            'out_refund_no': 'test_refund_123',
            'total_fee': 100,
            'refund_fee': 50,
            'refund_desc': '用户申请退款'
        }
        
        serializer = WechatPaymentRefundSerializer(data=data)
        self.assertTrue(serializer.is_valid())
    
    def test_refund_fee_exceeds_total(self):
        """测试退款金额超过订单金额"""
        data = {
            'out_trade_no': 'test_order_123',
            'out_refund_no': 'test_refund_123',
            'total_fee': 100,
            'refund_fee': 150,  # 超过订单金额
        }
        
        serializer = WechatPaymentRefundSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('退款金额不能大于订单金额', str(serializer.errors))
    
    def test_refund_without_order_info(self):
        """测试退款缺少订单信息"""
        data = {
            'out_refund_no': 'test_refund_123',
            'total_fee': 100,
            'refund_fee': 50,
        }
        
        serializer = WechatPaymentRefundSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('必须提供商户订单号或微信交易号', str(serializer.errors))
