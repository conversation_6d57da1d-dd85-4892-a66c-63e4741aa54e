"""
微信应用序列化器
"""
from rest_framework import serializers
from .models import WechatUser, WechatMessage, WechatMenu, WechatTemplate, WechatTemplateMessage, WechatConfig


class WechatUserSerializer(serializers.ModelSerializer):
    """微信用户序列化器"""
    user_username = serializers.CharField(source='user.username', read_only=True)
    user_nickname = serializers.CharField(source='user.nickname', read_only=True)
    
    class Meta:
        model = WechatUser
        fields = [
            'id', 'user', 'user_username', 'user_nickname',
            'openid', 'unionid', 'nickname', 'avatar_url',
            'gender', 'city', 'province', 'country', 'language',
            'subscribe', 'subscribe_time', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'openid', 'created_at', 'updated_at']
    
    def get_gender_display(self, obj):
        """获取性别显示名称"""
        gender_map = {0: '未知', 1: '男', 2: '女'}
        return gender_map.get(obj.gender, '未知')


class WechatMessageSerializer(serializers.ModelSerializer):
    """微信消息序列化器"""
    wechat_user_nickname = serializers.CharField(source='wechat_user.nickname', read_only=True)
    msg_type_display = serializers.CharField(source='get_msg_type_display', read_only=True)
    direction_display = serializers.CharField(source='get_direction_display', read_only=True)
    
    class Meta:
        model = WechatMessage
        fields = [
            'id', 'wechat_user', 'wechat_user_nickname',
            'msg_id', 'msg_type', 'msg_type_display',
            'direction', 'direction_display', 'content',
            'media_id', 'pic_url', 'location_x', 'location_y',
            'scale', 'label', 'title', 'description', 'url',
            'event', 'event_key', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class WechatMenuSerializer(serializers.ModelSerializer):
    """微信菜单序列化器"""
    children = serializers.SerializerMethodField()
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    
    class Meta:
        model = WechatMenu
        fields = [
            'id', 'name', 'type', 'type_display', 'key', 'url',
            'media_id', 'parent', 'sort_order', 'is_active',
            'children', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_children(self, obj):
        """获取子菜单"""
        if obj.children.exists():
            return WechatMenuSerializer(
                obj.children.filter(is_active=True).order_by('sort_order'),
                many=True,
                context=self.context
            ).data
        return []
    
    def validate(self, data):
        """验证菜单数据"""
        menu_type = data.get('type')
        key = data.get('key')
        url = data.get('url')
        
        if menu_type == 'click' and not key:
            raise serializers.ValidationError("点击类型菜单必须设置key值")
        
        if menu_type == 'view' and not url:
            raise serializers.ValidationError("跳转类型菜单必须设置url")
        
        return data


class WechatTemplateSerializer(serializers.ModelSerializer):
    """微信模板消息序列化器"""
    
    class Meta:
        model = WechatTemplate
        fields = [
            'id', 'template_id', 'title', 'primary_industry',
            'deputy_industry', 'content', 'example', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_template_id(self, value):
        """验证模板ID格式"""
        if not value or len(value) < 10:
            raise serializers.ValidationError("模板ID格式不正确")
        return value


class WechatTemplateMessageSerializer(serializers.ModelSerializer):
    """微信模板消息记录序列化器"""
    wechat_user_nickname = serializers.CharField(source='wechat_user.nickname', read_only=True)
    template_title = serializers.CharField(source='template.title', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = WechatTemplateMessage
        fields = [
            'id', 'wechat_user', 'wechat_user_nickname',
            'template', 'template_title', 'data', 'url',
            'miniprogram', 'status', 'status_display',
            'msg_id', 'error_msg', 'sent_at', 'created_at'
        ]
        read_only_fields = ['id', 'msg_id', 'sent_at', 'created_at']


class WechatAuthSerializer(serializers.Serializer):
    """微信登录序列化器"""
    code = serializers.CharField(help_text="微信授权码")
    
    def validate_code(self, value):
        """验证授权码"""
        if not value:
            raise serializers.ValidationError("授权码不能为空")
        return value


class MiniprogramAuthSerializer(serializers.Serializer):
    """小程序登录序列化器"""
    code = serializers.CharField(help_text="小程序登录凭证")
    encrypted_data = serializers.CharField(required=False, help_text="加密数据")
    iv = serializers.CharField(required=False, help_text="初始向量")
    
    def validate_code(self, value):
        """验证登录凭证"""
        if not value:
            raise serializers.ValidationError("登录凭证不能为空")
        return value


class MiniprogramDecryptSerializer(serializers.Serializer):
    """小程序数据解密序列化器"""
    encrypted_data = serializers.CharField(help_text="加密数据")
    iv = serializers.CharField(help_text="初始向量")
    session_key = serializers.CharField(help_text="会话密钥")


class WechatPaymentSerializer(serializers.Serializer):
    """微信支付序列化器"""
    order_id = serializers.CharField(help_text="订单号")
    openid = serializers.CharField(help_text="用户OpenID")
    notify_url = serializers.URLField(help_text="支付回调地址")


class MessagePushSerializer(serializers.Serializer):
    """消息推送序列化器"""
    platform = serializers.ChoiceField(
        choices=[('official', '公众号'), ('work', '企业微信'), ('miniprogram', '小程序')],
        help_text="推送平台"
    )
    target = serializers.CharField(help_text="目标用户或群组")
    content = serializers.CharField(help_text="消息内容")
    msg_type = serializers.ChoiceField(
        choices=[('text', '文本'), ('textcard', '文本卡片'), ('news', '图文')],
        default='text',
        help_text="消息类型"
    )


class TemplatePushSerializer(serializers.Serializer):
    """模板消息推送序列化器"""
    openid = serializers.CharField(help_text="用户OpenID")
    template_id = serializers.CharField(help_text="模板ID")
    data = serializers.JSONField(help_text="模板数据")
    url = serializers.URLField(required=False, help_text="跳转链接")
    miniprogram = serializers.JSONField(required=False, help_text="小程序信息")


class WechatMenuCreateSerializer(serializers.Serializer):
    """微信菜单创建序列化器"""
    force_update = serializers.BooleanField(default=False, help_text="是否强制更新")


class WechatWorkSyncSerializer(serializers.Serializer):
    """企业微信同步序列化器"""
    sync_type = serializers.ChoiceField(
        choices=[('contacts', '通讯录'), ('departments', '部门')],
        default='contacts',
        help_text="同步类型"
    )


class WechatStatsSerializer(serializers.Serializer):
    """微信统计序列化器"""
    date_range = serializers.ChoiceField(
        choices=[('today', '今天'), ('week', '本周'), ('month', '本月'), ('year', '今年')],
        default='today',
        help_text="统计时间范围"
    )


class WechatConfigSerializer(serializers.ModelSerializer):
    """微信配置序列化器"""

    class Meta:
        model = WechatConfig
        fields = '__all__'

    def validate(self, attrs):
        """验证配置数据"""
        platform = attrs.get('platform')

        if platform == 'official':
            required_fields = ['app_id', 'app_secret', 'token']
            for field in required_fields:
                if not attrs.get(field):
                    raise serializers.ValidationError(f"公众号配置缺少必要字段: {field}")

        elif platform == 'miniprogram':
            required_fields = ['app_id', 'app_secret']
            for field in required_fields:
                if not attrs.get(field):
                    raise serializers.ValidationError(f"小程序配置缺少必要字段: {field}")

        elif platform == 'work':
            required_fields = ['corp_id', 'corp_secret']
            for field in required_fields:
                if not attrs.get(field):
                    raise serializers.ValidationError(f"企业微信配置缺少必要字段: {field}")

        elif platform == 'payment':
            required_fields = ['app_id', 'mch_id', 'mch_key']
            for field in required_fields:
                if not attrs.get(field):
                    raise serializers.ValidationError(f"微信支付配置缺少必要字段: {field}")

        return attrs

    def to_representation(self, instance):
        """自定义输出格式"""
        data = super().to_representation(instance)

        # 隐藏敏感信息
        sensitive_fields = ['app_secret', 'corp_secret', 'mch_key', 'token', 'encoding_aes_key']
        for field in sensitive_fields:
            if field in data and data.get(field):
                data[field] = '***已配置***'

        return data


class WechatPaymentOrderSerializer(serializers.Serializer):
    """微信支付订单序列化器"""
    out_trade_no = serializers.CharField(max_length=32, help_text="商户订单号")
    total_fee = serializers.IntegerField(min_value=1, help_text="订单金额(分)")
    body = serializers.CharField(max_length=128, help_text="商品描述")
    notify_url = serializers.URLField(required=False, help_text="通知地址")
    trade_type = serializers.ChoiceField(
        choices=[('JSAPI', 'JSAPI支付'), ('NATIVE', '扫码支付'), ('APP', 'APP支付')],
        default='JSAPI',
        help_text="交易类型"
    )
    openid = serializers.CharField(max_length=128, required=False, help_text="用户openid")

    def validate(self, attrs):
        """验证支付订单数据"""
        trade_type = attrs.get('trade_type')
        openid = attrs.get('openid')

        if trade_type == 'JSAPI' and not openid:
            raise serializers.ValidationError("JSAPI支付必须提供用户openid")

        return attrs


class WechatPaymentQuerySerializer(serializers.Serializer):
    """微信支付查询序列化器"""
    out_trade_no = serializers.CharField(max_length=32, required=False, help_text="商户订单号")
    transaction_id = serializers.CharField(max_length=32, required=False, help_text="微信交易号")

    def validate(self, attrs):
        """验证查询参数"""
        out_trade_no = attrs.get('out_trade_no')
        transaction_id = attrs.get('transaction_id')

        if not out_trade_no and not transaction_id:
            raise serializers.ValidationError("必须提供商户订单号或微信交易号")

        return attrs


class WechatPaymentRefundSerializer(serializers.Serializer):
    """微信支付退款序列化器"""
    out_trade_no = serializers.CharField(max_length=32, required=False, help_text="商户订单号")
    transaction_id = serializers.CharField(max_length=32, required=False, help_text="微信交易号")
    out_refund_no = serializers.CharField(max_length=64, help_text="商户退款单号")
    total_fee = serializers.IntegerField(min_value=1, help_text="订单金额(分)")
    refund_fee = serializers.IntegerField(min_value=1, help_text="退款金额(分)")
    refund_desc = serializers.CharField(max_length=80, required=False, help_text="退款原因")

    def validate(self, attrs):
        """验证退款数据"""
        out_trade_no = attrs.get('out_trade_no')
        transaction_id = attrs.get('transaction_id')
        total_fee = attrs.get('total_fee')
        refund_fee = attrs.get('refund_fee')

        if not out_trade_no and not transaction_id:
            raise serializers.ValidationError("必须提供商户订单号或微信交易号")

        if refund_fee > total_fee:
            raise serializers.ValidationError("退款金额不能大于订单金额")

        return attrs
