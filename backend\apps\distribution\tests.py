from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
from rest_framework.test import APITestCase
from rest_framework import status

from .models import (
    DistributionConfig, DistributionLevel, DistributionRelation,
    CommissionRecord, WithdrawalRecord, PromotionCode
)
from .utils import (
    generate_invitation_code, get_distribution_config,
    calculate_withdrawal_fee, check_level_upgrade
)

User = get_user_model()


class DistributionModelTest(TestCase):
    """分销模型测试"""

    def setUp(self):
        """测试数据准备"""
        # 创建用户
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )

        # 创建分销等级
        self.level1 = DistributionLevel.objects.create(
            name='普通分销商',
            level=1,
            commission_rate=Decimal('0.05'),
            min_sales=Decimal('0.00'),
            min_referrals=0,
            is_active=True
        )
        self.level2 = DistributionLevel.objects.create(
            name='银牌分销商',
            level=2,
            commission_rate=Decimal('0.08'),
            min_sales=Decimal('1000.00'),
            min_referrals=5,
            is_active=True
        )
        self.level3 = DistributionLevel.objects.create(
            name='金牌分销商',
            level=3,
            commission_rate=Decimal('0.12'),
            min_sales=Decimal('2000.00'),
            min_referrals=15,
            is_active=True
        )

    def test_distribution_relation_creation(self):
        """测试分销关系创建"""
        relation = DistributionRelation.objects.create(
            user=self.user1,
            level=self.level1,
            is_active=True,
            activated_at=timezone.now()
        )

        self.assertEqual(relation.user, self.user1)
        self.assertEqual(relation.level, self.level1)
        self.assertTrue(relation.is_active)
        self.assertIsNotNone(relation.invitation_code)
        self.assertEqual(len(relation.invitation_code), 8)

    def test_distribution_hierarchy(self):
        """测试分销层级关系"""
        # 创建上级分销商
        parent_relation = DistributionRelation.objects.create(
            user=self.user1,
            level=self.level1,
            is_active=True,
            activated_at=timezone.now()
        )

        # 创建下级分销商
        child_relation = DistributionRelation.objects.create(
            user=self.user2,
            parent=parent_relation,
            level=self.level1,
            is_active=True,
            activated_at=timezone.now()
        )

        self.assertEqual(child_relation.parent, parent_relation)
        self.assertIn(child_relation, parent_relation.children.all())
        self.assertEqual(parent_relation.get_children_count(), 1)

    def test_invitation_code_uniqueness(self):
        """测试邀请码唯一性"""
        codes = set()
        for i in range(100):
            code = generate_invitation_code()
            self.assertNotIn(code, codes)
            codes.add(code)

    def test_level_upgrade_check(self):
        """测试等级升级检查"""
        # 直接创建一个relation，不通过save触发信号
        relation = DistributionRelation(
            user=self.user1,
            level=self.level1,
            is_active=True,
            activated_at=timezone.now(),
            total_sales=Decimal('1500.00'),
            referral_count=10
        )
        # 手动设置ID以模拟已存在的记录
        relation.id = 1

        # 检查升级（应该升级到level2）
        result = check_level_upgrade(relation)
        self.assertTrue(result['upgraded'])
        self.assertEqual(result['new_level'], self.level2)


class DistributionConfigTest(TestCase):
    """分销配置测试"""

    def setUp(self):
        """测试数据准备"""
        DistributionConfig.objects.create(
            name='测试配置',
            key='test_config',
            value='test_value',
            is_active=True
        )

    def test_get_config(self):
        """测试获取配置"""
        value = get_distribution_config('test_config')
        self.assertEqual(value, 'test_value')

        # 测试不存在的配置
        value = get_distribution_config('non_existent', 'default')
        self.assertEqual(value, 'default')


class WithdrawalTest(TestCase):
    """提现测试"""

    def test_withdrawal_fee_calculation(self):
        """测试提现手续费计算"""
        # 创建配置
        DistributionConfig.objects.create(
            key='withdrawal_fee_rate',
            value='0.02',  # 2%
            is_active=True
        )

        amount = Decimal('100.00')
        fee = calculate_withdrawal_fee(amount)
        self.assertEqual(fee, Decimal('2.00'))


class DistributionAPITest(APITestCase):
    """分销API测试"""

    def setUp(self):
        """测试数据准备"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.level = DistributionLevel.objects.create(
            name='普通分销商',
            level=1,
            commission_rate=Decimal('0.05'),
            min_sales=Decimal('0.00'),
            min_referrals=0,
            is_active=True
        )

        # 创建分销配置，启用分销系统
        self.config = DistributionConfig.objects.create(
            key='distribution_enabled',
            value='true',
            description='分销系统开关'
        )

    def test_join_distribution(self):
        """测试加入分销系统"""
        self.client.force_authenticate(user=self.user)

        response = self.client.post('/api/v1/distribution/join/', {})
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # 验证分销关系已创建
        self.assertTrue(
            DistributionRelation.objects.filter(user=self.user).exists()
        )

    def test_join_distribution_with_invitation(self):
        """测试通过邀请码加入分销系统"""
        # 创建推荐人
        parent_user = User.objects.create_user(
            username='parent_user',
            email='<EMAIL>',
            password='testpass123'
        )
        # 手动创建推荐人的分销关系
        parent_relation = DistributionRelation.objects.create(
            user=parent_user,
            level=self.level,
            is_active=True,
            activated_at=timezone.now()
        )

        # 创建新用户用于测试
        child_user = User.objects.create_user(
            username='child_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=child_user)

        response = self.client.post('/api/v1/distribution/join/', {
            'invitation_code': parent_relation.invitation_code
        })
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # 验证分销关系已创建且有上级
        relation = DistributionRelation.objects.get(user=child_user)
        self.assertEqual(relation.parent, parent_relation)

    def test_get_my_team(self):
        """测试获取我的团队"""
        # 创建分销关系
        relation = DistributionRelation.objects.create(
            user=self.user,
            level=self.level,
            is_active=True,
            activated_at=timezone.now()
        )

        self.client.force_authenticate(user=self.user)

        response = self.client.get('/api/v1/distribution/my-team/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('stats', response.data)
        self.assertIn('direct_children', response.data)
